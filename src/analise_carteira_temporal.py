#!/usr/bin/env python3
"""
Análise Temporal da Carteira de Investimentos

Este script gera análise temporal da carteira com:
- Gráficos temporais individuais para cada ação
- Gráfico temporal do rendimento total da carteira
- Evolução do valor investido vs valor atual ao longo do tempo

Baseado na estrutura do analise_carteira_simples.py mas com foco temporal.
"""

import pandas as pd
import numpy as np
import yfinance as yf
import matplotlib.pyplot as plt
import seaborn as sns
import os
import sys
from datetime import datetime, timedelta
import warnings

warnings.filterwarnings('ignore')

# Adicionar o diretório src ao path para importar config
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from config_loader import ConfigLoader

# Configurar estilo dos gráficos
plt.style.use('default')
sns.set_palette("husl")

def carregar_dividendos():
    """
    Carrega os dividendos do arquivo dividendos.csv

    Returns:
        DataFrame com os dividendos ou DataFrame vazio se arquivo não existir
    """
    try:
        dividendos_df = pd.read_csv('dividendos.csv')
        dividendos_df['data'] = pd.to_datetime(dividendos_df['data']).dt.date
        print(f"✓ Dividendos carregados: {len(dividendos_df)} registros")
        return dividendos_df
    except FileNotFoundError:
        print("⚠️  Arquivo dividendos.csv não encontrado. Continuando sem dividendos.")
        return pd.DataFrame(columns=['ticker', 'data', 'valor'])
    except Exception as e:
        print(f"⚠️  Erro ao carregar dividendos.csv: {e}. Continuando sem dividendos.")
        return pd.DataFrame(columns=['ticker', 'data', 'valor'])

def calcular_dividendos_ate_data(dividendos_df, data_atual):
    """
    Calcula o total de dividendos recebidos até uma data específica

    Args:
        dividendos_df: DataFrame com os dividendos
        data_atual: Data limite para cálculo

    Returns:
        float: Total de dividendos recebidos até a data
    """
    if dividendos_df.empty:
        return 0.0

    # Converter data_atual para date se for datetime
    if hasattr(data_atual, 'date'):
        data_atual = data_atual.date()

    dividendos_ate_data = dividendos_df[dividendos_df['data'] <= data_atual]
    return dividendos_ate_data['valor'].sum()

def corrigir_valores_tims3(dados, ticker):
    """Aplica correção específica para TIMS3 até 02/07/2025"""
    if ticker != 'TIMS3.SA':
        return dados

    # Data limite para correção (timezone-naive)
    data_limite = pd.to_datetime('2025-07-02').tz_localize(None)
    colunas_preco = ['Open', 'High', 'Low', 'Close', 'Adj Close']

    # Verificar se o índice é datetime
    if not isinstance(dados.index, pd.DatetimeIndex):
        return dados

    # Garantir que o índice seja timezone-naive
    dados_index = dados.index
    if dados_index.tz is not None:
        dados_index = dados_index.tz_convert(None)
        dados.index = dados_index

    # Filtrar dados até a data limite
    mask_correcao = dados_index <= data_limite

    if mask_correcao.any():
        print(f"     🔧 Corrigindo valores TIMS3 até 02/07/2025 (dividindo por 100)")

        # Aplicar correção nas colunas de preço
        for coluna in colunas_preco:
            if coluna in dados.columns:
                dados.loc[mask_correcao, coluna] = dados.loc[mask_correcao, coluna] / 100

        print(f"     ✅ Correção aplicada em {mask_correcao.sum()} registros")

    return dados

def carregar_carteira(arquivo_csv):
    """Carrega dados da carteira do arquivo CSV"""
    carteira = pd.read_csv(arquivo_csv)
    carteira['data_compra'] = pd.to_datetime(carteira['data_compra'])
    return carteira

def obter_dados_historicos(tickers, data_inicio):
    """Obtém dados históricos das ações desde a data de início"""
    print("Obtendo dados históricos...")
    dados_historicos = {}
    
    # Calcular data final (hoje + margem)
    data_fim = datetime.now() + timedelta(days=1)
    
    for ticker in tickers:
        try:
            print(f"  Baixando dados para {ticker}...")
            stock = yf.Ticker(ticker)
            hist = stock.history(start=data_inicio, end=data_fim)
            
            if not hist.empty:
                # Aplicar correção para TIMS3 se necessário
                hist = corrigir_valores_tims3(hist, ticker)
                dados_historicos[ticker] = hist
                print(f"  ✓ {len(hist)} registros obtidos para {ticker}")
            else:
                print(f"  ✗ Nenhum dado obtido para {ticker}")
                
        except Exception as e:
            print(f"  ✗ Erro ao obter dados para {ticker}: {e}")
    
    return dados_historicos

def processar_transacoes_temporais(carteira):
    """Processa transações da carteira organizadas temporalmente"""
    # Ordenar por data
    carteira_ordenada = carteira.sort_values('data_compra').copy()
    
    # Criar estrutura para acompanhar posições ao longo do tempo
    transacoes = []
    
    for _, transacao in carteira_ordenada.iterrows():
        ticker = transacao['ticker']
        quantidade = transacao['quantidade']
        data = transacao['data_compra']
        preco = transacao['preco_compra']
        
        transacoes.append({
            'ticker': ticker,
            'data': data,
            'quantidade': quantidade,
            'preco': preco,
            'valor_transacao': quantidade * preco,
            'tipo': 'COMPRA' if quantidade > 0 else 'VENDA'
        })
    
    return pd.DataFrame(transacoes)

def obter_precos_atuais_para_temporal(tickers):
    """Obtém preços atuais das ações para o último dia da análise temporal"""
    print("  Obtendo preços atuais para o último dia...")
    precos = {}

    for ticker in tickers:
        try:
            stock = yf.Ticker(ticker)
            # Primeiro tenta obter dados do dia atual
            hist = stock.history(period='1d')

            if not hist.empty:
                # Aplicar correção para TIMS3 se necessário
                hist = corrigir_valores_tims3(hist, ticker)
                precos[ticker] = hist['Close'].iloc[-1]
            else:
                # Se não há dados do dia atual, busca dados dos últimos 5 dias
                hist = stock.history(period='5d')
                if not hist.empty:
                    hist = corrigir_valores_tims3(hist, ticker)
                    precos[ticker] = hist['Close'].iloc[-1]
        except Exception as e:
            print(f"    ⚠️ Erro ao obter preço atual para {ticker}: {e}")

    return precos

def calcular_posicoes_temporais(transacoes_df, dados_historicos):
    """Calcula evolução das posições ao longo do tempo, incluindo dividendos"""
    # Carregar dividendos
    dividendos_df = carregar_dividendos()

    # Obter todas as datas únicas dos dados históricos
    todas_datas = set()
    for ticker, dados in dados_historicos.items():
        # Converter índice para timezone-naive se necessário
        if dados.index.tz is not None:
            dados.index = dados.index.tz_convert(None)
        todas_datas.update(dados.index.date)

    todas_datas = sorted(todas_datas)

    # Converter para datetime timezone-naive
    todas_datas = [pd.to_datetime(data).tz_localize(None) for data in todas_datas]

    # Filtrar apenas datas a partir da primeira transação
    data_inicio = transacoes_df['data'].min()
    if data_inicio.tz is not None:
        data_inicio = data_inicio.tz_localize(None)
    todas_datas = [data for data in todas_datas if data >= data_inicio]

    # Para o último dia, obter preços atuais mais precisos
    data_mais_recente = max(todas_datas)
    tickers_ativos = transacoes_df['ticker'].unique()
    precos_atuais_precisos = obter_precos_atuais_para_temporal(tickers_ativos)

    # Estrutura para armazenar evolução temporal
    evolucao_temporal = []
    
    # Para cada data, calcular posições e valores
    for data_atual in todas_datas:
        # Filtrar transações até a data atual
        transacoes_ate_data = transacoes_df[transacoes_df['data'] <= data_atual]
        
        if transacoes_ate_data.empty:
            continue
        
        # Calcular posições consolidadas até esta data
        posicoes_data = {}
        valor_total_investido = 0
        valor_total_atual = 0
        
        # Consolidar posições por ticker
        for ticker in transacoes_ate_data['ticker'].unique():
            transacoes_ticker = transacoes_ate_data[transacoes_ate_data['ticker'] == ticker]
            
            quantidade_total = transacoes_ticker['quantidade'].sum()
            valor_investido_bruto = transacoes_ticker[transacoes_ticker['quantidade'] > 0]['valor_transacao'].sum()
            valor_vendido = abs(transacoes_ticker[transacoes_ticker['quantidade'] < 0]['valor_transacao'].sum())
            valor_investido_liquido = valor_investido_bruto - valor_vendido
            
            # Obter preço atual para esta data
            preco_atual = None

            # Se é o último dia e temos preços atuais mais precisos, usar eles
            if data_atual == data_mais_recente and ticker in precos_atuais_precisos:
                preco_atual = precos_atuais_precisos[ticker]
            elif ticker in dados_historicos:
                dados_ticker = dados_historicos[ticker].copy()
                # Garantir que o índice seja timezone-naive
                if dados_ticker.index.tz is not None:
                    dados_ticker.index = dados_ticker.index.tz_convert(None)

                # Garantir que data_atual seja timezone-naive
                data_atual_naive = data_atual
                if hasattr(data_atual, 'tz') and data_atual.tz is not None:
                    data_atual_naive = data_atual.tz_localize(None)

                # Encontrar preço mais próximo da data atual
                datas_disponiveis = dados_ticker.index[dados_ticker.index <= data_atual_naive]
                if not datas_disponiveis.empty:
                    data_mais_proxima = datas_disponiveis.max()
                    preco_atual = dados_ticker.loc[data_mais_proxima, 'Close']
            
            if preco_atual is not None and quantidade_total > 0:
                valor_atual_ticker = quantidade_total * preco_atual
                
                posicoes_data[ticker] = {
                    'quantidade': quantidade_total,
                    'valor_investido_bruto': valor_investido_bruto,
                    'valor_investido_liquido': valor_investido_liquido,
                    'valor_atual': valor_atual_ticker,
                    'preco_atual': preco_atual
                }
                
                valor_total_investido += valor_investido_liquido
                valor_total_atual += valor_atual_ticker
        
        # Calcular totais seguindo EXATAMENTE a lógica da análise simples
        # Valor total investido bruto (soma de TODAS as compras, incluindo as vendidas)
        valor_total_investido_bruto = 0
        valor_total_vendido = 0

        for ticker in transacoes_ate_data['ticker'].unique():
            transacoes_ticker = transacoes_ate_data[transacoes_ate_data['ticker'] == ticker]

            # Somar TODAS as compras (quantidade > 0)
            compras_ticker = transacoes_ticker[transacoes_ticker['quantidade'] > 0]['valor_transacao'].sum()
            valor_total_investido_bruto += compras_ticker

            # Somar TODAS as vendas (quantidade < 0)
            vendas_ticker = transacoes_ticker[transacoes_ticker['quantidade'] < 0]['valor_transacao'].sum()
            valor_total_vendido += abs(vendas_ticker)

        # Calcular dividendos recebidos até esta data
        dividendos_recebidos = calcular_dividendos_ate_data(dividendos_df, data_atual)

        # Calcular valor_total_recuperavel (valor atual das ações + vendas + dividendos)
        valor_total_recuperavel = valor_total_atual + valor_total_vendido + dividendos_recebidos

        # Calcular capital seguindo EXATAMENTE a lógica da análise simples, mas incluindo dividendos
        config_loader = ConfigLoader()
        capital_inicial = config_loader.get_initial_capital('portfolio_analysis')
        capital_disponivel = capital_inicial - valor_total_investido_bruto + valor_total_vendido + dividendos_recebidos
        capital_total = capital_disponivel + valor_total_recuperavel - valor_total_vendido - dividendos_recebidos

        # Rendimento do capital (equivalente ao gráfico de barras da análise simples)
        rendimento_capital_absoluto = capital_total - capital_inicial
        rendimento_capital_percentual = (rendimento_capital_absoluto / capital_inicial) * 100

        evolucao_temporal.append({
            'data': data_atual,
            'valor_investido_liquido': valor_total_investido,  # Valor líquido investido
            'valor_investido_bruto': valor_total_investido_bruto,  # Valor bruto investido
            'valor_atual_total': valor_total_atual,  # Valor atual das ações
            'valor_vendido_total': valor_total_vendido,  # Valor total das vendas
            'dividendos_recebidos': dividendos_recebidos,  # Total de dividendos recebidos até a data
            'valor_total_recuperavel': valor_total_recuperavel,  # Valor atual + vendas + dividendos
            'capital_inicial': capital_inicial,
            'capital_disponivel': capital_disponivel,
            'capital_total': capital_total,
            'rendimento_capital_absoluto': rendimento_capital_absoluto,
            'rendimento_capital_percentual': rendimento_capital_percentual,
            'posicoes': posicoes_data.copy()
        })
    
    return evolucao_temporal

def gerar_graficos_temporais_individuais(evolucao_temporal, dados_historicos):
    """Gera gráficos temporais individuais para cada ação"""
    print("Gerando gráficos temporais individuais...")
    
    # Obter todos os tickers únicos
    todos_tickers = set()
    for snapshot in evolucao_temporal:
        todos_tickers.update(snapshot['posicoes'].keys())
    
    # Criar diretório para gráficos individuais
    os.makedirs('results/figures/temporal_individual', exist_ok=True)
    
    for ticker in todos_tickers:
        # Coletar dados temporais para este ticker
        datas = []
        valores_investidos = []
        valores_atuais = []
        quantidades = []
        precos = []
        
        for snapshot in evolucao_temporal:
            if ticker in snapshot['posicoes']:
                posicao = snapshot['posicoes'][ticker]
                datas.append(snapshot['data'])
                valores_investidos.append(posicao['valor_investido_liquido'])
                valores_atuais.append(posicao['valor_atual'])
                quantidades.append(posicao['quantidade'])
                precos.append(posicao['preco_atual'])
        
        if not datas:
            continue
        
        # Criar gráfico para este ticker
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
        
        # Gráfico 1: Evolução do valor investido vs atual
        ax1.plot(datas, valores_investidos, label='Valor Investido', linewidth=2, color='blue')
        ax1.plot(datas, valores_atuais, label='Valor Atual', linewidth=2, color='green')
        ax1.fill_between(datas, valores_investidos, valores_atuais, alpha=0.3, 
                        color='green' if valores_atuais[-1] > valores_investidos[-1] else 'red')
        
        ax1.set_title(f'{ticker.replace(".SA", "")} - Evolução do Investimento\n'
                     f'Quantidade: {quantidades[-1]} ações', fontsize=14, fontweight='bold')
        ax1.set_ylabel('Valor (R$)', fontsize=12)
        ax1.legend(loc='upper left')
        ax1.grid(True, alpha=0.3)
        ax1.tick_params(axis='x', rotation=45)
        
        # Gráfico 2: Evolução do preço da ação
        if ticker in dados_historicos:
            dados_ticker = dados_historicos[ticker]
            # Filtrar dados para o período relevante
            dados_periodo = dados_ticker[dados_ticker.index >= datas[0]]
            
            ax2.plot(dados_periodo.index, dados_periodo['Close'], 
                    label='Preço de Fechamento', linewidth=1.5, color='orange')
            
            # Marcar pontos de compra/venda
            # Aqui você pode adicionar marcadores para transações específicas
            
        ax2.set_title(f'Evolução do Preço da Ação', fontsize=12)
        ax2.set_xlabel('Data', fontsize=12)
        ax2.set_ylabel('Preço (R$)', fontsize=12)
        ax2.legend(loc='upper left')
        ax2.grid(True, alpha=0.3)
        ax2.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        
        # Salvar gráfico
        nome_arquivo = f'temporal_{ticker.replace(".SA", "")}.png'
        caminho_arquivo = f'results/figures/temporal_individual/{nome_arquivo}'
        plt.savefig(caminho_arquivo, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"  ✓ Gráfico salvo: {caminho_arquivo}")

def gerar_grafico_carteira_total(evolucao_temporal):
    """Gera gráfico da evolução temporal da carteira total"""
    print("Gerando gráfico da carteira total...")

    # Extrair dados temporais seguindo a lógica da análise simples
    datas = [snapshot['data'] for snapshot in evolucao_temporal]
    capital_inicial = [snapshot['capital_inicial'] for snapshot in evolucao_temporal]
    capital_disponivel = [snapshot['capital_disponivel'] for snapshot in evolucao_temporal]
    valor_atual_acoes = [snapshot['valor_atual_total'] for snapshot in evolucao_temporal]
    dividendos_recebidos = [snapshot['dividendos_recebidos'] for snapshot in evolucao_temporal]
    capital_total = [snapshot['capital_total'] for snapshot in evolucao_temporal]
    rendimento_capital_pct = [snapshot['rendimento_capital_percentual'] for snapshot in evolucao_temporal]
    
    # Criar gráfico
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10))

    # Gráfico 1: Evolução do Capital Total (equivalente ao gráfico de barras da análise simples)
    ax1.axhline(y=capital_inicial[0], color='black', linestyle='--', alpha=0.7,
                label=f'Capital Inicial (R$ {capital_inicial[0]:.2f})')

    ax1.plot(datas, capital_disponivel, label='Capital Disponível',
             linewidth=2.5, color='blue', marker='o', markersize=3)
    ax1.plot(datas, valor_atual_acoes, label='Valor Atual das Ações',
             linewidth=2.5, color='orange', marker='s', markersize=3)

    # Adicionar linha dos dividendos se houver dividendos
    if max(dividendos_recebidos) > 0:
        ax1.plot(datas, dividendos_recebidos, label='Dividendos Recebidos',
                 linewidth=2, color='purple', marker='^', markersize=3)

    ax1.plot(datas, capital_total, label='Capital Total',
             linewidth=3, color='green', marker='D', markersize=4)

    # Preencher área entre capital inicial e capital total
    ax1.fill_between(datas, capital_inicial, capital_total, alpha=0.3,
                    color='green' if capital_total[-1] > capital_inicial[0] else 'red')

    ax1.set_title('Evolução Temporal do Capital Total da Carteira\n(Equivalente ao Gráfico de Barras da Análise Simples)',
                 fontsize=16, fontweight='bold')
    ax1.set_ylabel('Valor (R$)', fontsize=12)
    ax1.legend(loc='upper left', fontsize=11)
    ax1.grid(True, alpha=0.3)
    ax1.tick_params(axis='x', rotation=45)

    # Adicionar anotações de valores finais
    capital_final = capital_total[-1]
    capital_disponivel_final = capital_disponivel[-1]
    valor_acoes_final = valor_atual_acoes[-1]

    ax1.annotate(f'Capital Total: R$ {capital_final:.2f}',
                xy=(datas[-1], capital_final),
                xytext=(10, 10), textcoords='offset points',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='green', alpha=0.7),
                color='white', fontweight='bold')

    ax1.annotate(f'Disponível: R$ {capital_disponivel_final:.2f}',
                xy=(datas[-1], capital_disponivel_final),
                xytext=(10, -20), textcoords='offset points',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='blue', alpha=0.7),
                color='white', fontweight='bold')
    
    # Gráfico 2: Evolução do rendimento percentual do capital
    ax2.plot(datas, rendimento_capital_pct, linewidth=2.5, color='purple', marker='D', markersize=4)
    ax2.axhline(y=0, color='black', linestyle='--', alpha=0.5)
    ax2.fill_between(datas, 0, rendimento_capital_pct, alpha=0.3,
                    color='green' if rendimento_capital_pct[-1] >= 0 else 'red')

    ax2.set_title('Evolução do Rendimento Percentual do Capital', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Data', fontsize=12)
    ax2.set_ylabel('Rendimento do Capital (%)', fontsize=12)
    ax2.grid(True, alpha=0.3)
    ax2.tick_params(axis='x', rotation=45)

    # Adicionar anotação do rendimento final
    rendimento_final_pct = rendimento_capital_pct[-1]
    ax2.annotate(f'{rendimento_final_pct:.2f}%',
                xy=(datas[-1], rendimento_final_pct),
                xytext=(10, 10), textcoords='offset points',
                bbox=dict(boxstyle='round,pad=0.3',
                         facecolor='green' if rendimento_final_pct >= 0 else 'red', alpha=0.7),
                color='white', fontweight='bold')
    
    plt.tight_layout()
    
    # Salvar gráfico
    caminho_arquivo = 'results/figures/evolucao_carteira_temporal.png'
    plt.savefig(caminho_arquivo, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"  ✓ Gráfico da carteira total salvo: {caminho_arquivo}")
    
    # Imprimir resumo final seguindo a lógica da análise simples
    print(f"\n{'='*60}")
    print(f"RESUMO FINAL DA ANÁLISE TEMPORAL")
    print(f"{'='*60}")
    print(f"Período analisado: {datas[0].strftime('%d/%m/%Y')} a {datas[-1].strftime('%d/%m/%Y')}")
    print(f"Capital Inicial: R$ {capital_inicial[0]:.2f}")
    print(f"Capital Disponível: R$ {capital_disponivel_final:.2f}")
    print(f"Valor Atual das Ações: R$ {valor_acoes_final:.2f}")
    print(f"Capital Total: R$ {capital_final:.2f}")

    rendimento_capital_absoluto_final = capital_final - capital_inicial[0]
    print(f"Rendimento do Capital: R$ {rendimento_capital_absoluto_final:.2f} ({rendimento_final_pct:.2f}%)")

    status = "📈 LUCRO" if rendimento_capital_absoluto_final >= 0 else "📉 PREJUÍZO"
    print(f"Status: {status}")

    print(f"\n💡 NOTA: Valores equivalentes ao gráfico de barras da análise simples")

def main():
    """Função principal"""
    arquivo_carteira = 'carteira.csv'
    
    print("="*60)
    print("ANÁLISE TEMPORAL DA CARTEIRA DE INVESTIMENTOS")
    print("="*60)
    
    # Carregar carteira
    print("1. Carregando dados da carteira...")
    carteira = carregar_carteira(arquivo_carteira)
    if carteira is None or carteira.empty:
        print("❌ Erro: Não foi possível carregar a carteira")
        return
    
    print(f"   ✓ Carteira carregada: {len(carteira)} transações")
    
    # Processar transações temporalmente
    print("2. Processando transações temporais...")
    transacoes_df = processar_transacoes_temporais(carteira)
    print(f"   ✓ {len(transacoes_df)} transações processadas")
    
    # Obter dados históricos
    print("3. Obtendo dados históricos...")
    tickers = carteira['ticker'].unique()
    data_inicio = carteira['data_compra'].min() - timedelta(days=1)
    dados_historicos = obter_dados_historicos(tickers, data_inicio)
    
    if not dados_historicos:
        print("❌ Erro: Nenhum dado histórico foi obtido")
        return
    
    print(f"   ✓ Dados históricos obtidos para {len(dados_historicos)} tickers")
    
    # Calcular evolução temporal das posições
    print("4. Calculando evolução temporal das posições...")
    evolucao_temporal = calcular_posicoes_temporais(transacoes_df, dados_historicos)
    print(f"   ✓ Evolução calculada para {len(evolucao_temporal)} períodos")
    
    # Criar diretório para resultados
    os.makedirs('results/figures', exist_ok=True)
    
    # Gerar gráficos individuais
    print("5. Gerando gráficos temporais individuais...")
    gerar_graficos_temporais_individuais(evolucao_temporal, dados_historicos)
    
    # Gerar gráfico da carteira total
    print("6. Gerando gráfico da carteira total...")
    gerar_grafico_carteira_total(evolucao_temporal)
    
    # Salvar dados da evolução temporal
    print("7. Salvando dados da evolução temporal...")
    os.makedirs('results', exist_ok=True)
    
    # Converter evolução temporal para DataFrame
    dados_evolucao = []
    for snapshot in evolucao_temporal:
        dados_evolucao.append({
            'data': snapshot['data'],
            'capital_inicial': snapshot['capital_inicial'],
            'capital_disponivel': snapshot['capital_disponivel'],
            'valor_atual_acoes': snapshot['valor_atual_total'],
            'dividendos_recebidos': snapshot['dividendos_recebidos'],
            'valor_total_recuperavel': snapshot['valor_total_recuperavel'],
            'capital_total': snapshot['capital_total'],
            'valor_investido_bruto': snapshot['valor_investido_bruto'],
            'valor_vendido_total': snapshot['valor_vendido_total'],
            'rendimento_capital_absoluto': snapshot['rendimento_capital_absoluto'],
            'rendimento_capital_percentual': snapshot['rendimento_capital_percentual']
        })
    
    df_evolucao = pd.DataFrame(dados_evolucao)
    df_evolucao.to_csv('results/evolucao_carteira_temporal.csv', index=False)
    print(f"   ✓ Dados salvos em 'results/evolucao_carteira_temporal.csv'")
    
    print("\n✅ Análise temporal concluída com sucesso!")
    print(f"📁 Gráficos salvos em: results/figures/")
    print(f"📊 Gráficos individuais em: results/figures/temporal_individual/")

def criar_dashboard():
    """Cria dashboard interativo com Streamlit no estilo trader profissional"""
    import streamlit as st
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots

    # Configuração da página com tema escuro profissional
    st.set_page_config(
        page_title="Trading Dashboard - Portfolio Analysis",
        page_icon="📊",
        layout="wide",
        initial_sidebar_state="expanded"
    )

    # CSS customizado para estilo trader profissional
    st.markdown("""
    <style>
    /* Tema escuro principal */
    .stApp {
        background-color: #0e1117;
        color: #fafafa;
    }

    /* Sidebar escura */
    .css-1d391kg {
        background-color: #1e2329;
    }

    /* Métricas estilo trading */
    [data-testid="metric-container"] {
        background-color: #1e2329;
        border: 1px solid #2d3748;
        padding: 1rem;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    /* Títulos com estilo profissional */
    .trading-header {
        background: linear-gradient(90deg, #f7931e 0%, #ac9c3d 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-weight: bold;
        font-size: 2.5rem;
        text-align: center;
        margin-bottom: 2rem;
    }

    .section-header {
        color: #f7931e;
        border-bottom: 2px solid #f7931e;
        padding-bottom: 0.5rem;
        margin-bottom: 1rem;
    }

    /* Cards de informação */
    .info-card {
        background-color: #1e2329;
        border: 1px solid #2d3748;
        border-radius: 8px;
        padding: 1rem;
        margin: 0.5rem 0;
    }

    /* Tabelas com estilo escuro */
    .stDataFrame {
        background-color: #1e2329;
    }

    /* Botões customizados */
    .stButton > button {
        background-color: #f7931e;
        color: #000;
        border: none;
        border-radius: 4px;
        font-weight: bold;
    }

    .stButton > button:hover {
        background-color: #e8851b;
    }

    /* Selectbox e inputs */
    .stSelectbox > div > div {
        background-color: #2d3748;
        color: #fafafa;
    }

    /* Tabs customizadas */
    .stTabs [data-baseweb="tab-list"] {
        background-color: #1e2329;
    }

    .stTabs [data-baseweb="tab"] {
        background-color: #2d3748;
        color: #fafafa;
    }

    .stTabs [aria-selected="true"] {
        background-color: #f7931e !important;
        color: #000 !important;
    }
    </style>
    """, unsafe_allow_html=True)

    # Título principal estilo trader
    st.markdown('<h1 class="trading-header">📊 TRADING DASHBOARD</h1>', unsafe_allow_html=True)
    st.markdown('<h3 style="text-align: center; color: #8b949e; margin-bottom: 2rem;">Portfolio Performance Analysis</h3>', unsafe_allow_html=True)

    # Sidebar para controles com estilo trader
    st.sidebar.markdown('<h2 style="color: #f7931e;">⚙️ TRADING CONTROLS</h2>', unsafe_allow_html=True)

    # Verificar se existem dados
    arquivo_evolucao = 'results/evolucao_carteira_temporal.csv'
    arquivo_carteira = 'carteira.csv'

    if not os.path.exists(arquivo_evolucao):
        st.error("❌ Dados da evolução temporal não encontrados. Execute primeiro a análise temporal.")
        if st.button("🔄 Executar Análise Temporal"):
            with st.spinner("Executando análise temporal..."):
                main()
            st.rerun()
        return

    # Carregar dados
    @st.cache_data
    def carregar_dados():
        df_evolucao = pd.read_csv(arquivo_evolucao)
        df_evolucao['data'] = pd.to_datetime(df_evolucao['data'])

        carteira = pd.read_csv(arquivo_carteira)
        carteira['data_compra'] = pd.to_datetime(carteira['data_compra'])

        return df_evolucao, carteira

    try:
        df_evolucao, carteira = carregar_dados()
    except Exception as e:
        st.error(f"❌ Erro ao carregar dados: {e}")
        return

    # Filtros na sidebar com estilo trader
    st.sidebar.markdown('<h3 style="color: #f7931e;">📅 TIME FRAME</h3>', unsafe_allow_html=True)

    # Botões de período rápido
    col1, col2 = st.sidebar.columns(2)
    with col1:
        if st.button("7D", key="7d"):
            data_inicio = df_evolucao['data'].max().date() - pd.Timedelta(days=7)
        if st.button("1M", key="1m"):
            data_inicio = df_evolucao['data'].max().date() - pd.Timedelta(days=30)
    with col2:
        if st.button("3M", key="3m"):
            data_inicio = df_evolucao['data'].max().date() - pd.Timedelta(days=90)
        if st.button("ALL", key="all"):
            data_inicio = df_evolucao['data'].min().date()

    # Seletores de data customizados
    data_inicio = st.sidebar.date_input(
        "📅 Start Date",
        value=df_evolucao['data'].min().date(),
        min_value=df_evolucao['data'].min().date(),
        max_value=df_evolucao['data'].max().date()
    )

    data_fim = st.sidebar.date_input(
        "📅 End Date",
        value=df_evolucao['data'].max().date(),
        min_value=df_evolucao['data'].min().date(),
        max_value=df_evolucao['data'].max().date()
    )

    # Filtrar dados por data
    mask_data = (df_evolucao['data'].dt.date >= data_inicio) & (df_evolucao['data'].dt.date <= data_fim)
    df_filtrado = df_evolucao[mask_data].copy()

    if df_filtrado.empty:
        st.warning("⚠️ Nenhum dado encontrado para o período selecionado.")
        return

    # Métricas principais estilo trading
    st.markdown('<h2 class="section-header">📊 PORTFOLIO OVERVIEW</h2>', unsafe_allow_html=True)

    # Calcular métricas
    capital_inicial = df_filtrado['capital_inicial'].iloc[0]
    capital_final = df_filtrado['capital_total'].iloc[-1]
    rendimento_abs = capital_final - capital_inicial
    rendimento_pct = df_filtrado['rendimento_capital_percentual'].iloc[-1]
    valor_acoes = df_filtrado['valor_atual_acoes'].iloc[-1]
    capital_disponivel = df_filtrado['capital_disponivel'].iloc[-1]
    dividendos_total = df_filtrado['dividendos_recebidos'].iloc[-1] if 'dividendos_recebidos' in df_filtrado.columns else 0

    # Layout de métricas estilo trader
    col1, col2, col3, col4, col5, col6 = st.columns(6)

    with col1:
        delta_color = "normal"
        st.metric(
            label="💰 INITIAL CAPITAL",
            value=f"R$ {capital_inicial:,.2f}",
            delta=None
        )

    with col2:
        delta_color = "normal" if rendimento_abs >= 0 else "inverse"
        st.metric(
            label="💼 TOTAL EQUITY",
            value=f"R$ {capital_final:,.2f}",
            delta=f"R$ {rendimento_abs:,.2f}",
            delta_color=delta_color
        )

    with col3:
        delta_color = "normal" if rendimento_pct >= 0 else "inverse"
        status_emoji = "🟢" if rendimento_pct >= 0 else "🔴"
        st.metric(
            label=f"{status_emoji} P&L (%)",
            value=f"{rendimento_pct:.2f}%",
            delta=f"{rendimento_pct:.2f}%",
            delta_color=delta_color
        )

    with col4:
        st.metric(
            label="💵 CASH AVAILABLE",
            value=f"R$ {capital_disponivel:,.2f}",
            delta=f"{(capital_disponivel/capital_inicial)*100:.1f}% of capital"
        )

    with col5:
        st.metric(
            label="📈 POSITIONS VALUE",
            value=f"R$ {valor_acoes:,.2f}",
            delta=f"{(valor_acoes/capital_inicial)*100:.1f}% allocated"
        )

    with col6:
        st.metric(
            label="💎 DIVIDENDS",
            value=f"R$ {dividendos_total:,.2f}",
            delta=f"{(dividendos_total/capital_inicial)*100:.1f}% yield" if dividendos_total > 0 else "No dividends"
        )

    # Seção de Estimativas XGBoost
    st.markdown('<h2 class="section-header">🤖 XGBOOST TRADING SIGNALS</h2>', unsafe_allow_html=True)

    # Botão para gerar estimativas
    col1, col2, col3 = st.columns([1, 2, 1])
    with col2:
        if st.button("🚀 Generate XGBoost Trading Signals", key="xgboost_signals", type="primary"):
            with st.spinner("🤖 Running XGBoost analysis... This may take a few minutes..."):
                try:
                    # Executar o script de estimativas XGBoost
                    import subprocess
                    import sys

                    # Executar o script estimador_xgboost_sinais.py
                    result = subprocess.run([
                        sys.executable, 'src/estimador_xgboost_sinais.py'
                    ], capture_output=True, text=True, cwd=os.getcwd())

                    if result.returncode == 0:
                        st.success("✅ XGBoost analysis completed successfully!")

                        # Carregar e exibir os resultados
                        try:
                            # Carregar sinais de compra
                            compra_file = 'results/csv/xgboost_estimador_analysis/sinais_compra_estimador.csv'
                            venda_file = 'results/csv/xgboost_estimador_analysis/sinais_venda_estimador.csv'

                            if os.path.exists(compra_file):
                                df_compra = pd.read_csv(compra_file)
                                if not df_compra.empty:
                                    st.markdown('<h3 style="color: #00d4aa;">📈 BUY SIGNALS</h3>', unsafe_allow_html=True)

                                    # Formatar dados para exibição
                                    df_compra_display = df_compra.copy()
                                    df_compra_display['preco_atual'] = df_compra_display['preco_atual'].apply(lambda x: f"R$ {x:.2f}")
                                    df_compra_display['valor_estimado'] = df_compra_display['valor_estimado'].apply(lambda x: f"R$ {x:.2f}")
                                    df_compra_display['pct_change_estimado'] = df_compra_display['pct_change_estimado'].apply(lambda x: f"{x:.2f}%")
                                    df_compra_display['volume'] = df_compra_display['volume'].apply(lambda x: f"{x:,.0f}")
                                    df_compra_display['volatilidade'] = df_compra_display['volatilidade'].apply(lambda x: f"{x:.2f}%")

                                    # Renomear colunas para exibição
                                    df_compra_display = df_compra_display.rename(columns={
                                        'ticker': 'TICKER',
                                        'data': 'DATE',
                                        'preco_atual': 'CURRENT PRICE',
                                        'valor_estimado': 'ESTIMATED VALUE',
                                        'pct_change_estimado': 'EXPECTED CHANGE',
                                        'volume': 'VOLUME',
                                        'volatilidade': 'VOLATILITY'
                                    })

                                    st.dataframe(df_compra_display, use_container_width=True)
                                else:
                                    st.info("📊 No buy signals found for today.")

                            if os.path.exists(venda_file):
                                df_venda = pd.read_csv(venda_file)
                                if not df_venda.empty:
                                    st.markdown('<h3 style="color: #ff6b6b;">📉 SELL SIGNALS</h3>', unsafe_allow_html=True)

                                    # Formatar dados para exibição
                                    df_venda_display = df_venda.copy()
                                    df_venda_display['preco_atual'] = df_venda_display['preco_atual'].apply(lambda x: f"R$ {x:.2f}")
                                    df_venda_display['valor_estimado'] = df_venda_display['valor_estimado'].apply(lambda x: f"R$ {x:.2f}")
                                    df_venda_display['pct_change_estimado'] = df_venda_display['pct_change_estimado'].apply(lambda x: f"{x:.2f}%")
                                    df_venda_display['volume'] = df_venda_display['volume'].apply(lambda x: f"{x:,.0f}")
                                    df_venda_display['volatilidade'] = df_venda_display['volatilidade'].apply(lambda x: f"{x:.2f}%")

                                    # Renomear colunas para exibição
                                    df_venda_display = df_venda_display.rename(columns={
                                        'ticker': 'TICKER',
                                        'data': 'DATE',
                                        'preco_atual': 'CURRENT PRICE',
                                        'valor_estimado': 'ESTIMATED VALUE',
                                        'pct_change_estimado': 'EXPECTED CHANGE',
                                        'volume': 'VOLUME',
                                        'volatilidade': 'VOLATILITY'
                                    })

                                    st.dataframe(df_venda_display, use_container_width=True)
                                else:
                                    st.info("📊 No sell signals found for today.")

                        except Exception as e:
                            st.error(f"❌ Error loading XGBoost results: {e}")

                    else:
                        st.error(f"❌ Error running XGBoost analysis: {result.stderr}")

                except Exception as e:
                    st.error(f"❌ Error executing XGBoost analysis: {e}")

    # Verificar se já existem resultados salvos
    compra_file = 'results/csv/xgboost_estimador_analysis/sinais_compra_estimador.csv'
    venda_file = 'results/csv/xgboost_estimador_analysis/sinais_venda_estimador.csv'

    if os.path.exists(compra_file) or os.path.exists(venda_file):
        st.markdown('<h3 style="color: #f7931e;">📊 Latest XGBoost Signals</h3>', unsafe_allow_html=True)

        col1, col2 = st.columns(2)

        with col1:
            if os.path.exists(compra_file):
                try:
                    df_compra = pd.read_csv(compra_file)
                    if not df_compra.empty:
                        st.markdown('<h4 style="color: #00d4aa;">📈 Buy Signals</h4>', unsafe_allow_html=True)
                        st.metric("🎯 Buy Opportunities", len(df_compra))

                        # Mostrar top 3 sinais de compra
                        top_compra = df_compra.head(3)[['ticker', 'pct_change_estimado']].copy()
                        top_compra['pct_change_estimado'] = top_compra['pct_change_estimado'].apply(lambda x: f"{x:.2f}%")
                        st.dataframe(top_compra, hide_index=True, use_container_width=True)
                    else:
                        st.info("No buy signals available")
                except Exception as e:
                    st.error(f"Error loading buy signals: {e}")

        with col2:
            if os.path.exists(venda_file):
                try:
                    df_venda = pd.read_csv(venda_file)
                    if not df_venda.empty:
                        st.markdown('<h4 style="color: #ff6b6b;">📉 Sell Signals</h4>', unsafe_allow_html=True)
                        st.metric("⚠️ Sell Alerts", len(df_venda))

                        # Mostrar top 3 sinais de venda
                        top_venda = df_venda.head(3)[['ticker', 'pct_change_estimado']].copy()
                        top_venda['pct_change_estimado'] = top_venda['pct_change_estimado'].apply(lambda x: f"{x:.2f}%")
                        st.dataframe(top_venda, hide_index=True, use_container_width=True)
                    else:
                        st.info("No sell signals available")
                except Exception as e:
                    st.error(f"Error loading sell signals: {e}")

    # Gráfico principal estilo trading
    st.markdown('<h2 class="section-header">📈 EQUITY CURVE & PERFORMANCE</h2>', unsafe_allow_html=True)

    # Configuração do tema escuro para gráficos
    template_dark = {
        'layout': {
            'paper_bgcolor': '#0e1117',
            'plot_bgcolor': '#1e2329',
            'font': {'color': '#fafafa'},
            'colorway': ['#00d4aa', '#ff6b6b', '#4ecdc4', '#45b7d1', '#f7931e', '#c44569'],
            'xaxis': {
                'gridcolor': '#2d3748',
                'linecolor': '#2d3748',
                'tickcolor': '#2d3748'
            },
            'yaxis': {
                'gridcolor': '#2d3748',
                'linecolor': '#2d3748',
                'tickcolor': '#2d3748'
            }
        }
    }

    fig_capital = make_subplots(
        rows=2, cols=1,
        subplot_titles=('<b>EQUITY CURVE</b>', '<b>P&L PERCENTAGE</b>'),
        vertical_spacing=0.12,
        row_heights=[0.7, 0.3]
    )

    # Gráfico 1: Equity Curve estilo trader
    fig_capital.add_trace(
        go.Scatter(
            x=df_filtrado['data'],
            y=df_filtrado['capital_inicial'],
            mode='lines',
            name='Initial Capital',
            line=dict(color='#8b949e', dash='dash', width=1),
            hovertemplate='<b>Initial Capital</b><br>%{x}<br>R$ %{y:,.2f}<extra></extra>'
        ),
        row=1, col=1
    )

    fig_capital.add_trace(
        go.Scatter(
            x=df_filtrado['data'],
            y=df_filtrado['capital_disponivel'],
            mode='lines',
            name='Cash Available',
            line=dict(color='#4ecdc4', width=2),
            hovertemplate='<b>Cash Available</b><br>%{x}<br>R$ %{y:,.2f}<extra></extra>'
        ),
        row=1, col=1
    )

    fig_capital.add_trace(
        go.Scatter(
            x=df_filtrado['data'],
            y=df_filtrado['valor_atual_acoes'],
            mode='lines',
            name='Positions Value',
            line=dict(color='#f7931e', width=2),
            hovertemplate='<b>Positions Value</b><br>%{x}<br>R$ %{y:,.2f}<extra></extra>'
        ),
        row=1, col=1
    )

    # Adicionar linha dos dividendos se houver dados
    if 'dividendos_recebidos' in df_filtrado.columns and df_filtrado['dividendos_recebidos'].max() > 0:
        fig_capital.add_trace(
            go.Scatter(
                x=df_filtrado['data'],
                y=df_filtrado['dividendos_recebidos'],
                mode='lines',
                name='Dividends Received',
                line=dict(color='#c44569', width=2),
                hovertemplate='<b>Dividends Received</b><br>%{x}<br>R$ %{y:,.2f}<extra></extra>'
            ),
            row=1, col=1
        )

    # Linha principal - Total Equity com destaque
    color_equity = '#00d4aa' if capital_final >= capital_inicial else '#ff6b6b'
    fig_capital.add_trace(
        go.Scatter(
            x=df_filtrado['data'],
            y=df_filtrado['capital_total'],
            mode='lines',
            name='Total Equity',
            line=dict(color=color_equity, width=4),
            hovertemplate='<b>Total Equity</b><br>%{x}<br>R$ %{y:,.2f}<extra></extra>'
        ),
        row=1, col=1
    )

    # Área preenchida entre capital inicial e total (estilo trader)
    fill_color = 'rgba(0, 212, 170, 0.1)' if capital_final >= capital_inicial else 'rgba(255, 107, 107, 0.1)'
    fig_capital.add_trace(
        go.Scatter(
            x=df_filtrado['data'].tolist() + df_filtrado['data'].tolist()[::-1],
            y=df_filtrado['capital_inicial'].tolist() + df_filtrado['capital_total'].tolist()[::-1],
            fill='toself',
            fillcolor=fill_color,
            line=dict(color='rgba(255,255,255,0)'),
            showlegend=False,
            hoverinfo='skip'
        ),
        row=1, col=1
    )

    # Gráfico 2: P&L Percentage estilo trader
    pnl_color = '#00d4aa' if df_filtrado['rendimento_capital_percentual'].iloc[-1] >= 0 else '#ff6b6b'
    fig_capital.add_trace(
        go.Scatter(
            x=df_filtrado['data'],
            y=df_filtrado['rendimento_capital_percentual'],
            mode='lines',
            name='P&L %',
            line=dict(color=pnl_color, width=3),
            hovertemplate='<b>P&L Percentage</b><br>%{x}<br>%{y:.2f}%<extra></extra>'
        ),
        row=2, col=1
    )

    # Linha zero no gráfico de P&L
    fig_capital.add_hline(y=0, line_dash="solid", line_color="#8b949e", line_width=1, opacity=0.7, row=2, col=1)

    # Área preenchida no P&L
    pnl_fill_color = 'rgba(0, 212, 170, 0.2)' if df_filtrado['rendimento_capital_percentual'].iloc[-1] >= 0 else 'rgba(255, 107, 107, 0.2)'
    fig_capital.add_trace(
        go.Scatter(
            x=df_filtrado['data'],
            y=df_filtrado['rendimento_capital_percentual'],
            fill='tozeroy',
            fillcolor=pnl_fill_color,
            line=dict(color='rgba(255,255,255,0)'),
            showlegend=False,
            hoverinfo='skip'
        ),
        row=2, col=1
    )

    # Layout estilo trader profissional
    fig_capital.update_layout(
        height=700,
        paper_bgcolor='#0e1117',
        plot_bgcolor='#1e2329',
        font=dict(color='#fafafa', family="Arial, sans-serif"),
        showlegend=True,
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="center",
            x=0.5,
            bgcolor='rgba(30, 35, 41, 0.8)',
            bordercolor='#2d3748',
            borderwidth=1
        ),
        hovermode='x unified',
        margin=dict(l=50, r=50, t=80, b=50)
    )

    # Atualizar eixos com estilo escuro
    fig_capital.update_xaxes(
        title_text="<b>TIME</b>",
        row=2, col=1,
        gridcolor='#2d3748',
        linecolor='#2d3748',
        tickcolor='#2d3748'
    )
    fig_capital.update_yaxes(
        title_text="<b>VALUE (R$)</b>",
        row=1, col=1,
        gridcolor='#2d3748',
        linecolor='#2d3748',
        tickcolor='#2d3748'
    )
    fig_capital.update_yaxes(
        title_text="<b>P&L (%)</b>",
        row=2, col=1,
        gridcolor='#2d3748',
        linecolor='#2d3748',
        tickcolor='#2d3748'
    )

    st.plotly_chart(fig_capital, use_container_width=True)

    # Análise de transações estilo trader
    st.markdown('<h2 class="section-header">💼 TRADING ACTIVITY</h2>', unsafe_allow_html=True)

    col1, col2 = st.columns([1, 1])

    with col1:
        st.markdown('<h3 style="color: #f7931e;">📋 TRADE SUMMARY</h3>', unsafe_allow_html=True)

        # Agrupar transações por tipo
        compras = carteira[carteira['quantidade'] > 0]
        vendas = carteira[carteira['quantidade'] < 0]

        total_compras = len(compras)
        total_vendas = len(vendas)
        valor_total_compras = (compras['quantidade'] * compras['preco_compra']).sum()
        valor_total_vendas = abs((vendas['quantidade'] * vendas['preco_compra']).sum())

        # Métricas de trading
        col_a, col_b = st.columns(2)
        with col_a:
            st.metric("🟢 BUY ORDERS", total_compras, f"R$ {valor_total_compras:,.2f}")
        with col_b:
            st.metric("� SELL ORDERS", total_vendas, f"R$ {valor_total_vendas:,.2f}")

        # Win rate simulado
        win_rate = (rendimento_pct + 50) if rendimento_pct > -50 else 0  # Simulação baseada no rendimento
        st.metric("📊 WIN RATE", f"{max(0, min(100, win_rate)):.1f}%")

        # Top holdings
        st.markdown('<h4 style="color: #f7931e;">🏆 TOP HOLDINGS</h4>', unsafe_allow_html=True)
        acoes_negociadas = carteira.groupby('ticker').agg({
            'quantidade': 'sum',
            'preco_compra': 'mean'
        }).round(2)
        acoes_negociadas = acoes_negociadas[acoes_negociadas['quantidade'] > 0].sort_values('quantidade', ascending=False)
        acoes_negociadas.index = acoes_negociadas.index.str.replace('.SA', '')
        acoes_negociadas.columns = ['QTY', 'AVG PRICE']
        st.dataframe(acoes_negociadas.head(8), use_container_width=True)

    with col2:
        st.markdown('<h3 style="color: #f7931e;">📊 POSITION SUMMARY</h3>', unsafe_allow_html=True)

        # Resumo das posições atuais em formato de tabela
        posicoes_atuais = carteira.groupby('ticker')['quantidade'].sum()
        posicoes_atuais = posicoes_atuais[posicoes_atuais > 0]

        if not posicoes_atuais.empty:
            # Mostrar apenas informações básicas das posições
            posicoes_info = []
            for ticker in posicoes_atuais.index:
                quantidade = posicoes_atuais[ticker]
                posicoes_info.append({
                    'SYMBOL': ticker.replace('.SA', ''),
                    'QUANTITY': int(quantidade)
                })

            # Ordenar por quantidade decrescente
            posicoes_info.sort(key=lambda x: x['QUANTITY'], reverse=True)

            # Criar DataFrame para exibição
            df_posicoes = pd.DataFrame(posicoes_info)

            st.dataframe(df_posicoes, use_container_width=True, hide_index=True)

            # Métricas adicionais
            total_positions = len(posicoes_info)
            total_shares = sum([p['QUANTITY'] for p in posicoes_info])

            col_a, col_b = st.columns(2)
            with col_a:
                st.metric("📊 POSITIONS", total_positions)
            with col_b:
                st.metric("📈 TOTAL SHARES", total_shares)
        else:
            st.info("⚠️ No active positions found.")

    # Timeline de transações estilo trader
    st.markdown('<h2 class="section-header">⏰ TRADING TIMELINE</h2>', unsafe_allow_html=True)

    # Preparar dados para timeline
    timeline_data = carteira.copy()
    timeline_data['tipo'] = timeline_data['quantidade'].apply(lambda x: 'BUY' if x > 0 else 'SELL')
    timeline_data['valor_transacao'] = timeline_data['quantidade'] * timeline_data['preco_compra']
    timeline_data['ticker_clean'] = timeline_data['ticker'].str.replace('.SA', '')

    fig_timeline = px.scatter(
        timeline_data,
        x='data_compra',
        y='ticker_clean',
        size=abs(timeline_data['valor_transacao']),
        color='tipo',
        hover_data=['quantidade', 'preco_compra', 'valor_transacao'],
        title="<b>TRADE EXECUTION TIMELINE</b>",
        color_discrete_map={'BUY': '#00d4aa', 'SELL': '#ff6b6b'}
    )

    fig_timeline.update_layout(
        height=450,
        paper_bgcolor='#0e1117',
        plot_bgcolor='#1e2329',
        font=dict(color='#fafafa'),
        xaxis_title="<b>TRADE DATE</b>",
        yaxis_title="<b>SYMBOL</b>",
        showlegend=True,
        legend=dict(
            bgcolor='rgba(30, 35, 41, 0.8)',
            bordercolor='#2d3748',
            borderwidth=1
        ),
        xaxis=dict(
            gridcolor='#2d3748',
            linecolor='#2d3748',
            tickcolor='#2d3748'
        ),
        yaxis=dict(
            gridcolor='#2d3748',
            linecolor='#2d3748',
            tickcolor='#2d3748'
        )
    )

    fig_timeline.update_traces(
        marker=dict(
            line=dict(color='#2d3748', width=1),
            opacity=0.8
        )
    )

    st.plotly_chart(fig_timeline, use_container_width=True)

    # Seção de análise individual de ações
    st.markdown('<h2 class="section-header">📊 INDIVIDUAL STOCK ANALYSIS</h2>', unsafe_allow_html=True)

    # Obter dados históricos para análise individual
    @st.cache_data
    def obter_dados_historicos_dashboard():
        tickers = carteira['ticker'].unique()
        data_inicio_hist = carteira['data_compra'].min() - pd.Timedelta(days=1)
        return obter_dados_historicos(tickers, data_inicio_hist)

    dados_historicos = obter_dados_historicos_dashboard()

    # Obter lista de ações na carteira
    acoes_carteira = carteira['ticker'].unique()
    acoes_carteira_clean = [ticker.replace('.SA', '') for ticker in acoes_carteira]

    # Seletor de ação
    col1, col2 = st.columns([1, 3])

    with col1:
        acao_selecionada = st.selectbox(
            "🎯 SELECT SYMBOL",
            options=acoes_carteira_clean,
            index=0
        )

        # Converter de volta para formato com .SA
        ticker_completo = f"{acao_selecionada}.SA"

        # Informações da ação selecionada
        transacoes_acao = carteira[carteira['ticker'] == ticker_completo]
        quantidade_total = transacoes_acao['quantidade'].sum()

        # Calcular preço médio apenas das ações que estão na carteira atual
        if quantidade_total > 0:
            # Ordenar transações por data (mais recentes primeiro)
            transacoes_ordenadas = transacoes_acao.sort_values('data_compra', ascending=False)

            quantidade_restante = quantidade_total
            valor_total_carteira = 0

            for _, transacao in transacoes_ordenadas.iterrows():
                if quantidade_restante <= 0:
                    break

                if transacao['quantidade'] > 0:  # É uma compra
                    qtd_a_considerar = min(transacao['quantidade'], quantidade_restante)
                    valor_total_carteira += qtd_a_considerar * transacao['preco_compra']
                    quantidade_restante -= qtd_a_considerar

            preco_medio = valor_total_carteira / quantidade_total
        else:
            preco_medio = 0

        st.markdown(f'<div class="info-card">', unsafe_allow_html=True)
        st.markdown(f'<h4 style="color: #f7931e;">📈 {acao_selecionada}</h4>', unsafe_allow_html=True)
        st.metric("POSITION", f"{quantidade_total} shares")
        st.metric("AVG PRICE", f"R$ {preco_medio:.2f}")

        # Calcular P&L da ação se houver dados históricos
        if ticker_completo in dados_historicos and quantidade_total > 0:
            dados_acao = dados_historicos[ticker_completo]
            preco_atual = dados_acao['Close'].iloc[-1]
            pnl_acao = (preco_atual - preco_medio) * quantidade_total
            pnl_pct_acao = ((preco_atual - preco_medio) / preco_medio) * 100

            delta_color = "normal" if pnl_acao >= 0 else "inverse"
            st.metric(
                "CURRENT PRICE",
                f"R$ {preco_atual:.2f}",
                delta=f"R$ {pnl_acao:.2f} ({pnl_pct_acao:.1f}%)",
                delta_color=delta_color
            )

        st.markdown('</div>', unsafe_allow_html=True)

    with col2:
        # Gráfico da evolução individual da ação
        if ticker_completo in dados_historicos:
            dados_acao = dados_historicos[ticker_completo]

            # Filtrar dados por período selecionado
            dados_acao_filtrado = dados_acao[
                (dados_acao.index.date >= data_inicio) &
                (dados_acao.index.date <= data_fim)
            ]

            if not dados_acao_filtrado.empty:
                fig_individual = make_subplots(
                    rows=2, cols=1,
                    subplot_titles=(f'<b>{acao_selecionada} - PRICE EVOLUTION</b>', '<b>VOLUME</b>'),
                    vertical_spacing=0.15,
                    row_heights=[0.7, 0.3]
                )

                # Gráfico de preço
                fig_individual.add_trace(
                    go.Scatter(
                        x=dados_acao_filtrado.index,
                        y=dados_acao_filtrado['Close'],
                        mode='lines',
                        name='Close Price',
                        line=dict(color='#00d4aa', width=2),
                        hovertemplate='<b>Close Price</b><br>%{x}<br>R$ %{y:.2f}<extra></extra>'
                    ),
                    row=1, col=1
                )

                # Linha do preço médio de compra
                fig_individual.add_hline(
                    y=preco_medio,
                    line_dash="dash",
                    line_color="#f7931e",
                    annotation_text=f"Avg Buy: R$ {preco_medio:.2f}",
                    annotation_position="top right",
                    row=1, col=1
                )

                # Marcar pontos de transação
                for _, transacao in transacoes_acao.iterrows():
                    data_transacao = transacao['data_compra']
                    preco_transacao = transacao['preco_compra']
                    quantidade_transacao = transacao['quantidade']

                    # Verificar se a data está no período filtrado
                    if data_inicio <= data_transacao.date() <= data_fim:
                        cor_transacao = '#00d4aa' if quantidade_transacao > 0 else '#ff6b6b'
                        simbolo_transacao = 'triangle-up' if quantidade_transacao > 0 else 'triangle-down'
                        tipo_transacao = 'BUY' if quantidade_transacao > 0 else 'SELL'

                        fig_individual.add_trace(
                            go.Scatter(
                                x=[data_transacao],
                                y=[preco_transacao],
                                mode='markers',
                                name=f'{tipo_transacao}',
                                marker=dict(
                                    color=cor_transacao,
                                    size=12,
                                    symbol=simbolo_transacao,
                                    line=dict(color='#2d3748', width=2)
                                ),
                                hovertemplate=f'<b>{tipo_transacao}</b><br>%{{x}}<br>R$ %{{y:.2f}}<br>Qty: {quantidade_transacao}<extra></extra>',
                                showlegend=False
                            ),
                            row=1, col=1
                        )

                # Gráfico de volume
                fig_individual.add_trace(
                    go.Bar(
                        x=dados_acao_filtrado.index,
                        y=dados_acao_filtrado['Volume'],
                        name='Volume',
                        marker_color='#4ecdc4',
                        opacity=0.7,
                        hovertemplate='<b>Volume</b><br>%{x}<br>%{y:,.0f}<extra></extra>'
                    ),
                    row=2, col=1
                )

                # Layout do gráfico individual
                fig_individual.update_layout(
                    height=600,
                    paper_bgcolor='#0e1117',
                    plot_bgcolor='#1e2329',
                    font=dict(color='#fafafa'),
                    showlegend=True,
                    legend=dict(
                        orientation="h",
                        yanchor="bottom",
                        y=1.02,
                        xanchor="center",
                        x=0.5,
                        bgcolor='rgba(30, 35, 41, 0.8)',
                        bordercolor='#2d3748',
                        borderwidth=1
                    ),
                    hovermode='x unified'
                )

                # Atualizar eixos
                fig_individual.update_xaxes(
                    title_text="<b>DATE</b>",
                    row=2, col=1,
                    gridcolor='#2d3748',
                    linecolor='#2d3748',
                    tickcolor='#2d3748'
                )
                fig_individual.update_yaxes(
                    title_text="<b>PRICE (R$)</b>",
                    row=1, col=1,
                    gridcolor='#2d3748',
                    linecolor='#2d3748',
                    tickcolor='#2d3748'
                )
                fig_individual.update_yaxes(
                    title_text="<b>VOLUME</b>",
                    row=2, col=1,
                    gridcolor='#2d3748',
                    linecolor='#2d3748',
                    tickcolor='#2d3748'
                )

                st.plotly_chart(fig_individual, use_container_width=True)
            else:
                st.warning(f"⚠️ No data available for {acao_selecionada} in the selected period.")
        else:
            st.error(f"❌ Historical data not found for {acao_selecionada}")

    # Seção de gráficos de barra da análise simples
    st.markdown('<h2 class="section-header">📊 PORTFOLIO BAR CHARTS</h2>', unsafe_allow_html=True)

    # Função para consolidar posições (adaptada da análise simples)
    def consolidar_posicoes_dashboard(carteira_df):
        """Consolida posições por ticker para gráficos de barra"""
        posicoes_consolidadas = {}

        # Primeiro, calcular quantidade total por ticker
        for _, transacao in carteira_df.iterrows():
            ticker = transacao['ticker']
            quantidade = transacao['quantidade']
            preco = transacao['preco_compra']
            data_compra = transacao['data_compra']
            valor_transacao = quantidade * preco

            if ticker not in posicoes_consolidadas:
                posicoes_consolidadas[ticker] = {
                    'quantidade_total': 0,
                    'valor_investido_total': 0,
                    'valor_investido_original': 0,
                    'valor_vendido_total': 0,
                    'data_primeira_compra': data_compra,
                    'preco_medio_compra': 0,
                    'transacoes': []
                }

            posicoes_consolidadas[ticker]['quantidade_total'] += quantidade
            posicoes_consolidadas[ticker]['transacoes'].append({
                'quantidade': quantidade,
                'preco': preco,
                'data': data_compra,
                'valor': valor_transacao
            })

            if quantidade < 0:
                # É uma venda
                posicoes_consolidadas[ticker]['valor_vendido_total'] += abs(valor_transacao)
            else:
                # É uma compra
                posicoes_consolidadas[ticker]['valor_investido_original'] += valor_transacao
                if data_compra < posicoes_consolidadas[ticker]['data_primeira_compra']:
                    posicoes_consolidadas[ticker]['data_primeira_compra'] = data_compra

        # Agora calcular o preço médio apenas das ações que estão na carteira atual
        for ticker, dados in posicoes_consolidadas.items():
            quantidade_atual = dados['quantidade_total']

            if quantidade_atual > 0:
                # Ordenar transações por data (mais recentes primeiro)
                transacoes_ordenadas = sorted(dados['transacoes'], key=lambda x: x['data'], reverse=True)

                # Calcular preço médio das ações que estão na carteira
                quantidade_restante = quantidade_atual
                valor_total_carteira = 0

                for transacao in transacoes_ordenadas:
                    if quantidade_restante <= 0:
                        break

                    if transacao['quantidade'] > 0:  # É uma compra
                        qtd_a_considerar = min(transacao['quantidade'], quantidade_restante)
                        valor_total_carteira += qtd_a_considerar * transacao['preco']
                        quantidade_restante -= qtd_a_considerar

                dados['valor_investido_total'] = valor_total_carteira
                dados['preco_medio_compra'] = valor_total_carteira / quantidade_atual
            else:
                dados['valor_investido_total'] = 0
                dados['preco_medio_compra'] = 0

        return posicoes_consolidadas

    # Função para calcular rendimentos (adaptada da análise simples)
    def calcular_rendimentos_dashboard(carteira_df, dados_historicos):
        """Calcula rendimentos para gráficos de barra"""
        posicoes_consolidadas = consolidar_posicoes_dashboard(carteira_df)
        resultados = []

        # Obter preços atuais
        precos_atuais = {}
        for ticker in posicoes_consolidadas.keys():
            if ticker in dados_historicos:
                precos_atuais[ticker] = dados_historicos[ticker]['Close'].iloc[-1]

        for ticker, dados in posicoes_consolidadas.items():
            if ticker in precos_atuais:
                preco_atual = precos_atuais[ticker]
                quantidade_total = dados['quantidade_total']
                valor_investido_total = dados['valor_investido_total']  # Valor investido apenas nas ações atuais
                valor_investido_original = dados['valor_investido_original']  # Valor histórico total (para referência)
                valor_vendido_total = dados['valor_vendido_total']  # Valor histórico de vendas (para referência)
                preco_medio_compra = dados['preco_medio_compra']

                # P&L calculado APENAS com base nas ações que estão na carteira atual
                valor_atual = quantidade_total * preco_atual if quantidade_total > 0 else 0

                # Rendimento absoluto: diferença entre valor atual e valor investido nas ações atuais
                rendimento_absoluto = valor_atual - valor_investido_total

                # Rendimento percentual: baseado no valor investido nas ações atuais
                rendimento_percentual = (rendimento_absoluto / valor_investido_total) * 100 if valor_investido_total > 0 else 0

                resultado = {
                    'ticker': ticker,
                    'quantidade': quantidade_total,
                    'preco_compra': preco_medio_compra,
                    'preco_atual': preco_atual,
                    'valor_investido_liquido': valor_investido_total,  # Valor das ações atuais
                    'valor_atual': valor_atual,
                    'valor_total_recuperavel': valor_atual + valor_vendido_total,  # Para referência histórica
                    'valor_vendido_total': valor_vendido_total,  # Para referência histórica
                    'rendimento_absoluto': rendimento_absoluto,  # P&L apenas das ações atuais
                    'rendimento_percentual': rendimento_percentual  # P&L % apenas das ações atuais
                }
                resultados.append(resultado)

        return resultados

    # Calcular dados para gráficos de barra
    resultados_barras = calcular_rendimentos_dashboard(carteira, dados_historicos)
    resultados_ativos = [r for r in resultados_barras if r['quantidade'] > 0]

    if resultados_ativos:
        # Preparar dados
        tickers_ativos = [r['ticker'].replace('.SA', '') for r in resultados_ativos]
        valores_investidos_liquidos = [r['valor_investido_liquido'] for r in resultados_ativos]
        valores_atuais = [r['valor_atual'] for r in resultados_ativos]
        rendimentos_pct = [r['rendimento_percentual'] for r in resultados_ativos]

        # Layout em 3 colunas para os gráficos
        col1, col2, col3 = st.columns(3)

        with col1:
            # 1. Gráfico de barras - Valores Investidos vs Atuais
            fig_bar1 = go.Figure()

            fig_bar1.add_trace(go.Bar(
                x=tickers_ativos,
                y=valores_investidos_liquidos,
                name='Invested Value',
                marker_color='#4ecdc4',
                opacity=0.8,
                hovertemplate='<b>%{x}</b><br>Invested: R$ %{y:,.2f}<extra></extra>'
            ))

            fig_bar1.add_trace(go.Bar(
                x=tickers_ativos,
                y=valores_atuais,
                name='Current Value',
                marker_color='#00d4aa',
                opacity=0.8,
                hovertemplate='<b>%{x}</b><br>Current: R$ %{y:,.2f}<extra></extra>'
            ))

            fig_bar1.update_layout(
                title='<b>INVESTED vs CURRENT VALUE</b>',
                xaxis_title='<b>SYMBOLS</b>',
                yaxis_title='<b>VALUE (R$)</b>',
                paper_bgcolor='#0e1117',
                plot_bgcolor='#1e2329',
                font=dict(color='#fafafa'),
                showlegend=True,
                legend=dict(
                    bgcolor='rgba(30, 35, 41, 0.8)',
                    bordercolor='#2d3748',
                    borderwidth=1
                ),
                xaxis=dict(
                    gridcolor='#2d3748',
                    linecolor='#2d3748',
                    tickcolor='#2d3748'
                ),
                yaxis=dict(
                    gridcolor='#2d3748',
                    linecolor='#2d3748',
                    tickcolor='#2d3748'
                ),
                height=400
            )

            st.plotly_chart(fig_bar1, use_container_width=True)

        with col2:
            # 2. Gráfico de pizza - Distribuição da carteira (já existe, mas vou recriar aqui)
            fig_pie = px.pie(
                values=valores_atuais,
                names=tickers_ativos,
                title="<b>PORTFOLIO DISTRIBUTION</b>",
                color_discrete_sequence=['#00d4aa', '#f7931e', '#4ecdc4', '#45b7d1', '#ff6b6b', '#c44569', '#96ceb4', '#feca57']
            )

            fig_pie.update_layout(
                paper_bgcolor='#0e1117',
                plot_bgcolor='#1e2329',
                font=dict(color='#fafafa'),
                showlegend=True,
                legend=dict(
                    bgcolor='rgba(30, 35, 41, 0.8)',
                    bordercolor='#2d3748',
                    borderwidth=1
                ),
                height=400
            )

            fig_pie.update_traces(
                textposition='inside',
                textinfo='percent+label',
                textfont_size=10,
                marker=dict(line=dict(color='#2d3748', width=2))
            )

            st.plotly_chart(fig_pie, use_container_width=True)

        with col3:
            # 3. Gráfico de barras - Rendimento percentual
            colors = ['#00d4aa' if r >= 0 else '#ff6b6b' for r in rendimentos_pct]

            fig_bar3 = go.Figure()

            fig_bar3.add_trace(go.Bar(
                x=tickers_ativos,
                y=rendimentos_pct,
                marker_color=colors,
                opacity=0.8,
                text=[f'{r:.1f}%' for r in rendimentos_pct],
                textposition='outside',
                hovertemplate='<b>%{x}</b><br>P&L: %{y:.2f}%<extra></extra>'
            ))

            fig_bar3.add_hline(
                y=0,
                line_dash="solid",
                line_color="#8b949e",
                line_width=1,
                opacity=0.7
            )

            fig_bar3.update_layout(
                title='<b>P&L BY STOCK (%)</b>',
                xaxis_title='<b>SYMBOLS</b>',
                yaxis_title='<b>P&L (%)</b>',
                paper_bgcolor='#0e1117',
                plot_bgcolor='#1e2329',
                font=dict(color='#fafafa'),
                showlegend=False,
                xaxis=dict(
                    gridcolor='#2d3748',
                    linecolor='#2d3748',
                    tickcolor='#2d3748'
                ),
                yaxis=dict(
                    gridcolor='#2d3748',
                    linecolor='#2d3748',
                    tickcolor='#2d3748'
                ),
                height=400
            )

            st.plotly_chart(fig_bar3, use_container_width=True)

    else:
        st.warning("⚠️ No active positions found for bar charts.")

    # Tabela detalhada estilo trader
    st.markdown('<h2 class="section-header">📋 DETAILED DATA</h2>', unsafe_allow_html=True)

    tab1, tab2 = st.tabs(["📈 EQUITY DATA", "💼 TRADE LOG"])

    with tab1:
        st.markdown('<h3 style="color: #f7931e;">📊 Portfolio Evolution Data</h3>', unsafe_allow_html=True)

        # Formatar dados para exibição
        df_display = df_filtrado.copy()
        df_display['data'] = df_display['data'].dt.strftime('%d/%m/%Y')

        # Formatar colunas monetárias
        colunas_monetarias = ['capital_inicial', 'capital_disponivel', 'valor_atual_acoes',
                             'dividendos_recebidos', 'valor_total_recuperavel', 'capital_total', 'valor_investido_bruto',
                             'valor_vendido_total', 'rendimento_capital_absoluto']

        for col in colunas_monetarias:
            if col in df_display.columns:
                df_display[col] = df_display[col].apply(lambda x: f"R$ {x:,.2f}")

        # Formatar coluna percentual
        if 'rendimento_capital_percentual' in df_display.columns:
            df_display['rendimento_capital_percentual'] = df_display['rendimento_capital_percentual'].apply(lambda x: f"{x:.2f}%")

        # Renomear colunas para inglês
        df_display.columns = [
            'DATE', 'INITIAL_CAPITAL', 'CASH_AVAILABLE', 'POSITIONS_VALUE',
            'DIVIDENDS_RECEIVED', 'TOTAL_RECOVERABLE', 'TOTAL_EQUITY', 'GROSS_INVESTED',
            'TOTAL_SOLD', 'PNL_ABS', 'PNL_PCT'
        ]

        st.dataframe(df_display, use_container_width=True)

    with tab2:
        st.markdown('<h3 style="color: #f7931e;">📝 Complete Trade History</h3>', unsafe_allow_html=True)

        # Formatar dados de transações
        transacoes_display = carteira.copy()
        transacoes_display['data_compra'] = transacoes_display['data_compra'].dt.strftime('%d/%m/%Y')
        transacoes_display['ticker'] = transacoes_display['ticker'].str.replace('.SA', '')
        transacoes_display['preco_compra'] = transacoes_display['preco_compra'].apply(lambda x: f"R$ {x:.2f}")
        transacoes_display['valor_total'] = (carteira['quantidade'] * carteira['preco_compra']).apply(lambda x: f"R$ {x:.2f}")
        transacoes_display['tipo'] = carteira['quantidade'].apply(lambda x: '� BUY' if x > 0 else '� SELL')

        # Reordenar colunas
        colunas_ordem = ['data_compra', 'ticker', 'tipo', 'quantidade', 'preco_compra', 'valor_total']
        transacoes_display = transacoes_display[colunas_ordem]

        # Renomear colunas para inglês
        transacoes_display.columns = ['DATE', 'SYMBOL', 'TYPE', 'QTY', 'PRICE', 'VALUE']

        st.dataframe(transacoes_display.sort_values('DATE', ascending=False), use_container_width=True)

    # Controles da sidebar estilo trader
    st.sidebar.markdown("---")
    st.sidebar.markdown('<h3 style="color: #f7931e;">🔄 SYSTEM CONTROLS</h3>', unsafe_allow_html=True)

    if st.sidebar.button("🔄 REFRESH DATA", type="primary"):
        st.cache_data.clear()
        with st.spinner("🔄 Updating portfolio analysis..."):
            main()
        st.success("✅ Portfolio data updated successfully!")
        st.rerun()

    # Market status simulado
    st.sidebar.markdown("---")
    st.sidebar.markdown('<h3 style="color: #f7931e;">📊 MARKET STATUS</h3>', unsafe_allow_html=True)

    import datetime
    now = datetime.datetime.now()
    market_hours = 9 <= now.hour <= 18 and now.weekday() < 5
    status_color = "#00d4aa" if market_hours else "#ff6b6b"
    status_text = "🟢 OPEN" if market_hours else "🔴 CLOSED"

    st.sidebar.markdown(f'<div style="color: {status_color}; font-weight: bold; font-size: 16px;">{status_text}</div>', unsafe_allow_html=True)
    st.sidebar.markdown(f"Last Update: {now.strftime('%H:%M:%S')}")

    # Informações adicionais estilo trader
    st.sidebar.markdown("---")
    st.sidebar.markdown('<h3 style="color: #f7931e;">💡 TRADING TIPS</h3>', unsafe_allow_html=True)
    st.sidebar.markdown(
        """
        <div style="font-size: 12px; color: #8b949e;">
        • Use time filters for specific period analysis<br>
        • Hover over charts for detailed information<br>
        • Monitor P&L percentage for performance<br>
        • Check position allocation regularly<br>
        • Review trade history for patterns
        </div>
        """,
        unsafe_allow_html=True
    )

if __name__ == "__main__":
    import sys

    # Verificar se deve executar o dashboard
    if len(sys.argv) > 1 and sys.argv[1] == "--dashboard":
        criar_dashboard()
    else:
        main()
