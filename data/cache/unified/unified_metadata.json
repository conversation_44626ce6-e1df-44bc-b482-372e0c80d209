{"last_update": "2025-09-24 12:41:10.618167", "tickers": {"STBP3.SA": {"nome": "Santos Brasil", "origem": "diversificacao", "total_days": 3729, "data_start": "2010-09-22 00:00:00", "data_end": "2025-09-24 00:00:00"}, "WEGE3.SA": {"nome": "WEG", "origem": "diversificacao", "total_days": 3729, "data_start": "2010-09-22 00:00:00", "data_end": "2025-09-24 00:00:00"}, "PORT3.SA": {"nome": "Wilson Sons", "origem": "diversificacao", "total_days": 979, "data_start": "2021-10-25 00:00:00", "data_end": "2025-09-24 00:00:00"}, "MDIA3.SA": {"nome": "M<PERSON>ran<PERSON>", "origem": "diversificacao", "total_days": 3729, "data_start": "2010-09-22 00:00:00", "data_end": "2025-09-24 00:00:00"}, "PETR3.SA": {"nome": "Petrobras", "origem": "diversificacao", "total_days": 3729, "data_start": "2010-09-22 00:00:00", "data_end": "2025-09-24 00:00:00"}, "ODPV3.SA": {"nome": "Odontoprev", "origem": "diversificacao", "total_days": 3729, "data_start": "2010-09-22 00:00:00", "data_end": "2025-09-24 00:00:00"}, "GGBR4.SA": {"nome": "<PERSON><PERSON><PERSON>", "origem": "diversificacao", "total_days": 3729, "data_start": "2010-09-22 00:00:00", "data_end": "2025-09-24 00:00:00"}, "RADL3.SA": {"nome": "Raia<PERSON><PERSON><PERSON><PERSON>", "origem": "diversificacao", "total_days": 3729, "data_start": "2010-09-22 00:00:00", "data_end": "2025-09-24 00:00:00"}, "CMIG3.SA": {"nome": "Cemig", "origem": "diversificacao", "total_days": 3729, "data_start": "2010-09-22 00:00:00", "data_end": "2025-09-24 00:00:00"}, "CSMG3.SA": {"nome": "COPASA", "origem": "diversificacao", "total_days": 3729, "data_start": "2010-09-22 00:00:00", "data_end": "2025-09-24 00:00:00"}, "KLBN11.SA": {"nome": "<PERSON><PERSON><PERSON>", "origem": "diversificacao", "total_days": 2904, "data_start": "2014-01-23 00:00:00", "data_end": "2025-09-24 00:00:00"}, "HYPE3.SA": {"nome": "<PERSON><PERSON><PERSON>", "origem": "diversificacao", "total_days": 3729, "data_start": "2010-09-22 00:00:00", "data_end": "2025-09-24 00:00:00"}, "CXSE3.SA": {"nome": "Caixa Seguridade", "origem": "diversificacao", "total_days": 1101, "data_start": "2021-04-30 00:00:00", "data_end": "2025-09-24 00:00:00"}, "MELK3.SA": {"nome": "<PERSON><PERSON>", "origem": "diversificacao", "total_days": 1242, "data_start": "2020-09-29 00:00:00", "data_end": "2025-09-24 00:00:00"}, "PSSA3.SA": {"nome": "Porto Seguro", "origem": "diversificacao", "total_days": 3729, "data_start": "2010-09-22 00:00:00", "data_end": "2025-09-24 00:00:00"}, "GRND3.SA": {"nome": "Grendene", "origem": "diversificacao", "total_days": 3729, "data_start": "2010-09-22 00:00:00", "data_end": "2025-09-24 00:00:00"}, "POMO4.SA": {"nome": "Marcopolo", "origem": "diversificacao", "total_days": 3729, "data_start": "2010-09-22 00:00:00", "data_end": "2025-09-24 00:00:00"}, "BRSR6.SA": {"nome": "<PERSON><PERSON><PERSON>", "origem": "diversificacao", "total_days": 3729, "data_start": "2010-09-22 00:00:00", "data_end": "2025-09-24 00:00:00"}, "BBAS3.SA": {"nome": "Banco do Brasil", "origem": "diversificacao", "total_days": 3729, "data_start": "2010-09-22 00:00:00", "data_end": "2025-09-24 00:00:00"}, "POMO3.SA": {"nome": "Marcopolo", "origem": "diversificacao", "total_days": 3729, "data_start": "2010-09-22 00:00:00", "data_end": "2025-09-24 00:00:00"}, "VIVT3.SA": {"nome": "Vivo", "origem": "diversificacao", "total_days": 3729, "data_start": "2010-09-22 00:00:00", "data_end": "2025-09-24 00:00:00"}, "RANI3.SA": {"nome": "Irani", "origem": "diversificacao", "total_days": 3729, "data_start": "2010-09-22 00:00:00", "data_end": "2025-09-24 00:00:00"}, "TAEE4.SA": {"nome": "<PERSON><PERSON><PERSON>", "origem": "diversificacao", "total_days": 2090, "data_start": "2017-05-10 00:00:00", "data_end": "2025-09-24 00:00:00"}, "SHUL4.SA": {"nome": "<PERSON><PERSON><PERSON>", "origem": "diversificacao", "total_days": 3729, "data_start": "2010-09-22 00:00:00", "data_end": "2025-09-24 00:00:00"}, "PNVL3.SA": {"nome": "Di<PERSON>", "origem": "diversificacao", "total_days": 3729, "data_start": "2010-09-22 00:00:00", "data_end": "2025-09-24 00:00:00"}, "CMIG4.SA": {"nome": "Cemig", "origem": "diversificacao", "total_days": 3729, "data_start": "2010-09-22 00:00:00", "data_end": "2025-09-24 00:00:00"}, "ALUP11.SA": {"nome": "<PERSON><PERSON><PERSON>", "origem": "diversificacao", "total_days": 3091, "data_start": "2013-04-24 00:00:00", "data_end": "2025-09-24 00:00:00"}, "ROMI3.SA": {"nome": "IndÃºstrias ROMI", "origem": "diversificacao", "total_days": 3729, "data_start": "2010-09-22 00:00:00", "data_end": "2025-09-24 00:00:00"}, "VULC3.SA": {"nome": "Vulcabras", "origem": "diversificacao", "total_days": 3729, "data_start": "2010-09-22 00:00:00", "data_end": "2025-09-24 00:00:00"}, "CAML3.SA": {"nome": "<PERSON><PERSON>", "origem": "diversificacao", "total_days": 1989, "data_start": "2017-09-28 00:00:00", "data_end": "2025-09-24 00:00:00"}, "RDOR3.SA": {"nome": "Rede D'Or", "origem": "diversificacao", "total_days": 1190, "data_start": "2020-12-15 00:00:00", "data_end": "2025-09-24 00:00:00"}, "EZTC3.SA": {"nome": "EZTEC", "origem": "diversificacao", "total_days": 3729, "data_start": "2010-09-22 00:00:00", "data_end": "2025-09-24 00:00:00"}}, "total_records": 3729, "date_range": {"start": "2010-09-22 00:00:00", "end": "2025-09-24 00:00:00"}, "compression": "snappy", "columns": ["STBP3.SA_Open", "STBP3.SA_High", "STBP3.SA_Low", "STBP3.SA_Close", "STBP3.SA_Volume", "STBP3.SA_Media_OHLC", "STBP3.SA_MM10", "STBP3.SA_MM25", "STBP3.SA_MM100", "STBP3.SA_Media_OHLC_PctChange", "STBP3.SA_Open_PctChange", "STBP3.SA_High_PctChange", "STBP3.SA_Low_PctChange", "STBP3.SA_Close_PctChange", "STBP3.SA_Close_PctChange_Historical", "STBP3.SA_Volatilidade", "STBP3.SA_Spread", "STBP3.SA_Parkinson_Volatility", "STBP3.SA_MFI", "STBP3.SA_MFI_Historical", "STBP3.SA_EMV", "STBP3.SA_EMV_MA", "STBP3.SA_Amihud", "STBP3.SA_Amihud_Historical", "STBP3.SA_Roll_Spread", "STBP3.SA_Roll_Spread_Historical", "STBP3.SA_Hurst", "STBP3.SA_<PERSON><PERSON>_Historical", "STBP3.SA_Vol_per_Volume", "STBP3.SA_Vol_per_Volume_Historical", "STBP3.SA_CMF", "STBP3.SA_CMF_Historical", "STBP3.SA_AD_Line", "STBP3.SA_AD_Line_Historical", "STBP3.SA_VO", "STBP3.SA_High_Max_50", "STBP3.SA_Low_Min_50", "STBP3.SA_Segunda", "STBP3.SA_Terca", "STBP3.SA_Quarta", "STBP3.SA_Quinta", "STBP3.SA_Sexta", "STBP3.SA_Mes_1", "STBP3.SA_Mes_2", "STBP3.SA_Mes_3", "STBP3.SA_Mes_4", "STBP3.SA_Mes_5", "STBP3.SA_Mes_6", "STBP3.SA_Mes_7", "STBP3.SA_Mes_8", "STBP3.SA_Mes_9", "STBP3.SA_Mes_10", "STBP3.SA_Mes_11", "STBP3.SA_Mes_12", "STBP3.SA_Quarter_1", "STBP3.SA_Quarter_2", "STBP3.SA_Quarter_3", "STBP3.SA_Quarter_4", "STBP3.SA_Last_Day_Quarter", "STBP3.SA_Pre_Feriado_Brasil", "STBP3.SA_Media_OHLC_Anterior", "STBP3.SA_Sinal_Compra", "STBP3.SA_Sinal_Venda", "STBP3.SA_Media_OHLC_Futura", "STBP3.SA_Open_PctChange_Lag_1", "STBP3.SA_High_PctChange_Lag_1", "STBP3.SA_Low_PctChange_Lag_1", "STBP3.SA_Close_PctChange_Lag_1", "STBP3.SA_Open_PctChange_Lag_2", "STBP3.SA_High_PctChange_Lag_2", "STBP3.SA_Low_PctChange_Lag_2", "STBP3.SA_Close_PctChange_Lag_2", "STBP3.SA_Open_PctChange_Lag_3", "STBP3.SA_High_PctChange_Lag_3", "STBP3.SA_Low_PctChange_Lag_3", "STBP3.SA_Close_PctChange_Lag_3", "STBP3.SA_Open_PctChange_Lag_4", "STBP3.SA_High_PctChange_Lag_4", "STBP3.SA_Low_PctChange_Lag_4", "STBP3.SA_Close_PctChange_Lag_4", "STBP3.SA_Open_PctChange_Lag_5", "STBP3.SA_High_PctChange_Lag_5", "STBP3.SA_Low_PctChange_Lag_5", "STBP3.SA_Close_PctChange_Lag_5", "STBP3.SA_Open_PctChange_Lag_6", "STBP3.SA_High_PctChange_Lag_6", "STBP3.SA_Low_PctChange_Lag_6", "STBP3.SA_Close_PctChange_Lag_6", "STBP3.SA_Open_PctChange_Lag_7", "STBP3.SA_High_PctChange_Lag_7", "STBP3.SA_Low_PctChange_Lag_7", "STBP3.SA_Close_PctChange_Lag_7", "STBP3.SA_Open_PctChange_Lag_8", "STBP3.SA_High_PctChange_Lag_8", "STBP3.SA_Low_PctChange_Lag_8", "STBP3.SA_Close_PctChange_Lag_8", "STBP3.SA_Open_PctChange_Lag_9", "STBP3.SA_High_PctChange_Lag_9", "STBP3.SA_Low_PctChange_Lag_9", "STBP3.SA_Close_PctChange_Lag_9", "STBP3.SA_Open_PctChange_Lag_10", "STBP3.SA_High_PctChange_Lag_10", "STBP3.SA_Low_PctChange_Lag_10", "STBP3.SA_Close_PctChange_Lag_10", "STBP3.SA_MM10_PctChange", "STBP3.SA_Diff_OHLC_MM10", "STBP3.SA_MM25_PctChange", "STBP3.SA_Diff_OHLC_MM25", "STBP3.SA_MM100_PctChange", "STBP3.SA_Diff_OHLC_MM100", "STBP3.SA_MM10_Menos_MM25", "STBP3.SA_MM25_Menos_MM100", "STBP3.SA_MM10_Menos_MM100", "STBP3.SA_Volume_Lag_1", "STBP3.SA_Volume_Lag_2", "STBP3.SA_Volume_Lag_3", "STBP3.SA_Volume_Lag_4", "STBP3.SA_Volume_Lag_5", "STBP3.SA_Spread_Lag_1", "STBP3.SA_Spread_Lag_2", "STBP3.SA_Spread_Lag_3", "STBP3.SA_Spread_Lag_4", "STBP3.SA_Spread_Lag_5", "STBP3.SA_Volatilidade_Lag_1", "STBP3.SA_Volatilidade_Lag_2", "STBP3.SA_Volatilidade_Lag_3", "STBP3.SA_Volatilidade_Lag_4", "STBP3.SA_Volatilidade_Lag_5", "STBP3.SA_Parkinson_Volatility_Lag_1", "STBP3.SA_Parkinson_Volatility_Lag_2", "STBP3.SA_Parkinson_Volatility_Lag_3", "STBP3.SA_Parkinson_Volatility_Lag_4", "STBP3.<PERSON>_Parkinson_Volatility_Lag_5", "STBP3.SA_EMV_Lag_1", "STBP3.SA_EMV_Lag_2", "STBP3.SA_EMV_Lag_3", "STBP3.SA_EMV_Lag_4", "STBP3.SA_EMV_Lag_5", "STBP3.SA_EMV_MA_Lag_1", "STBP3.SA_EMV_MA_Lag_2", "STBP3.SA_EMV_MA_Lag_3", "STBP3.SA_EMV_MA_Lag_4", "STBP3.SA_EMV_MA_Lag_5", "STBP3.SA_VO_Lag_1", "STBP3.SA_VO_Lag_2", "STBP3.SA_VO_Lag_3", "STBP3.SA_VO_Lag_4", "STBP3.SA_VO_Lag_5", "STBP3.SA_High_Max_50_Lag_1", "STBP3.SA_High_Max_50_Lag_2", "STBP3.SA_High_Max_50_Lag_3", "STBP3.SA_High_Max_50_Lag_4", "STBP3.SA_High_Max_50_Lag_5", "STBP3.SA_Low_Min_50_Lag_1", "STBP3.SA_Low_Min_50_Lag_2", "STBP3.SA_Low_Min_50_Lag_3", "STBP3.SA_Low_Min_50_Lag_4", "STBP3.SA_Low_Min_50_Lag_5", "STBP3.SA_MFI_Lag_1", "STBP3.SA_MFI_Lag_2", "STBP3.SA_MFI_Lag_3", "STBP3.SA_MFI_Lag_4", "STBP3.SA_MFI_Lag_5", "STBP3.SA_Amihud_Lag_1", "STBP3.SA_Amihud_Lag_2", "STBP3.SA_Amihud_Lag_3", "STBP3.SA_Amihud_Lag_4", "STBP3.SA_Amihud_Lag_5", "STBP3.SA_Roll_Spread_Lag_1", "STBP3.SA_Roll_Spread_Lag_2", "STBP3.SA_Roll_Spread_Lag_3", "STBP3.SA_Roll_Spread_Lag_4", "STBP3.SA_Roll_Spread_Lag_5", "STBP3.SA_Hurst_Lag_1", "STBP3.SA_Hurst_Lag_2", "STBP3.SA_Hurst_Lag_3", "STBP3.SA_Hurst_Lag_4", "STBP3.<PERSON>_<PERSON><PERSON>_Lag_5", "STBP3.SA_Vol_per_Volume_Lag_1", "STBP3.SA_Vol_per_Volume_Lag_2", "STBP3.SA_Vol_per_Volume_Lag_3", "STBP3.SA_Vol_per_Volume_Lag_4", "STBP3.SA_Vol_per_Volume_Lag_5", "STBP3.SA_CMF_Lag_1", "STBP3.SA_CMF_Lag_2", "STBP3.SA_CMF_Lag_3", "STBP3.SA_CMF_Lag_4", "STBP3.SA_CMF_Lag_5", "STBP3.SA_AD_Line_Lag_1", "STBP3.SA_AD_Line_Lag_2", "STBP3.SA_AD_Line_Lag_3", "STBP3.SA_AD_Line_Lag_4", "STBP3.SA_AD_Line_Lag_5", "WEGE3.SA_Open", "WEGE3.SA_High", "WEGE3.SA_Low", "WEGE3.SA_Close", "WEGE3.SA_Volume", "WEGE3.SA_Media_OHLC", "WEGE3.SA_MM10", "WEGE3.SA_MM25", "WEGE3.SA_MM100", "WEGE3.SA_Media_OHLC_PctChange", "WEGE3.SA_Open_PctChange", "WEGE3.SA_High_PctChange", "WEGE3.SA_Low_PctChange", "WEGE3.SA_Close_PctChange", "WEGE3.SA_Close_PctChange_Historical", "WEGE3.SA_Volatilidade", "WEGE3.SA_Spread", "WEGE3.SA_Parkinson_Volatility", "WEGE3.SA_MFI", "WEGE3.SA_MFI_Historical", "WEGE3.SA_EMV", "WEGE3.SA_EMV_MA", "WEGE3.SA_Amihud", "WEGE3.SA_Amihud_Historical", "WEGE3.SA_Roll_Spread", "WEGE3.SA_Roll_Spread_Historical", "WEGE3.SA_Hurst", "WEGE3.<PERSON>_<PERSON><PERSON>_Historical", "WEGE3.SA_Vol_per_Volume", "WEGE3.SA_Vol_per_Volume_Historical", "WEGE3.SA_CMF", "WEGE3.SA_CMF_Historical", "WEGE3.SA_AD_Line", "WEGE3.SA_AD_Line_Historical", "WEGE3.SA_VO", "WEGE3.SA_High_Max_50", "WEGE3.SA_Low_Min_50", "WEGE3.SA_Segunda", "WEGE3.SA_Terca", "WEGE3.SA_Quarta", "WEGE3.SA_Quinta", "WEGE3.SA_Sexta", "WEGE3.SA_Mes_1", "WEGE3.SA_Mes_2", "WEGE3.SA_Mes_3", "WEGE3.SA_Mes_4", "WEGE3.SA_Mes_5", "WEGE3.SA_Mes_6", "WEGE3.SA_Mes_7", "WEGE3.SA_Mes_8", "WEGE3.SA_Mes_9", "WEGE3.SA_Mes_10", "WEGE3.SA_Mes_11", "WEGE3.SA_Mes_12", "WEGE3.SA_Quarter_1", "WEGE3.SA_Quarter_2", "WEGE3.SA_Quarter_3", "WEGE3.SA_Quarter_4", "WEGE3.SA_Last_Day_Quarter", "WEGE3.SA_Pre_Feriado_Brasil", "WEGE3.SA_Media_OHLC_Anterior", "WEGE3.SA_Sinal_Compra", "WEGE3.SA_Sinal_Venda", "WEGE3.SA_Media_OHLC_Futura", "WEGE3.SA_Open_PctChange_Lag_1", "WEGE3.SA_High_PctChange_Lag_1", "WEGE3.SA_Low_PctChange_Lag_1", "WEGE3.SA_Close_PctChange_Lag_1", "WEGE3.SA_Open_PctChange_Lag_2", "WEGE3.SA_High_PctChange_Lag_2", "WEGE3.SA_Low_PctChange_Lag_2", "WEGE3.SA_Close_PctChange_Lag_2", "WEGE3.SA_Open_PctChange_Lag_3", "WEGE3.SA_High_PctChange_Lag_3", "WEGE3.SA_Low_PctChange_Lag_3", "WEGE3.SA_Close_PctChange_Lag_3", "WEGE3.SA_Open_PctChange_Lag_4", "WEGE3.SA_High_PctChange_Lag_4", "WEGE3.SA_Low_PctChange_Lag_4", "WEGE3.SA_Close_PctChange_Lag_4", "WEGE3.SA_Open_PctChange_Lag_5", "WEGE3.SA_High_PctChange_Lag_5", "WEGE3.SA_Low_PctChange_Lag_5", "WEGE3.SA_Close_PctChange_Lag_5", "WEGE3.SA_Open_PctChange_Lag_6", "WEGE3.SA_High_PctChange_Lag_6", "WEGE3.SA_Low_PctChange_Lag_6", "WEGE3.SA_Close_PctChange_Lag_6", "WEGE3.SA_Open_PctChange_Lag_7", "WEGE3.SA_High_PctChange_Lag_7", "WEGE3.SA_Low_PctChange_Lag_7", "WEGE3.SA_Close_PctChange_Lag_7", "WEGE3.SA_Open_PctChange_Lag_8", "WEGE3.SA_High_PctChange_Lag_8", "WEGE3.SA_Low_PctChange_Lag_8", "WEGE3.SA_Close_PctChange_Lag_8", "WEGE3.SA_Open_PctChange_Lag_9", "WEGE3.SA_High_PctChange_Lag_9", "WEGE3.SA_Low_PctChange_Lag_9", "WEGE3.SA_Close_PctChange_Lag_9", "WEGE3.SA_Open_PctChange_Lag_10", "WEGE3.SA_High_PctChange_Lag_10", "WEGE3.SA_Low_PctChange_Lag_10", "WEGE3.SA_Close_PctChange_Lag_10", "WEGE3.SA_MM10_PctChange", "WEGE3.SA_Diff_OHLC_MM10", "WEGE3.SA_MM25_PctChange", "WEGE3.SA_Diff_OHLC_MM25", "WEGE3.SA_MM100_PctChange", "WEGE3.SA_Diff_OHLC_MM100", "WEGE3.SA_MM10_Menos_MM25", "WEGE3.SA_MM25_Menos_MM100", "WEGE3.SA_MM10_Menos_MM100", "WEGE3.SA_Volume_Lag_1", "WEGE3.SA_Volume_Lag_2", "WEGE3.SA_Volume_Lag_3", "WEGE3.SA_Volume_Lag_4", "WEGE3.SA_Volume_Lag_5", "WEGE3.SA_Spread_Lag_1", "WEGE3.SA_Spread_Lag_2", "WEGE3.SA_Spread_Lag_3", "WEGE3.SA_Spread_Lag_4", "WEGE3.SA_Spread_Lag_5", "WEGE3.SA_Volatilidade_Lag_1", "WEGE3.SA_Volatilidade_Lag_2", "WEGE3.SA_Volatilidade_Lag_3", "WEGE3.SA_Volatilidade_Lag_4", "WEGE3.SA_Volatilidade_Lag_5", "WEGE3.SA_Parkinson_Volatility_Lag_1", "WEGE3.<PERSON>_Parkinson_Volatility_Lag_2", "WEGE3.<PERSON>_Parkinson_Volatility_Lag_3", "WEGE3.<PERSON>_Parkinson_Volatility_Lag_4", "WEGE3.<PERSON>_Parkinson_Volatility_Lag_5", "WEGE3.SA_EMV_Lag_1", "WEGE3.SA_EMV_Lag_2", "WEGE3.SA_EMV_Lag_3", "WEGE3.SA_EMV_Lag_4", "WEGE3.SA_EMV_Lag_5", "WEGE3.SA_EMV_MA_Lag_1", "WEGE3.SA_EMV_MA_Lag_2", "WEGE3.SA_EMV_MA_Lag_3", "WEGE3.SA_EMV_MA_Lag_4", "WEGE3.SA_EMV_MA_Lag_5", "WEGE3.SA_VO_Lag_1", "WEGE3.SA_VO_Lag_2", "WEGE3.SA_VO_Lag_3", "WEGE3.SA_VO_Lag_4", "WEGE3.SA_VO_Lag_5", "WEGE3.SA_High_Max_50_Lag_1", "WEGE3.SA_High_Max_50_Lag_2", "WEGE3.SA_High_Max_50_Lag_3", "WEGE3.SA_High_Max_50_Lag_4", "WEGE3.SA_High_Max_50_Lag_5", "WEGE3.SA_Low_Min_50_Lag_1", "WEGE3.SA_Low_Min_50_Lag_2", "WEGE3.SA_Low_Min_50_Lag_3", "WEGE3.SA_Low_Min_50_Lag_4", "WEGE3.SA_Low_Min_50_Lag_5", "WEGE3.SA_MFI_Lag_1", "WEGE3.SA_MFI_Lag_2", "WEGE3.SA_MFI_Lag_3", "WEGE3.SA_MFI_Lag_4", "WEGE3.SA_MFI_Lag_5", "WEGE3.SA_Amihud_Lag_1", "WEGE3.SA_Amihud_Lag_2", "WEGE3.SA_Amihud_Lag_3", "WEGE3.SA_Amihud_Lag_4", "WEGE3.SA_Amihud_Lag_5", "WEGE3.SA_Roll_Spread_Lag_1", "WEGE3.SA_Roll_Spread_Lag_2", "WEGE3.SA_Roll_Spread_Lag_3", "WEGE3.SA_Roll_Spread_Lag_4", "WEGE3.SA_Roll_Spread_Lag_5", "WEGE3.<PERSON>_Hurst_Lag_1", "WEGE3.<PERSON>_Hu<PERSON>_Lag_2", "WEGE3.<PERSON>_Hu<PERSON>_Lag_3", "WEGE3.<PERSON>_<PERSON><PERSON>_Lag_4", "WEGE3.<PERSON>_<PERSON><PERSON>_Lag_5", "WEGE3.SA_Vol_per_Volume_Lag_1", "WEGE3.SA_Vol_per_Volume_Lag_2", "WEGE3.SA_Vol_per_Volume_Lag_3", "WEGE3.SA_Vol_per_Volume_Lag_4", "WEGE3.SA_Vol_per_Volume_Lag_5", "WEGE3.SA_CMF_Lag_1", "WEGE3.SA_CMF_Lag_2", "WEGE3.SA_CMF_Lag_3", "WEGE3.SA_CMF_Lag_4", "WEGE3.SA_CMF_Lag_5", "WEGE3.SA_AD_Line_Lag_1", "WEGE3.SA_AD_Line_Lag_2", "WEGE3.SA_AD_Line_Lag_3", "WEGE3.SA_AD_Line_Lag_4", "WEGE3.SA_AD_Line_Lag_5", "PORT3.SA_Close", "PORT3.SA_High", "PORT3.SA_Low", "PORT3.SA_Open", "PORT3.SA_Volume", "PORT3.SA_Media_OHLC", "PORT3.SA_MM10", "PORT3.SA_MM25", "PORT3.SA_MM100", "PORT3.SA_Media_OHLC_PctChange", "PORT3.SA_Open_PctChange", "PORT3.SA_High_PctChange", "PORT3.SA_Low_PctChange", "PORT3.SA_Close_PctChange", "PORT3.SA_Close_PctChange_Historical", "PORT3.SA_Volatilidade", "PORT3.SA_Spread", "PORT3.SA_Parkinson_Volatility", "PORT3.SA_MFI", "PORT3.SA_MFI_Historical", "PORT3.SA_EMV", "PORT3.SA_EMV_MA", "PORT3.SA_Amihud", "PORT3.SA_Amihud_Historical", "PORT3.SA_Roll_Spread", "PORT3.SA_Roll_Spread_Historical", "PORT3.SA_Hurst", "PORT3.<PERSON>_<PERSON><PERSON>_Historical", "PORT3.SA_Vol_per_Volume", "PORT3.SA_Vol_per_Volume_Historical", "PORT3.SA_CMF", "PORT3.SA_CMF_Historical", "PORT3.SA_AD_Line", "PORT3.SA_AD_Line_Historical", "PORT3.SA_VO", "PORT3.SA_High_Max_50", "PORT3.SA_Low_Min_50", "PORT3.SA_Segunda", "PORT3.SA_Terca", "PORT3.SA_Quarta", "PORT3.SA_Quinta", "PORT3.SA_Sexta", "PORT3.SA_Mes_1", "PORT3.SA_Mes_2", "PORT3.SA_Mes_3", "PORT3.SA_Mes_4", "PORT3.SA_Mes_5", "PORT3.SA_Mes_6", "PORT3.SA_Mes_7", "PORT3.SA_Mes_8", "PORT3.SA_Mes_9", "PORT3.SA_Mes_10", "PORT3.SA_Mes_11", "PORT3.SA_Mes_12", "PORT3.SA_Quarter_1", "PORT3.SA_Quarter_2", "PORT3.SA_Quarter_3", "PORT3.SA_Quarter_4", "PORT3.SA_Last_Day_Quarter", "PORT3.SA_Pre_Feriado_Brasil", "PORT3.SA_Media_OHLC_Anterior", "PORT3.SA_Sinal_Compra", "PORT3.SA_Sinal_Venda", "PORT3.SA_Media_OHLC_Futura", "PORT3.SA_Open_PctChange_Lag_1", "PORT3.SA_High_PctChange_Lag_1", "PORT3.SA_Low_PctChange_Lag_1", "PORT3.SA_Close_PctChange_Lag_1", "PORT3.SA_Open_PctChange_Lag_2", "PORT3.SA_High_PctChange_Lag_2", "PORT3.SA_Low_PctChange_Lag_2", "PORT3.SA_Close_PctChange_Lag_2", "PORT3.SA_Open_PctChange_Lag_3", "PORT3.SA_High_PctChange_Lag_3", "PORT3.SA_Low_PctChange_Lag_3", "PORT3.SA_Close_PctChange_Lag_3", "PORT3.SA_Open_PctChange_Lag_4", "PORT3.SA_High_PctChange_Lag_4", "PORT3.SA_Low_PctChange_Lag_4", "PORT3.SA_Close_PctChange_Lag_4", "PORT3.SA_Open_PctChange_Lag_5", "PORT3.SA_High_PctChange_Lag_5", "PORT3.SA_Low_PctChange_Lag_5", "PORT3.SA_Close_PctChange_Lag_5", "PORT3.SA_Open_PctChange_Lag_6", "PORT3.SA_High_PctChange_Lag_6", "PORT3.SA_Low_PctChange_Lag_6", "PORT3.SA_Close_PctChange_Lag_6", "PORT3.SA_Open_PctChange_Lag_7", "PORT3.SA_High_PctChange_Lag_7", "PORT3.SA_Low_PctChange_Lag_7", "PORT3.SA_Close_PctChange_Lag_7", "PORT3.SA_Open_PctChange_Lag_8", "PORT3.SA_High_PctChange_Lag_8", "PORT3.SA_Low_PctChange_Lag_8", "PORT3.SA_Close_PctChange_Lag_8", "PORT3.SA_Open_PctChange_Lag_9", "PORT3.SA_High_PctChange_Lag_9", "PORT3.SA_Low_PctChange_Lag_9", "PORT3.SA_Close_PctChange_Lag_9", "PORT3.SA_Open_PctChange_Lag_10", "PORT3.SA_High_PctChange_Lag_10", "PORT3.SA_Low_PctChange_Lag_10", "PORT3.SA_Close_PctChange_Lag_10", "PORT3.SA_MM10_PctChange", "PORT3.SA_Diff_OHLC_MM10", "PORT3.SA_MM25_PctChange", "PORT3.SA_Diff_OHLC_MM25", "PORT3.SA_MM100_PctChange", "PORT3.SA_Diff_OHLC_MM100", "PORT3.SA_MM10_Menos_MM25", "PORT3.SA_MM25_Menos_MM100", "PORT3.SA_MM10_Menos_MM100", "PORT3.SA_Volume_Lag_1", "PORT3.SA_Volume_Lag_2", "PORT3.SA_Volume_Lag_3", "PORT3.SA_Volume_Lag_4", "PORT3.SA_Volume_Lag_5", "PORT3.SA_Spread_Lag_1", "PORT3.SA_Spread_Lag_2", "PORT3.SA_Spread_Lag_3", "PORT3.SA_Spread_Lag_4", "PORT3.SA_Spread_Lag_5", "PORT3.SA_Volatilidade_Lag_1", "PORT3.SA_Volatilidade_Lag_2", "PORT3.SA_Volatilidade_Lag_3", "PORT3.SA_Volatilidade_Lag_4", "PORT3.SA_Volatilidade_Lag_5", "PORT3.SA_Parkinson_Volatility_Lag_1", "PORT3.<PERSON>_Parkinson_Volatility_Lag_2", "PORT3.SA_Parkinson_Volatility_Lag_3", "PORT3.SA_Parkinson_Volatility_Lag_4", "PORT3.<PERSON>_Parkinson_Volatility_Lag_5", "PORT3.SA_EMV_Lag_1", "PORT3.SA_EMV_Lag_2", "PORT3.SA_EMV_Lag_3", "PORT3.SA_EMV_Lag_4", "PORT3.SA_EMV_Lag_5", "PORT3.SA_EMV_MA_Lag_1", "PORT3.SA_EMV_MA_Lag_2", "PORT3.SA_EMV_MA_Lag_3", "PORT3.SA_EMV_MA_Lag_4", "PORT3.SA_EMV_MA_Lag_5", "PORT3.SA_VO_Lag_1", "PORT3.SA_VO_Lag_2", "PORT3.SA_VO_Lag_3", "PORT3.SA_VO_Lag_4", "PORT3.SA_VO_Lag_5", "PORT3.SA_High_Max_50_Lag_1", "PORT3.SA_High_Max_50_Lag_2", "PORT3.SA_High_Max_50_Lag_3", "PORT3.SA_High_Max_50_Lag_4", "PORT3.SA_High_Max_50_Lag_5", "PORT3.SA_Low_Min_50_Lag_1", "PORT3.SA_Low_Min_50_Lag_2", "PORT3.SA_Low_Min_50_Lag_3", "PORT3.SA_Low_Min_50_Lag_4", "PORT3.SA_Low_Min_50_Lag_5", "PORT3.SA_MFI_Lag_1", "PORT3.SA_MFI_Lag_2", "PORT3.SA_MFI_Lag_3", "PORT3.SA_MFI_Lag_4", "PORT3.SA_MFI_Lag_5", "PORT3.SA_Amihud_Lag_1", "PORT3.SA_Amihud_Lag_2", "PORT3.SA_Amihud_Lag_3", "PORT3.SA_Amihud_Lag_4", "PORT3.SA_Amihud_Lag_5", "PORT3.SA_Roll_Spread_Lag_1", "PORT3.SA_Roll_Spread_Lag_2", "PORT3.SA_Roll_Spread_Lag_3", "PORT3.SA_Roll_Spread_Lag_4", "PORT3.SA_Roll_Spread_Lag_5", "PORT3.<PERSON>_Hu<PERSON>_Lag_1", "PORT3.<PERSON>_<PERSON><PERSON>_Lag_2", "PORT3.<PERSON>_<PERSON><PERSON>_Lag_3", "PORT3.<PERSON>_<PERSON><PERSON>_Lag_4", "PORT3.<PERSON>_<PERSON><PERSON>_Lag_5", "PORT3.SA_Vol_per_Volume_Lag_1", "PORT3.SA_Vol_per_Volume_Lag_2", "PORT3.SA_Vol_per_Volume_Lag_3", "PORT3.SA_Vol_per_Volume_Lag_4", "PORT3.SA_Vol_per_Volume_Lag_5", "PORT3.SA_CMF_Lag_1", "PORT3.SA_CMF_Lag_2", "PORT3.SA_CMF_Lag_3", "PORT3.SA_CMF_Lag_4", "PORT3.SA_CMF_Lag_5", "PORT3.SA_AD_Line_Lag_1", "PORT3.SA_AD_Line_Lag_2", "PORT3.SA_AD_Line_Lag_3", "PORT3.SA_AD_Line_Lag_4", "PORT3.SA_AD_Line_Lag_5", "MDIA3.SA_Open", "MDIA3.SA_High", "MDIA3.SA_Low", "MDIA3.SA_Close", "MDIA3.SA_Volume", "MDIA3.SA_Media_OHLC", "MDIA3.SA_MM10", "MDIA3.SA_MM25", "MDIA3.SA_MM100", "MDIA3.SA_Media_OHLC_PctChange", "MDIA3.SA_Open_PctChange", "MDIA3.SA_High_PctChange", "MDIA3.SA_Low_PctChange", "MDIA3.SA_Close_PctChange", "MDIA3.SA_Close_PctChange_Historical", "MDIA3.SA_Volatilidade", "MDIA3.SA_Spread", "MDIA3.SA_Parkinson_Volatility", "MDIA3.SA_MFI", "MDIA3.SA_MFI_Historical", "MDIA3.SA_EMV", "MDIA3.SA_EMV_MA", "MDIA3.SA_Amihud", "MDIA3.SA_Amihud_Historical", "MDIA3.SA_Roll_Spread", "MDIA3.SA_Roll_Spread_Historical", "MDIA3.SA_Hurst", "MDIA3.SA_<PERSON><PERSON>_Historical", "MDIA3.SA_Vol_per_Volume", "MDIA3.SA_Vol_per_Volume_Historical", "MDIA3.SA_CMF", "MDIA3.SA_CMF_Historical", "MDIA3.SA_AD_Line", "MDIA3.SA_AD_Line_Historical", "MDIA3.SA_VO", "MDIA3.SA_High_Max_50", "MDIA3.SA_Low_Min_50", "MDIA3.SA_Segunda", "MDIA3.SA_Terca", "MDIA3.SA_Quarta", "MDIA3.SA_Quinta", "MDIA3.SA_Sexta", "MDIA3.SA_Mes_1", "MDIA3.SA_Mes_2", "MDIA3.SA_Mes_3", "MDIA3.SA_Mes_4", "MDIA3.SA_Mes_5", "MDIA3.SA_Mes_6", "MDIA3.SA_Mes_7", "MDIA3.SA_Mes_8", "MDIA3.SA_Mes_9", "MDIA3.SA_Mes_10", "MDIA3.SA_Mes_11", "MDIA3.SA_Mes_12", "MDIA3.SA_Quarter_1", "MDIA3.SA_Quarter_2", "MDIA3.SA_Quarter_3", "MDIA3.SA_Quarter_4", "MDIA3.SA_Last_Day_Quarter", "MDIA3.SA_Pre_Feriado_Brasil", "MDIA3.SA_Media_OHLC_Anterior", "MDIA3.SA_Sinal_Compra", "MDIA3.SA_Sinal_Venda", "MDIA3.SA_Media_OHLC_Futura", "MDIA3.SA_Open_PctChange_Lag_1", "MDIA3.SA_High_PctChange_Lag_1", "MDIA3.SA_Low_PctChange_Lag_1", "MDIA3.SA_Close_PctChange_Lag_1", "MDIA3.SA_Open_PctChange_Lag_2", "MDIA3.SA_High_PctChange_Lag_2", "MDIA3.SA_Low_PctChange_Lag_2", "MDIA3.SA_Close_PctChange_Lag_2", "MDIA3.SA_Open_PctChange_Lag_3", "MDIA3.SA_High_PctChange_Lag_3", "MDIA3.SA_Low_PctChange_Lag_3", "MDIA3.SA_Close_PctChange_Lag_3", "MDIA3.SA_Open_PctChange_Lag_4", "MDIA3.SA_High_PctChange_Lag_4", "MDIA3.SA_Low_PctChange_Lag_4", "MDIA3.SA_Close_PctChange_Lag_4", "MDIA3.SA_Open_PctChange_Lag_5", "MDIA3.SA_High_PctChange_Lag_5", "MDIA3.SA_Low_PctChange_Lag_5", "MDIA3.SA_Close_PctChange_Lag_5", "MDIA3.SA_Open_PctChange_Lag_6", "MDIA3.SA_High_PctChange_Lag_6", "MDIA3.SA_Low_PctChange_Lag_6", "MDIA3.SA_Close_PctChange_Lag_6", "MDIA3.SA_Open_PctChange_Lag_7", "MDIA3.SA_High_PctChange_Lag_7", "MDIA3.SA_Low_PctChange_Lag_7", "MDIA3.SA_Close_PctChange_Lag_7", "MDIA3.SA_Open_PctChange_Lag_8", "MDIA3.SA_High_PctChange_Lag_8", "MDIA3.SA_Low_PctChange_Lag_8", "MDIA3.SA_Close_PctChange_Lag_8", "MDIA3.SA_Open_PctChange_Lag_9", "MDIA3.SA_High_PctChange_Lag_9", "MDIA3.SA_Low_PctChange_Lag_9", "MDIA3.SA_Close_PctChange_Lag_9", "MDIA3.SA_Open_PctChange_Lag_10", "MDIA3.SA_High_PctChange_Lag_10", "MDIA3.SA_Low_PctChange_Lag_10", "MDIA3.SA_Close_PctChange_Lag_10", "MDIA3.SA_MM10_PctChange", "MDIA3.SA_Diff_OHLC_MM10", "MDIA3.SA_MM25_PctChange", "MDIA3.SA_Diff_OHLC_MM25", "MDIA3.SA_MM100_PctChange", "MDIA3.SA_Diff_OHLC_MM100", "MDIA3.SA_MM10_Menos_MM25", "MDIA3.SA_MM25_Menos_MM100", "MDIA3.SA_MM10_Menos_MM100", "MDIA3.SA_Volume_Lag_1", "MDIA3.SA_Volume_Lag_2", "MDIA3.SA_Volume_Lag_3", "MDIA3.SA_Volume_Lag_4", "MDIA3.SA_Volume_Lag_5", "MDIA3.SA_Spread_Lag_1", "MDIA3.SA_Spread_Lag_2", "MDIA3.SA_Spread_Lag_3", "MDIA3.SA_Spread_Lag_4", "MDIA3.SA_Spread_Lag_5", "MDIA3.SA_Volatilidade_Lag_1", "MDIA3.SA_Volatilidade_Lag_2", "MDIA3.SA_Volatilidade_Lag_3", "MDIA3.SA_Volatilidade_Lag_4", "MDIA3.SA_Volatilidade_Lag_5", "MDIA3.SA_Parkinson_Volatility_Lag_1", "MDIA3.SA_Parkinson_Volatility_Lag_2", "MDIA3.SA_Parkinson_Volatility_Lag_3", "MDIA3.SA_Parkinson_Volatility_Lag_4", "MDIA3.SA_Parkinson_Volatility_Lag_5", "MDIA3.SA_EMV_Lag_1", "MDIA3.SA_EMV_Lag_2", "MDIA3.SA_EMV_Lag_3", "MDIA3.SA_EMV_Lag_4", "MDIA3.SA_EMV_Lag_5", "MDIA3.SA_EMV_MA_Lag_1", "MDIA3.SA_EMV_MA_Lag_2", "MDIA3.SA_EMV_MA_Lag_3", "MDIA3.SA_EMV_MA_Lag_4", "MDIA3.SA_EMV_MA_Lag_5", "MDIA3.SA_VO_Lag_1", "MDIA3.SA_VO_Lag_2", "MDIA3.SA_VO_Lag_3", "MDIA3.SA_VO_Lag_4", "MDIA3.SA_VO_Lag_5", "MDIA3.SA_High_Max_50_Lag_1", "MDIA3.SA_High_Max_50_Lag_2", "MDIA3.SA_High_Max_50_Lag_3", "MDIA3.SA_High_Max_50_Lag_4", "MDIA3.SA_High_Max_50_Lag_5", "MDIA3.SA_Low_Min_50_Lag_1", "MDIA3.SA_Low_Min_50_Lag_2", "MDIA3.SA_Low_Min_50_Lag_3", "MDIA3.SA_Low_Min_50_Lag_4", "MDIA3.SA_Low_Min_50_Lag_5", "MDIA3.SA_MFI_Lag_1", "MDIA3.SA_MFI_Lag_2", "MDIA3.SA_MFI_Lag_3", "MDIA3.SA_MFI_Lag_4", "MDIA3.SA_MFI_Lag_5", "MDIA3.SA_Amihud_Lag_1", "MDIA3.SA_Amihud_Lag_2", "MDIA3.SA_Amihud_Lag_3", "MDIA3.SA_Amihud_Lag_4", "MDIA3.SA_Amihud_Lag_5", "MDIA3.SA_Roll_Spread_Lag_1", "MDIA3.SA_Roll_Spread_Lag_2", "MDIA3.SA_Roll_Spread_Lag_3", "MDIA3.SA_Roll_Spread_Lag_4", "MDIA3.SA_Roll_Spread_Lag_5", "MDIA3.SA_Hurst_Lag_1", "MDIA3.SA_Hurst_Lag_2", "MDIA3.SA_Hurst_Lag_3", "MDIA3.SA_<PERSON>rst_Lag_4", "MDIA3.<PERSON>_<PERSON><PERSON>_Lag_5", "MDIA3.SA_Vol_per_Volume_Lag_1", "MDIA3.SA_Vol_per_Volume_Lag_2", "MDIA3.SA_Vol_per_Volume_Lag_3", "MDIA3.SA_Vol_per_Volume_Lag_4", "MDIA3.SA_Vol_per_Volume_Lag_5", "MDIA3.SA_CMF_Lag_1", "MDIA3.SA_CMF_Lag_2", "MDIA3.SA_CMF_Lag_3", "MDIA3.SA_CMF_Lag_4", "MDIA3.SA_CMF_Lag_5", "MDIA3.SA_AD_Line_Lag_1", "MDIA3.SA_AD_Line_Lag_2", "MDIA3.SA_AD_Line_Lag_3", "MDIA3.SA_AD_Line_Lag_4", "MDIA3.SA_AD_Line_Lag_5", "PETR3.SA_Open", "PETR3.SA_High", "PETR3.SA_Low", "PETR3.SA_Close", "PETR3.SA_Volume", "PETR3.SA_Media_OHLC", "PETR3.SA_MM10", "PETR3.SA_MM25", "PETR3.SA_MM100", "PETR3.SA_Media_OHLC_PctChange", "PETR3.SA_Open_PctChange", "PETR3.SA_High_PctChange", "PETR3.SA_Low_PctChange", "PETR3.SA_Close_PctChange", "PETR3.SA_Close_PctChange_Historical", "PETR3.SA_Volatilidade", "PETR3.SA_Spread", "PETR3.SA_Parkinson_Volatility", "PETR3.SA_MFI", "PETR3.SA_MFI_Historical", "PETR3.SA_EMV", "PETR3.SA_EMV_MA", "PETR3.SA_Amihud", "PETR3.SA_Amihud_Historical", "PETR3.SA_Roll_Spread", "PETR3.SA_Roll_Spread_Historical", "PETR3.SA_Hurst", "PETR3.SA_<PERSON><PERSON>_Historical", "PETR3.SA_Vol_per_Volume", "PETR3.SA_Vol_per_Volume_Historical", "PETR3.SA_CMF", "PETR3.SA_CMF_Historical", "PETR3.SA_AD_Line", "PETR3.SA_AD_Line_Historical", "PETR3.SA_VO", "PETR3.SA_High_Max_50", "PETR3.SA_Low_Min_50", "PETR3.SA_Segunda", "PETR3.SA_Terca", "PETR3.SA_Quarta", "PETR3.SA_Quinta", "PETR3.SA_Sexta", "PETR3.SA_Mes_1", "PETR3.SA_Mes_2", "PETR3.SA_Mes_3", "PETR3.SA_Mes_4", "PETR3.SA_Mes_5", "PETR3.SA_Mes_6", "PETR3.SA_Mes_7", "PETR3.SA_Mes_8", "PETR3.SA_Mes_9", "PETR3.SA_Mes_10", "PETR3.SA_Mes_11", "PETR3.SA_Mes_12", "PETR3.SA_Quarter_1", "PETR3.SA_Quarter_2", "PETR3.SA_Quarter_3", "PETR3.SA_Quarter_4", "PETR3.SA_Last_Day_Quarter", "PETR3.SA_Pre_Feriado_Brasil", "PETR3.SA_Media_OHLC_Anterior", "PETR3.SA_Sinal_Compra", "PETR3.SA_Sinal_Venda", "PETR3.SA_Media_OHLC_Futura", "PETR3.SA_Open_PctChange_Lag_1", "PETR3.SA_High_PctChange_Lag_1", "PETR3.SA_Low_PctChange_Lag_1", "PETR3.SA_Close_PctChange_Lag_1", "PETR3.SA_Open_PctChange_Lag_2", "PETR3.SA_High_PctChange_Lag_2", "PETR3.SA_Low_PctChange_Lag_2", "PETR3.SA_Close_PctChange_Lag_2", "PETR3.SA_Open_PctChange_Lag_3", "PETR3.SA_High_PctChange_Lag_3", "PETR3.SA_Low_PctChange_Lag_3", "PETR3.SA_Close_PctChange_Lag_3", "PETR3.SA_Open_PctChange_Lag_4", "PETR3.SA_High_PctChange_Lag_4", "PETR3.SA_Low_PctChange_Lag_4", "PETR3.SA_Close_PctChange_Lag_4", "PETR3.SA_Open_PctChange_Lag_5", "PETR3.SA_High_PctChange_Lag_5", "PETR3.SA_Low_PctChange_Lag_5", "PETR3.SA_Close_PctChange_Lag_5", "PETR3.SA_Open_PctChange_Lag_6", "PETR3.SA_High_PctChange_Lag_6", "PETR3.SA_Low_PctChange_Lag_6", "PETR3.SA_Close_PctChange_Lag_6", "PETR3.SA_Open_PctChange_Lag_7", "PETR3.SA_High_PctChange_Lag_7", "PETR3.SA_Low_PctChange_Lag_7", "PETR3.SA_Close_PctChange_Lag_7", "PETR3.SA_Open_PctChange_Lag_8", "PETR3.SA_High_PctChange_Lag_8", "PETR3.SA_Low_PctChange_Lag_8", "PETR3.SA_Close_PctChange_Lag_8", "PETR3.SA_Open_PctChange_Lag_9", "PETR3.SA_High_PctChange_Lag_9", "PETR3.SA_Low_PctChange_Lag_9", "PETR3.SA_Close_PctChange_Lag_9", "PETR3.SA_Open_PctChange_Lag_10", "PETR3.SA_High_PctChange_Lag_10", "PETR3.SA_Low_PctChange_Lag_10", "PETR3.SA_Close_PctChange_Lag_10", "PETR3.SA_MM10_PctChange", "PETR3.SA_Diff_OHLC_MM10", "PETR3.SA_MM25_PctChange", "PETR3.SA_Diff_OHLC_MM25", "PETR3.SA_MM100_PctChange", "PETR3.SA_Diff_OHLC_MM100", "PETR3.SA_MM10_Menos_MM25", "PETR3.SA_MM25_Menos_MM100", "PETR3.SA_MM10_Menos_MM100", "PETR3.SA_Volume_Lag_1", "PETR3.SA_Volume_Lag_2", "PETR3.SA_Volume_Lag_3", "PETR3.SA_Volume_Lag_4", "PETR3.SA_Volume_Lag_5", "PETR3.SA_Spread_Lag_1", "PETR3.SA_Spread_Lag_2", "PETR3.SA_Spread_Lag_3", "PETR3.SA_Spread_Lag_4", "PETR3.SA_Spread_Lag_5", "PETR3.SA_Volatilidade_Lag_1", "PETR3.SA_Volatilidade_Lag_2", "PETR3.SA_Volatilidade_Lag_3", "PETR3.SA_Volatilidade_Lag_4", "PETR3.SA_Volatilidade_Lag_5", "PETR3.SA_Parkinson_Volatility_Lag_1", "PETR3.<PERSON>_Parkinson_Volatility_Lag_2", "PETR3.SA_Parkinson_Volatility_Lag_3", "PETR3.SA_Parkinson_Volatility_Lag_4", "PETR3.<PERSON>_Parkinson_Volatility_Lag_5", "PETR3.SA_EMV_Lag_1", "PETR3.SA_EMV_Lag_2", "PETR3.SA_EMV_Lag_3", "PETR3.SA_EMV_Lag_4", "PETR3.SA_EMV_Lag_5", "PETR3.SA_EMV_MA_Lag_1", "PETR3.SA_EMV_MA_Lag_2", "PETR3.SA_EMV_MA_Lag_3", "PETR3.SA_EMV_MA_Lag_4", "PETR3.SA_EMV_MA_Lag_5", "PETR3.SA_VO_Lag_1", "PETR3.SA_VO_Lag_2", "PETR3.SA_VO_Lag_3", "PETR3.SA_VO_Lag_4", "PETR3.SA_VO_Lag_5", "PETR3.SA_High_Max_50_Lag_1", "PETR3.SA_High_Max_50_Lag_2", "PETR3.SA_High_Max_50_Lag_3", "PETR3.SA_High_Max_50_Lag_4", "PETR3.SA_High_Max_50_Lag_5", "PETR3.SA_Low_Min_50_Lag_1", "PETR3.SA_Low_Min_50_Lag_2", "PETR3.SA_Low_Min_50_Lag_3", "PETR3.SA_Low_Min_50_Lag_4", "PETR3.SA_Low_Min_50_Lag_5", "PETR3.SA_MFI_Lag_1", "PETR3.SA_MFI_Lag_2", "PETR3.SA_MFI_Lag_3", "PETR3.SA_MFI_Lag_4", "PETR3.SA_MFI_Lag_5", "PETR3.SA_Amihud_Lag_1", "PETR3.SA_Amihud_Lag_2", "PETR3.SA_Amihud_Lag_3", "PETR3.SA_Amihud_Lag_4", "PETR3.SA_Amihud_Lag_5", "PETR3.SA_Roll_Spread_Lag_1", "PETR3.SA_Roll_Spread_Lag_2", "PETR3.SA_Roll_Spread_Lag_3", "PETR3.SA_Roll_Spread_Lag_4", "PETR3.SA_Roll_Spread_Lag_5", "PETR3.<PERSON>_Hurst_Lag_1", "PETR3.<PERSON>_<PERSON><PERSON>_Lag_2", "PETR3.<PERSON>_<PERSON><PERSON>_Lag_3", "PETR3.<PERSON>_<PERSON><PERSON>_Lag_4", "PETR3.<PERSON>_<PERSON><PERSON>_Lag_5", "PETR3.SA_Vol_per_Volume_Lag_1", "PETR3.SA_Vol_per_Volume_Lag_2", "PETR3.SA_Vol_per_Volume_Lag_3", "PETR3.SA_Vol_per_Volume_Lag_4", "PETR3.SA_Vol_per_Volume_Lag_5", "PETR3.SA_CMF_Lag_1", "PETR3.SA_CMF_Lag_2", "PETR3.SA_CMF_Lag_3", "PETR3.SA_CMF_Lag_4", "PETR3.SA_CMF_Lag_5", "PETR3.SA_AD_Line_Lag_1", "PETR3.SA_AD_Line_Lag_2", "PETR3.SA_AD_Line_Lag_3", "PETR3.SA_AD_Line_Lag_4", "PETR3.SA_AD_Line_Lag_5", "ODPV3.SA_Open", "ODPV3.SA_High", "ODPV3.SA_Low", "ODPV3.SA_Close", "ODPV3.SA_Volume", "ODPV3.SA_Media_OHLC", "ODPV3.SA_MM10", "ODPV3.SA_MM25", "ODPV3.SA_MM100", "ODPV3.SA_Media_OHLC_PctChange", "ODPV3.SA_Open_PctChange", "ODPV3.SA_High_PctChange", "ODPV3.SA_Low_PctChange", "ODPV3.SA_Close_PctChange", "ODPV3.SA_Close_PctChange_Historical", "ODPV3.SA_Volatilidade", "ODPV3.SA_Spread", "ODPV3.SA_Parkinson_Volatility", "ODPV3.SA_MFI", "ODPV3.SA_MFI_Historical", "ODPV3.SA_EMV", "ODPV3.SA_EMV_MA", "ODPV3.SA_Amihud", "ODPV3.SA_Amihud_Historical", "ODPV3.SA_Roll_Spread", "ODPV3.SA_Roll_Spread_Historical", "ODPV3.SA_Hurst", "ODPV3.SA_Hurst_Historical", "ODPV3.SA_Vol_per_Volume", "ODPV3.SA_Vol_per_Volume_Historical", "ODPV3.SA_CMF", "ODPV3.SA_CMF_Historical", "ODPV3.SA_AD_Line", "ODPV3.SA_AD_Line_Historical", "ODPV3.SA_VO", "ODPV3.SA_High_Max_50", "ODPV3.SA_Low_Min_50", "ODPV3.SA_Segunda", "ODPV3.SA_Terca", "ODPV3.SA_Quarta", "ODPV3.SA_Quinta", "ODPV3.SA_Sexta", "ODPV3.SA_Mes_1", "ODPV3.SA_Mes_2", "ODPV3.SA_Mes_3", "ODPV3.SA_Mes_4", "ODPV3.SA_Mes_5", "ODPV3.SA_Mes_6", "ODPV3.SA_Mes_7", "ODPV3.SA_Mes_8", "ODPV3.SA_Mes_9", "ODPV3.SA_Mes_10", "ODPV3.SA_Mes_11", "ODPV3.SA_Mes_12", "ODPV3.SA_Quarter_1", "ODPV3.SA_Quarter_2", "ODPV3.SA_Quarter_3", "ODPV3.SA_Quarter_4", "ODPV3.SA_Last_Day_Quarter", "ODPV3.SA_Pre_Feriado_Brasil", "ODPV3.SA_Media_OHLC_Anterior", "ODPV3.SA_Sinal_Compra", "ODPV3.SA_Sinal_Venda", "ODPV3.SA_Media_OHLC_Futura", "ODPV3.SA_Open_PctChange_Lag_1", "ODPV3.SA_High_PctChange_Lag_1", "ODPV3.SA_Low_PctChange_Lag_1", "ODPV3.SA_Close_PctChange_Lag_1", "ODPV3.SA_Open_PctChange_Lag_2", "ODPV3.SA_High_PctChange_Lag_2", "ODPV3.SA_Low_PctChange_Lag_2", "ODPV3.SA_Close_PctChange_Lag_2", "ODPV3.SA_Open_PctChange_Lag_3", "ODPV3.SA_High_PctChange_Lag_3", "ODPV3.SA_Low_PctChange_Lag_3", "ODPV3.SA_Close_PctChange_Lag_3", "ODPV3.SA_Open_PctChange_Lag_4", "ODPV3.SA_High_PctChange_Lag_4", "ODPV3.SA_Low_PctChange_Lag_4", "ODPV3.SA_Close_PctChange_Lag_4", "ODPV3.SA_Open_PctChange_Lag_5", "ODPV3.SA_High_PctChange_Lag_5", "ODPV3.SA_Low_PctChange_Lag_5", "ODPV3.SA_Close_PctChange_Lag_5", "ODPV3.SA_Open_PctChange_Lag_6", "ODPV3.SA_High_PctChange_Lag_6", "ODPV3.SA_Low_PctChange_Lag_6", "ODPV3.SA_Close_PctChange_Lag_6", "ODPV3.SA_Open_PctChange_Lag_7", "ODPV3.SA_High_PctChange_Lag_7", "ODPV3.SA_Low_PctChange_Lag_7", "ODPV3.SA_Close_PctChange_Lag_7", "ODPV3.SA_Open_PctChange_Lag_8", "ODPV3.SA_High_PctChange_Lag_8", "ODPV3.SA_Low_PctChange_Lag_8", "ODPV3.SA_Close_PctChange_Lag_8", "ODPV3.SA_Open_PctChange_Lag_9", "ODPV3.SA_High_PctChange_Lag_9", "ODPV3.SA_Low_PctChange_Lag_9", "ODPV3.SA_Close_PctChange_Lag_9", "ODPV3.SA_Open_PctChange_Lag_10", "ODPV3.SA_High_PctChange_Lag_10", "ODPV3.SA_Low_PctChange_Lag_10", "ODPV3.SA_Close_PctChange_Lag_10", "ODPV3.SA_MM10_PctChange", "ODPV3.SA_Diff_OHLC_MM10", "ODPV3.SA_MM25_PctChange", "ODPV3.SA_Diff_OHLC_MM25", "ODPV3.SA_MM100_PctChange", "ODPV3.SA_Diff_OHLC_MM100", "ODPV3.SA_MM10_Menos_MM25", "ODPV3.SA_MM25_Menos_MM100", "ODPV3.SA_MM10_Menos_MM100", "ODPV3.SA_Volume_Lag_1", "ODPV3.SA_Volume_Lag_2", "ODPV3.SA_Volume_Lag_3", "ODPV3.SA_Volume_Lag_4", "ODPV3.SA_Volume_Lag_5", "ODPV3.SA_Spread_Lag_1", "ODPV3.SA_Spread_Lag_2", "ODPV3.SA_Spread_Lag_3", "ODPV3.SA_Spread_Lag_4", "ODPV3.SA_Spread_Lag_5", "ODPV3.SA_Volatilidade_Lag_1", "ODPV3.SA_Volatilidade_Lag_2", "ODPV3.SA_Volatilidade_Lag_3", "ODPV3.SA_Volatilidade_Lag_4", "ODPV3.SA_Volatilidade_Lag_5", "ODPV3.SA_Parkinson_Volatility_Lag_1", "ODPV3.SA_Parkinson_Volatility_Lag_2", "ODPV3.SA_Parkinson_Volatility_Lag_3", "ODPV3.SA_Parkinson_Volatility_Lag_4", "ODPV3.SA_Parkinson_Volatility_Lag_5", "ODPV3.SA_EMV_Lag_1", "ODPV3.SA_EMV_Lag_2", "ODPV3.SA_EMV_Lag_3", "ODPV3.SA_EMV_Lag_4", "ODPV3.SA_EMV_Lag_5", "ODPV3.SA_EMV_MA_Lag_1", "ODPV3.SA_EMV_MA_Lag_2", "ODPV3.SA_EMV_MA_Lag_3", "ODPV3.SA_EMV_MA_Lag_4", "ODPV3.SA_EMV_MA_Lag_5", "ODPV3.SA_VO_Lag_1", "ODPV3.SA_VO_Lag_2", "ODPV3.SA_VO_Lag_3", "ODPV3.SA_VO_Lag_4", "ODPV3.SA_VO_Lag_5", "ODPV3.SA_High_Max_50_Lag_1", "ODPV3.SA_High_Max_50_Lag_2", "ODPV3.SA_High_Max_50_Lag_3", "ODPV3.SA_High_Max_50_Lag_4", "ODPV3.SA_High_Max_50_Lag_5", "ODPV3.SA_Low_Min_50_Lag_1", "ODPV3.SA_Low_Min_50_Lag_2", "ODPV3.SA_Low_Min_50_Lag_3", "ODPV3.SA_Low_Min_50_Lag_4", "ODPV3.SA_Low_Min_50_Lag_5", "ODPV3.SA_MFI_Lag_1", "ODPV3.SA_MFI_Lag_2", "ODPV3.SA_MFI_Lag_3", "ODPV3.SA_MFI_Lag_4", "ODPV3.SA_MFI_Lag_5", "ODPV3.SA_Amihud_Lag_1", "ODPV3.SA_Amihud_Lag_2", "ODPV3.SA_Amihud_Lag_3", "ODPV3.SA_Amihud_Lag_4", "ODPV3.SA_Amihud_Lag_5", "ODPV3.SA_Roll_Spread_Lag_1", "ODPV3.SA_Roll_Spread_Lag_2", "ODPV3.SA_Roll_Spread_Lag_3", "ODPV3.SA_Roll_Spread_Lag_4", "ODPV3.SA_Roll_Spread_Lag_5", "ODPV3.SA_Hurst_Lag_1", "ODPV3.SA_Hurst_Lag_2", "ODPV3.SA_Hurst_Lag_3", "ODPV3.SA_<PERSON>rst_Lag_4", "ODPV3.SA_<PERSON><PERSON>_Lag_5", "ODPV3.SA_Vol_per_Volume_Lag_1", "ODPV3.SA_Vol_per_Volume_Lag_2", "ODPV3.SA_Vol_per_Volume_Lag_3", "ODPV3.SA_Vol_per_Volume_Lag_4", "ODPV3.SA_Vol_per_Volume_Lag_5", "ODPV3.SA_CMF_Lag_1", "ODPV3.SA_CMF_Lag_2", "ODPV3.SA_CMF_Lag_3", "ODPV3.SA_CMF_Lag_4", "ODPV3.SA_CMF_Lag_5", "ODPV3.SA_AD_Line_Lag_1", "ODPV3.SA_AD_Line_Lag_2", "ODPV3.SA_AD_Line_Lag_3", "ODPV3.SA_AD_Line_Lag_4", "ODPV3.SA_AD_Line_Lag_5", "GGBR4.SA_Open", "GGBR4.SA_High", "GGBR4.SA_Low", "GGBR4.SA_Close", "GGBR4.SA_Volume", "GGBR4.SA_Media_OHLC", "GGBR4.SA_MM10", "GGBR4.SA_MM25", "GGBR4.SA_MM100", "GGBR4.SA_Media_OHLC_PctChange", "GGBR4.SA_Open_PctChange", "GGBR4.SA_High_PctChange", "GGBR4.SA_Low_PctChange", "GGBR4.SA_Close_PctChange", "GGBR4.SA_Close_PctChange_Historical", "GGBR4.SA_Volatilidade", "GGBR4.SA_Spread", "GGBR4.SA_Parkinson_Volatility", "GGBR4.SA_MFI", "GGBR4.SA_MFI_Historical", "GGBR4.SA_EMV", "GGBR4.SA_EMV_MA", "GGBR4.SA_Amihud", "GGBR4.SA_Amihud_Historical", "GGBR4.SA_Roll_Spread", "GGBR4.SA_Roll_Spread_Historical", "GGBR4.SA_Hurst", "GGBR4.SA_Hurst_Historical", "GGBR4.SA_Vol_per_Volume", "GGBR4.SA_Vol_per_Volume_Historical", "GGBR4.SA_CMF", "GGBR4.SA_CMF_Historical", "GGBR4.SA_AD_Line", "GGBR4.SA_AD_Line_Historical", "GGBR4.SA_VO", "GGBR4.SA_High_Max_50", "GGBR4.SA_Low_Min_50", "GGBR4.SA_Segunda", "GGBR4.SA_Terca", "GGBR4.SA_Quarta", "GGBR4.SA_Quinta", "GGBR4.SA_Sexta", "GGBR4.SA_Mes_1", "GGBR4.SA_Mes_2", "GGBR4.SA_Mes_3", "GGBR4.SA_Mes_4", "GGBR4.SA_Mes_5", "GGBR4.SA_Mes_6", "GGBR4.SA_Mes_7", "GGBR4.SA_Mes_8", "GGBR4.SA_Mes_9", "GGBR4.SA_Mes_10", "GGBR4.SA_Mes_11", "GGBR4.SA_Mes_12", "GGBR4.SA_Quarter_1", "GGBR4.SA_Quarter_2", "GGBR4.SA_Quarter_3", "GGBR4.SA_Quarter_4", "GGBR4.SA_Last_Day_Quarter", "GGBR4.SA_Pre_Feriado_Brasil", "GGBR4.SA_Media_OHLC_Anterior", "GGBR4.SA_Sinal_Compra", "GGBR4.SA_Sinal_Venda", "GGBR4.SA_Media_OHLC_Futura", "GGBR4.SA_Open_PctChange_Lag_1", "GGBR4.SA_High_PctChange_Lag_1", "GGBR4.SA_Low_PctChange_Lag_1", "GGBR4.SA_Close_PctChange_Lag_1", "GGBR4.SA_Open_PctChange_Lag_2", "GGBR4.SA_High_PctChange_Lag_2", "GGBR4.SA_Low_PctChange_Lag_2", "GGBR4.SA_Close_PctChange_Lag_2", "GGBR4.SA_Open_PctChange_Lag_3", "GGBR4.SA_High_PctChange_Lag_3", "GGBR4.SA_Low_PctChange_Lag_3", "GGBR4.SA_Close_PctChange_Lag_3", "GGBR4.SA_Open_PctChange_Lag_4", "GGBR4.SA_High_PctChange_Lag_4", "GGBR4.SA_Low_PctChange_Lag_4", "GGBR4.SA_Close_PctChange_Lag_4", "GGBR4.SA_Open_PctChange_Lag_5", "GGBR4.SA_High_PctChange_Lag_5", "GGBR4.SA_Low_PctChange_Lag_5", "GGBR4.SA_Close_PctChange_Lag_5", "GGBR4.SA_Open_PctChange_Lag_6", "GGBR4.SA_High_PctChange_Lag_6", "GGBR4.SA_Low_PctChange_Lag_6", "GGBR4.SA_Close_PctChange_Lag_6", "GGBR4.SA_Open_PctChange_Lag_7", "GGBR4.SA_High_PctChange_Lag_7", "GGBR4.SA_Low_PctChange_Lag_7", "GGBR4.SA_Close_PctChange_Lag_7", "GGBR4.SA_Open_PctChange_Lag_8", "GGBR4.SA_High_PctChange_Lag_8", "GGBR4.SA_Low_PctChange_Lag_8", "GGBR4.SA_Close_PctChange_Lag_8", "GGBR4.SA_Open_PctChange_Lag_9", "GGBR4.SA_High_PctChange_Lag_9", "GGBR4.SA_Low_PctChange_Lag_9", "GGBR4.SA_Close_PctChange_Lag_9", "GGBR4.SA_Open_PctChange_Lag_10", "GGBR4.SA_High_PctChange_Lag_10", "GGBR4.SA_Low_PctChange_Lag_10", "GGBR4.SA_Close_PctChange_Lag_10", "GGBR4.SA_MM10_PctChange", "GGBR4.SA_Diff_OHLC_MM10", "GGBR4.SA_MM25_PctChange", "GGBR4.SA_Diff_OHLC_MM25", "GGBR4.SA_MM100_PctChange", "GGBR4.SA_Diff_OHLC_MM100", "GGBR4.SA_MM10_Menos_MM25", "GGBR4.SA_MM25_Menos_MM100", "GGBR4.SA_MM10_Menos_MM100", "GGBR4.SA_Volume_Lag_1", "GGBR4.SA_Volume_Lag_2", "GGBR4.SA_Volume_Lag_3", "GGBR4.SA_Volume_Lag_4", "GGBR4.SA_Volume_Lag_5", "GGBR4.SA_Spread_Lag_1", "GGBR4.SA_Spread_Lag_2", "GGBR4.SA_Spread_Lag_3", "GGBR4.SA_Spread_Lag_4", "GGBR4.SA_Spread_Lag_5", "GGBR4.SA_Volatilidade_Lag_1", "GGBR4.SA_Volatilidade_Lag_2", "GGBR4.SA_Volatilidade_Lag_3", "GGBR4.SA_Volatilidade_Lag_4", "GGBR4.SA_Volatilidade_Lag_5", "GGBR4.SA_Parkinson_Volatility_Lag_1", "GGBR4.SA_Parkinson_Volatility_Lag_2", "GGBR4.SA_Parkinson_Volatility_Lag_3", "GGBR4.SA_Parkinson_Volatility_Lag_4", "GGBR4.SA_Parkinson_Volatility_Lag_5", "GGBR4.SA_EMV_Lag_1", "GGBR4.SA_EMV_Lag_2", "GGBR4.SA_EMV_Lag_3", "GGBR4.SA_EMV_Lag_4", "GGBR4.SA_EMV_Lag_5", "GGBR4.SA_EMV_MA_Lag_1", "GGBR4.SA_EMV_MA_Lag_2", "GGBR4.SA_EMV_MA_Lag_3", "GGBR4.SA_EMV_MA_Lag_4", "GGBR4.SA_EMV_MA_Lag_5", "GGBR4.SA_VO_Lag_1", "GGBR4.SA_VO_Lag_2", "GGBR4.SA_VO_Lag_3", "GGBR4.SA_VO_Lag_4", "GGBR4.SA_VO_Lag_5", "GGBR4.SA_High_Max_50_Lag_1", "GGBR4.SA_High_Max_50_Lag_2", "GGBR4.SA_High_Max_50_Lag_3", "GGBR4.SA_High_Max_50_Lag_4", "GGBR4.SA_High_Max_50_Lag_5", "GGBR4.SA_Low_Min_50_Lag_1", "GGBR4.SA_Low_Min_50_Lag_2", "GGBR4.SA_Low_Min_50_Lag_3", "GGBR4.SA_Low_Min_50_Lag_4", "GGBR4.SA_Low_Min_50_Lag_5", "GGBR4.SA_MFI_Lag_1", "GGBR4.SA_MFI_Lag_2", "GGBR4.SA_MFI_Lag_3", "GGBR4.SA_MFI_Lag_4", "GGBR4.SA_MFI_Lag_5", "GGBR4.SA_Amihud_Lag_1", "GGBR4.SA_Amihud_Lag_2", "GGBR4.SA_Amihud_Lag_3", "GGBR4.SA_Amihud_Lag_4", "GGBR4.SA_Amihud_Lag_5", "GGBR4.SA_Roll_Spread_Lag_1", "GGBR4.SA_Roll_Spread_Lag_2", "GGBR4.SA_Roll_Spread_Lag_3", "GGBR4.SA_Roll_Spread_Lag_4", "GGBR4.SA_Roll_Spread_Lag_5", "GGBR4.SA_Hurst_Lag_1", "GGBR4.<PERSON>_<PERSON>rst_Lag_2", "GGBR4.SA_<PERSON>rst_Lag_3", "GGBR4.<PERSON>_<PERSON>rst_Lag_4", "GGBR4.<PERSON>_<PERSON><PERSON>_Lag_5", "GGBR4.SA_Vol_per_Volume_Lag_1", "GGBR4.SA_Vol_per_Volume_Lag_2", "GGBR4.SA_Vol_per_Volume_Lag_3", "GGBR4.SA_Vol_per_Volume_Lag_4", "GGBR4.SA_Vol_per_Volume_Lag_5", "GGBR4.SA_CMF_Lag_1", "GGBR4.SA_CMF_Lag_2", "GGBR4.SA_CMF_Lag_3", "GGBR4.SA_CMF_Lag_4", "GGBR4.SA_CMF_Lag_5", "GGBR4.SA_AD_Line_Lag_1", "GGBR4.SA_AD_Line_Lag_2", "GGBR4.SA_AD_Line_Lag_3", "GGBR4.SA_AD_Line_Lag_4", "GGBR4.SA_AD_Line_Lag_5", "RADL3.SA_Open", "RADL3.SA_High", "RADL3.SA_Low", "RADL3.SA_Close", "RADL3.SA_Volume", "RADL3.SA_Media_OHLC", "RADL3.SA_MM10", "RADL3.SA_MM25", "RADL3.SA_MM100", "RADL3.SA_Media_OHLC_PctChange", "RADL3.SA_Open_PctChange", "RADL3.SA_High_PctChange", "RADL3.SA_Low_PctChange", "RADL3.SA_Close_PctChange", "RADL3.SA_Close_PctChange_Historical", "RADL3.SA_Volatilidade", "RADL3.SA_Spread", "RADL3.SA_Parkinson_Volatility", "RADL3.SA_MFI", "RADL3.SA_MFI_Historical", "RADL3.SA_EMV", "RADL3.SA_EMV_MA", "RADL3.SA_Amihud", "RADL3.SA_Amihud_Historical", "RADL3.SA_Roll_Spread", "RADL3.SA_Roll_Spread_Historical", "RADL3.SA_Hurst", "RADL3.<PERSON>_<PERSON><PERSON>_Historical", "RADL3.SA_Vol_per_Volume", "RADL3.SA_Vol_per_Volume_Historical", "RADL3.SA_CMF", "RADL3.SA_CMF_Historical", "RADL3.SA_AD_Line", "RADL3.SA_AD_Line_Historical", "RADL3.SA_VO", "RADL3.SA_High_Max_50", "RADL3.SA_Low_Min_50", "RADL3.SA_Segunda", "RADL3.SA_Terca", "RADL3.SA_Quarta", "RADL3.SA_Quinta", "RADL3.SA_Sexta", "RADL3.SA_Mes_1", "RADL3.SA_Mes_2", "RADL3.SA_Mes_3", "RADL3.SA_Mes_4", "RADL3.SA_Mes_5", "RADL3.SA_Mes_6", "RADL3.SA_Mes_7", "RADL3.SA_Mes_8", "RADL3.SA_Mes_9", "RADL3.SA_Mes_10", "RADL3.SA_Mes_11", "RADL3.SA_Mes_12", "RADL3.SA_Quarter_1", "RADL3.SA_Quarter_2", "RADL3.SA_Quarter_3", "RADL3.SA_Quarter_4", "RADL3.SA_Last_Day_Quarter", "RADL3.SA_Pre_Feriado_Brasil", "RADL3.SA_Media_OHLC_Anterior", "RADL3.SA_Sinal_Compra", "RADL3.SA_Sinal_Venda", "RADL3.SA_Media_OHLC_Futura", "RADL3.SA_Open_PctChange_Lag_1", "RADL3.SA_High_PctChange_Lag_1", "RADL3.SA_Low_PctChange_Lag_1", "RADL3.SA_Close_PctChange_Lag_1", "RADL3.SA_Open_PctChange_Lag_2", "RADL3.SA_High_PctChange_Lag_2", "RADL3.SA_Low_PctChange_Lag_2", "RADL3.SA_Close_PctChange_Lag_2", "RADL3.SA_Open_PctChange_Lag_3", "RADL3.SA_High_PctChange_Lag_3", "RADL3.SA_Low_PctChange_Lag_3", "RADL3.SA_Close_PctChange_Lag_3", "RADL3.SA_Open_PctChange_Lag_4", "RADL3.SA_High_PctChange_Lag_4", "RADL3.SA_Low_PctChange_Lag_4", "RADL3.SA_Close_PctChange_Lag_4", "RADL3.SA_Open_PctChange_Lag_5", "RADL3.SA_High_PctChange_Lag_5", "RADL3.SA_Low_PctChange_Lag_5", "RADL3.SA_Close_PctChange_Lag_5", "RADL3.SA_Open_PctChange_Lag_6", "RADL3.SA_High_PctChange_Lag_6", "RADL3.SA_Low_PctChange_Lag_6", "RADL3.SA_Close_PctChange_Lag_6", "RADL3.SA_Open_PctChange_Lag_7", "RADL3.SA_High_PctChange_Lag_7", "RADL3.SA_Low_PctChange_Lag_7", "RADL3.SA_Close_PctChange_Lag_7", "RADL3.SA_Open_PctChange_Lag_8", "RADL3.SA_High_PctChange_Lag_8", "RADL3.SA_Low_PctChange_Lag_8", "RADL3.SA_Close_PctChange_Lag_8", "RADL3.SA_Open_PctChange_Lag_9", "RADL3.SA_High_PctChange_Lag_9", "RADL3.SA_Low_PctChange_Lag_9", "RADL3.SA_Close_PctChange_Lag_9", "RADL3.SA_Open_PctChange_Lag_10", "RADL3.SA_High_PctChange_Lag_10", "RADL3.SA_Low_PctChange_Lag_10", "RADL3.SA_Close_PctChange_Lag_10", "RADL3.SA_MM10_PctChange", "RADL3.SA_Diff_OHLC_MM10", "RADL3.SA_MM25_PctChange", "RADL3.SA_Diff_OHLC_MM25", "RADL3.SA_MM100_PctChange", "RADL3.SA_Diff_OHLC_MM100", "RADL3.SA_MM10_Menos_MM25", "RADL3.SA_MM25_Menos_MM100", "RADL3.SA_MM10_Menos_MM100", "RADL3.SA_Volume_Lag_1", "RADL3.SA_Volume_Lag_2", "RADL3.SA_Volume_Lag_3", "RADL3.SA_Volume_Lag_4", "RADL3.SA_Volume_Lag_5", "RADL3.SA_Spread_Lag_1", "RADL3.SA_Spread_Lag_2", "RADL3.SA_Spread_Lag_3", "RADL3.SA_Spread_Lag_4", "RADL3.SA_Spread_Lag_5", "RADL3.SA_Volatilidade_Lag_1", "RADL3.SA_Volatilidade_Lag_2", "RADL3.SA_Volatilidade_Lag_3", "RADL3.SA_Volatilidade_Lag_4", "RADL3.SA_Volatilidade_Lag_5", "RADL3.<PERSON>_Parkinson_Volatility_Lag_1", "RADL3.<PERSON>_Parkinson_Volatility_Lag_2", "RADL3.<PERSON>_Parkinson_Volatility_Lag_3", "RADL3.<PERSON>_Parkinson_Volatility_Lag_4", "RADL3.<PERSON>_Parkinson_Volatility_Lag_5", "RADL3.SA_EMV_Lag_1", "RADL3.SA_EMV_Lag_2", "RADL3.SA_EMV_Lag_3", "RADL3.SA_EMV_Lag_4", "RADL3.SA_EMV_Lag_5", "RADL3.SA_EMV_MA_Lag_1", "RADL3.SA_EMV_MA_Lag_2", "RADL3.SA_EMV_MA_Lag_3", "RADL3.SA_EMV_MA_Lag_4", "RADL3.SA_EMV_MA_Lag_5", "RADL3.SA_VO_Lag_1", "RADL3.SA_VO_Lag_2", "RADL3.SA_VO_Lag_3", "RADL3.SA_VO_Lag_4", "RADL3.SA_VO_Lag_5", "RADL3.SA_High_Max_50_Lag_1", "RADL3.SA_High_Max_50_Lag_2", "RADL3.SA_High_Max_50_Lag_3", "RADL3.SA_High_Max_50_Lag_4", "RADL3.SA_High_Max_50_Lag_5", "RADL3.SA_Low_Min_50_Lag_1", "RADL3.SA_Low_Min_50_Lag_2", "RADL3.SA_Low_Min_50_Lag_3", "RADL3.SA_Low_Min_50_Lag_4", "RADL3.SA_Low_Min_50_Lag_5", "RADL3.SA_MFI_Lag_1", "RADL3.SA_MFI_Lag_2", "RADL3.SA_MFI_Lag_3", "RADL3.SA_MFI_Lag_4", "RADL3.SA_MFI_Lag_5", "RADL3.SA_Amihud_Lag_1", "RADL3.SA_Amihud_Lag_2", "RADL3.SA_Amihud_Lag_3", "RADL3.SA_Amihud_Lag_4", "RADL3.SA_Amihud_Lag_5", "RADL3.SA_Roll_Spread_Lag_1", "RADL3.SA_Roll_Spread_Lag_2", "RADL3.SA_Roll_Spread_Lag_3", "RADL3.SA_Roll_Spread_Lag_4", "RADL3.SA_Roll_Spread_Lag_5", "RADL3.<PERSON>_Hu<PERSON>_Lag_1", "RADL3.<PERSON>_<PERSON><PERSON>_Lag_2", "RADL3.<PERSON>_<PERSON><PERSON>_Lag_3", "RADL3.<PERSON>_<PERSON><PERSON>_Lag_4", "RADL3.<PERSON>_<PERSON><PERSON>_Lag_5", "RADL3.SA_Vol_per_Volume_Lag_1", "RADL3.SA_Vol_per_Volume_Lag_2", "RADL3.SA_Vol_per_Volume_Lag_3", "RADL3.SA_Vol_per_Volume_Lag_4", "RADL3.SA_Vol_per_Volume_Lag_5", "RADL3.SA_CMF_Lag_1", "RADL3.SA_CMF_Lag_2", "RADL3.SA_CMF_Lag_3", "RADL3.SA_CMF_Lag_4", "RADL3.SA_CMF_Lag_5", "RADL3.SA_AD_Line_Lag_1", "RADL3.SA_AD_Line_Lag_2", "RADL3.SA_AD_Line_Lag_3", "RADL3.SA_AD_Line_Lag_4", "RADL3.SA_AD_Line_Lag_5", "CMIG3.SA_Open", "CMIG3.SA_High", "CMIG3.SA_Low", "CMIG3.SA_Close", "CMIG3.SA_Volume", "CMIG3.SA_Media_OHLC", "CMIG3.SA_MM10", "CMIG3.SA_MM25", "CMIG3.SA_MM100", "CMIG3.SA_Media_OHLC_PctChange", "CMIG3.SA_Open_PctChange", "CMIG3.SA_High_PctChange", "CMIG3.SA_Low_PctChange", "CMIG3.SA_Close_PctChange", "CMIG3.SA_Close_PctChange_Historical", "CMIG3.SA_Volatilidade", "CMIG3.SA_Spread", "CMIG3.SA_Parkinson_Volatility", "CMIG3.SA_MFI", "CMIG3.SA_MFI_Historical", "CMIG3.SA_EMV", "CMIG3.SA_EMV_MA", "CMIG3.SA_Amihud", "CMIG3.SA_Amihud_Historical", "CMIG3.SA_Roll_Spread", "CMIG3.SA_Roll_Spread_Historical", "CMIG3.SA_Hurst", "CMIG3.<PERSON>_<PERSON><PERSON>_Historical", "CMIG3.SA_Vol_per_Volume", "CMIG3.SA_Vol_per_Volume_Historical", "CMIG3.SA_CMF", "CMIG3.SA_CMF_Historical", "CMIG3.SA_AD_Line", "CMIG3.SA_AD_Line_Historical", "CMIG3.SA_VO", "CMIG3.SA_High_Max_50", "CMIG3.SA_Low_Min_50", "CMIG3.SA_Segunda", "CMIG3.SA_Terca", "CMIG3.SA_Quarta", "CMIG3.SA_Quinta", "CMIG3.SA_Sexta", "CMIG3.SA_Mes_1", "CMIG3.SA_Mes_2", "CMIG3.SA_Mes_3", "CMIG3.SA_Mes_4", "CMIG3.SA_Mes_5", "CMIG3.SA_Mes_6", "CMIG3.SA_Mes_7", "CMIG3.SA_Mes_8", "CMIG3.SA_Mes_9", "CMIG3.SA_Mes_10", "CMIG3.SA_Mes_11", "CMIG3.SA_Mes_12", "CMIG3.SA_Quarter_1", "CMIG3.SA_Quarter_2", "CMIG3.SA_Quarter_3", "CMIG3.SA_Quarter_4", "CMIG3.SA_Last_Day_Quarter", "CMIG3.SA_Pre_Feriado_Brasil", "CMIG3.SA_Media_OHLC_Anterior", "CMIG3.SA_Sinal_Compra", "CMIG3.SA_Sinal_Venda", "CMIG3.SA_Media_OHLC_Futura", "CMIG3.SA_Open_PctChange_Lag_1", "CMIG3.SA_High_PctChange_Lag_1", "CMIG3.SA_Low_PctChange_Lag_1", "CMIG3.SA_Close_PctChange_Lag_1", "CMIG3.SA_Open_PctChange_Lag_2", "CMIG3.SA_High_PctChange_Lag_2", "CMIG3.SA_Low_PctChange_Lag_2", "CMIG3.SA_Close_PctChange_Lag_2", "CMIG3.SA_Open_PctChange_Lag_3", "CMIG3.SA_High_PctChange_Lag_3", "CMIG3.SA_Low_PctChange_Lag_3", "CMIG3.SA_Close_PctChange_Lag_3", "CMIG3.SA_Open_PctChange_Lag_4", "CMIG3.SA_High_PctChange_Lag_4", "CMIG3.SA_Low_PctChange_Lag_4", "CMIG3.SA_Close_PctChange_Lag_4", "CMIG3.SA_Open_PctChange_Lag_5", "CMIG3.SA_High_PctChange_Lag_5", "CMIG3.SA_Low_PctChange_Lag_5", "CMIG3.SA_Close_PctChange_Lag_5", "CMIG3.SA_Open_PctChange_Lag_6", "CMIG3.SA_High_PctChange_Lag_6", "CMIG3.SA_Low_PctChange_Lag_6", "CMIG3.SA_Close_PctChange_Lag_6", "CMIG3.SA_Open_PctChange_Lag_7", "CMIG3.SA_High_PctChange_Lag_7", "CMIG3.SA_Low_PctChange_Lag_7", "CMIG3.SA_Close_PctChange_Lag_7", "CMIG3.SA_Open_PctChange_Lag_8", "CMIG3.SA_High_PctChange_Lag_8", "CMIG3.SA_Low_PctChange_Lag_8", "CMIG3.SA_Close_PctChange_Lag_8", "CMIG3.SA_Open_PctChange_Lag_9", "CMIG3.SA_High_PctChange_Lag_9", "CMIG3.SA_Low_PctChange_Lag_9", "CMIG3.SA_Close_PctChange_Lag_9", "CMIG3.SA_Open_PctChange_Lag_10", "CMIG3.SA_High_PctChange_Lag_10", "CMIG3.SA_Low_PctChange_Lag_10", "CMIG3.SA_Close_PctChange_Lag_10", "CMIG3.SA_MM10_PctChange", "CMIG3.SA_Diff_OHLC_MM10", "CMIG3.SA_MM25_PctChange", "CMIG3.SA_Diff_OHLC_MM25", "CMIG3.SA_MM100_PctChange", "CMIG3.SA_Diff_OHLC_MM100", "CMIG3.SA_MM10_Menos_MM25", "CMIG3.SA_MM25_Menos_MM100", "CMIG3.SA_MM10_Menos_MM100", "CMIG3.SA_Volume_Lag_1", "CMIG3.SA_Volume_Lag_2", "CMIG3.SA_Volume_Lag_3", "CMIG3.SA_Volume_Lag_4", "CMIG3.SA_Volume_Lag_5", "CMIG3.SA_Spread_Lag_1", "CMIG3.SA_Spread_Lag_2", "CMIG3.SA_Spread_Lag_3", "CMIG3.SA_Spread_Lag_4", "CMIG3.SA_Spread_Lag_5", "CMIG3.SA_Volatilidade_Lag_1", "CMIG3.SA_Volatilidade_Lag_2", "CMIG3.SA_Volatilidade_Lag_3", "CMIG3.SA_Volatilidade_Lag_4", "CMIG3.SA_Volatilidade_Lag_5", "CMIG3.SA_Parkinson_Volatility_Lag_1", "CMIG3.<PERSON>_Parkinson_Volatility_Lag_2", "CMIG3.SA_Parkinson_Volatility_Lag_3", "CMIG3.SA_Parkinson_Volatility_Lag_4", "CMIG3.<PERSON>_Parkinson_Volatility_Lag_5", "CMIG3.SA_EMV_Lag_1", "CMIG3.SA_EMV_Lag_2", "CMIG3.SA_EMV_Lag_3", "CMIG3.SA_EMV_Lag_4", "CMIG3.SA_EMV_Lag_5", "CMIG3.SA_EMV_MA_Lag_1", "CMIG3.SA_EMV_MA_Lag_2", "CMIG3.SA_EMV_MA_Lag_3", "CMIG3.SA_EMV_MA_Lag_4", "CMIG3.SA_EMV_MA_Lag_5", "CMIG3.SA_VO_Lag_1", "CMIG3.SA_VO_Lag_2", "CMIG3.SA_VO_Lag_3", "CMIG3.SA_VO_Lag_4", "CMIG3.SA_VO_Lag_5", "CMIG3.SA_High_Max_50_Lag_1", "CMIG3.SA_High_Max_50_Lag_2", "CMIG3.SA_High_Max_50_Lag_3", "CMIG3.SA_High_Max_50_Lag_4", "CMIG3.SA_High_Max_50_Lag_5", "CMIG3.SA_Low_Min_50_Lag_1", "CMIG3.SA_Low_Min_50_Lag_2", "CMIG3.SA_Low_Min_50_Lag_3", "CMIG3.SA_Low_Min_50_Lag_4", "CMIG3.SA_Low_Min_50_Lag_5", "CMIG3.SA_MFI_Lag_1", "CMIG3.SA_MFI_Lag_2", "CMIG3.SA_MFI_Lag_3", "CMIG3.SA_MFI_Lag_4", "CMIG3.SA_MFI_Lag_5", "CMIG3.SA_Amihud_Lag_1", "CMIG3.SA_Amihud_Lag_2", "CMIG3.SA_Amihud_Lag_3", "CMIG3.SA_Amihud_Lag_4", "CMIG3.SA_Amihud_Lag_5", "CMIG3.SA_Roll_Spread_Lag_1", "CMIG3.SA_Roll_Spread_Lag_2", "CMIG3.SA_Roll_Spread_Lag_3", "CMIG3.SA_Roll_Spread_Lag_4", "CMIG3.SA_Roll_Spread_Lag_5", "CMIG3.SA_Hurst_Lag_1", "CMIG3.<PERSON>_Hu<PERSON>_Lag_2", "CMIG3.<PERSON>_Hurst_Lag_3", "CMIG3.<PERSON>_<PERSON><PERSON>_Lag_4", "CMIG3.<PERSON>_<PERSON><PERSON>_Lag_5", "CMIG3.SA_Vol_per_Volume_Lag_1", "CMIG3.SA_Vol_per_Volume_Lag_2", "CMIG3.SA_Vol_per_Volume_Lag_3", "CMIG3.SA_Vol_per_Volume_Lag_4", "CMIG3.SA_Vol_per_Volume_Lag_5", "CMIG3.SA_CMF_Lag_1", "CMIG3.SA_CMF_Lag_2", "CMIG3.SA_CMF_Lag_3", "CMIG3.SA_CMF_Lag_4", "CMIG3.SA_CMF_Lag_5", "CMIG3.SA_AD_Line_Lag_1", "CMIG3.SA_AD_Line_Lag_2", "CMIG3.SA_AD_Line_Lag_3", "CMIG3.SA_AD_Line_Lag_4", "CMIG3.SA_AD_Line_Lag_5", "CSMG3.SA_Open", "CSMG3.SA_High", "CSMG3.SA_Low", "CSMG3.SA_Close", "CSMG3.SA_Volume", "CSMG3.SA_Media_OHLC", "CSMG3.SA_MM10", "CSMG3.SA_MM25", "CSMG3.SA_MM100", "CSMG3.SA_Media_OHLC_PctChange", "CSMG3.SA_Open_PctChange", "CSMG3.SA_High_PctChange", "CSMG3.SA_Low_PctChange", "CSMG3.SA_Close_PctChange", "CSMG3.SA_Close_PctChange_Historical", "CSMG3.SA_Volatilidade", "CSMG3.SA_Spread", "CSMG3.SA_Parkinson_Volatility", "CSMG3.SA_MFI", "CSMG3.SA_MFI_Historical", "CSMG3.SA_EMV", "CSMG3.SA_EMV_MA", "CSMG3.SA_Amihud", "CSMG3.SA_Amihud_Historical", "CSMG3.SA_Roll_Spread", "CSMG3.SA_Roll_Spread_Historical", "CSMG3.SA_Hurst", "CSMG3.SA_<PERSON><PERSON>_Historical", "CSMG3.SA_Vol_per_Volume", "CSMG3.SA_Vol_per_Volume_Historical", "CSMG3.SA_CMF", "CSMG3.SA_CMF_Historical", "CSMG3.SA_AD_Line", "CSMG3.SA_AD_Line_Historical", "CSMG3.SA_VO", "CSMG3.SA_High_Max_50", "CSMG3.SA_Low_Min_50", "CSMG3.SA_Segunda", "CSMG3.SA_Terca", "CSMG3.SA_Quarta", "CSMG3.SA_Quinta", "CSMG3.SA_Sexta", "CSMG3.SA_Mes_1", "CSMG3.SA_Mes_2", "CSMG3.SA_Mes_3", "CSMG3.SA_Mes_4", "CSMG3.SA_Mes_5", "CSMG3.SA_Mes_6", "CSMG3.SA_Mes_7", "CSMG3.SA_Mes_8", "CSMG3.SA_Mes_9", "CSMG3.SA_Mes_10", "CSMG3.SA_Mes_11", "CSMG3.SA_Mes_12", "CSMG3.SA_Quarter_1", "CSMG3.SA_Quarter_2", "CSMG3.SA_Quarter_3", "CSMG3.SA_Quarter_4", "CSMG3.SA_Last_Day_Quarter", "CSMG3.SA_Pre_Feriado_Brasil", "CSMG3.SA_Media_OHLC_Anterior", "CSMG3.SA_Sinal_Compra", "CSMG3.SA_Sinal_Venda", "CSMG3.SA_Media_OHLC_Futura", "CSMG3.SA_Open_PctChange_Lag_1", "CSMG3.SA_High_PctChange_Lag_1", "CSMG3.SA_Low_PctChange_Lag_1", "CSMG3.SA_Close_PctChange_Lag_1", "CSMG3.SA_Open_PctChange_Lag_2", "CSMG3.SA_High_PctChange_Lag_2", "CSMG3.SA_Low_PctChange_Lag_2", "CSMG3.SA_Close_PctChange_Lag_2", "CSMG3.SA_Open_PctChange_Lag_3", "CSMG3.SA_High_PctChange_Lag_3", "CSMG3.SA_Low_PctChange_Lag_3", "CSMG3.SA_Close_PctChange_Lag_3", "CSMG3.SA_Open_PctChange_Lag_4", "CSMG3.SA_High_PctChange_Lag_4", "CSMG3.SA_Low_PctChange_Lag_4", "CSMG3.SA_Close_PctChange_Lag_4", "CSMG3.SA_Open_PctChange_Lag_5", "CSMG3.SA_High_PctChange_Lag_5", "CSMG3.SA_Low_PctChange_Lag_5", "CSMG3.SA_Close_PctChange_Lag_5", "CSMG3.SA_Open_PctChange_Lag_6", "CSMG3.SA_High_PctChange_Lag_6", "CSMG3.SA_Low_PctChange_Lag_6", "CSMG3.SA_Close_PctChange_Lag_6", "CSMG3.SA_Open_PctChange_Lag_7", "CSMG3.SA_High_PctChange_Lag_7", "CSMG3.SA_Low_PctChange_Lag_7", "CSMG3.SA_Close_PctChange_Lag_7", "CSMG3.SA_Open_PctChange_Lag_8", "CSMG3.SA_High_PctChange_Lag_8", "CSMG3.SA_Low_PctChange_Lag_8", "CSMG3.SA_Close_PctChange_Lag_8", "CSMG3.SA_Open_PctChange_Lag_9", "CSMG3.SA_High_PctChange_Lag_9", "CSMG3.SA_Low_PctChange_Lag_9", "CSMG3.SA_Close_PctChange_Lag_9", "CSMG3.SA_Open_PctChange_Lag_10", "CSMG3.SA_High_PctChange_Lag_10", "CSMG3.SA_Low_PctChange_Lag_10", "CSMG3.SA_Close_PctChange_Lag_10", "CSMG3.SA_MM10_PctChange", "CSMG3.SA_Diff_OHLC_MM10", "CSMG3.SA_MM25_PctChange", "CSMG3.SA_Diff_OHLC_MM25", "CSMG3.SA_MM100_PctChange", "CSMG3.SA_Diff_OHLC_MM100", "CSMG3.SA_MM10_Menos_MM25", "CSMG3.SA_MM25_Menos_MM100", "CSMG3.SA_MM10_Menos_MM100", "CSMG3.SA_Volume_Lag_1", "CSMG3.SA_Volume_Lag_2", "CSMG3.SA_Volume_Lag_3", "CSMG3.SA_Volume_Lag_4", "CSMG3.SA_Volume_Lag_5", "CSMG3.SA_Spread_Lag_1", "CSMG3.SA_Spread_Lag_2", "CSMG3.SA_Spread_Lag_3", "CSMG3.SA_Spread_Lag_4", "CSMG3.SA_Spread_Lag_5", "CSMG3.SA_Volatilidade_Lag_1", "CSMG3.SA_Volatilidade_Lag_2", "CSMG3.SA_Volatilidade_Lag_3", "CSMG3.SA_Volatilidade_Lag_4", "CSMG3.SA_Volatilidade_Lag_5", "CSMG3.SA_Parkinson_Volatility_Lag_1", "CSMG3.SA_Parkinson_Volatility_Lag_2", "CSMG3.SA_Parkinson_Volatility_Lag_3", "CSMG3.SA_Parkinson_Volatility_Lag_4", "CSMG3.SA_Parkinson_Volatility_Lag_5", "CSMG3.SA_EMV_Lag_1", "CSMG3.SA_EMV_Lag_2", "CSMG3.SA_EMV_Lag_3", "CSMG3.SA_EMV_Lag_4", "CSMG3.SA_EMV_Lag_5", "CSMG3.SA_EMV_MA_Lag_1", "CSMG3.SA_EMV_MA_Lag_2", "CSMG3.SA_EMV_MA_Lag_3", "CSMG3.SA_EMV_MA_Lag_4", "CSMG3.SA_EMV_MA_Lag_5", "CSMG3.SA_VO_Lag_1", "CSMG3.SA_VO_Lag_2", "CSMG3.SA_VO_Lag_3", "CSMG3.SA_VO_Lag_4", "CSMG3.SA_VO_Lag_5", "CSMG3.SA_High_Max_50_Lag_1", "CSMG3.SA_High_Max_50_Lag_2", "CSMG3.SA_High_Max_50_Lag_3", "CSMG3.SA_High_Max_50_Lag_4", "CSMG3.SA_High_Max_50_Lag_5", "CSMG3.SA_Low_Min_50_Lag_1", "CSMG3.SA_Low_Min_50_Lag_2", "CSMG3.SA_Low_Min_50_Lag_3", "CSMG3.SA_Low_Min_50_Lag_4", "CSMG3.SA_Low_Min_50_Lag_5", "CSMG3.SA_MFI_Lag_1", "CSMG3.SA_MFI_Lag_2", "CSMG3.SA_MFI_Lag_3", "CSMG3.SA_MFI_Lag_4", "CSMG3.SA_MFI_Lag_5", "CSMG3.SA_Amihud_Lag_1", "CSMG3.SA_Amihud_Lag_2", "CSMG3.SA_Amihud_Lag_3", "CSMG3.SA_Amihud_Lag_4", "CSMG3.SA_Amihud_Lag_5", "CSMG3.SA_Roll_Spread_Lag_1", "CSMG3.SA_Roll_Spread_Lag_2", "CSMG3.SA_Roll_Spread_Lag_3", "CSMG3.SA_Roll_Spread_Lag_4", "CSMG3.SA_Roll_Spread_Lag_5", "CSMG3.SA_Hurst_Lag_1", "CSMG3.SA_Hurst_Lag_2", "CSMG3.SA_Hurst_Lag_3", "CSMG3.<PERSON>_<PERSON>rst_Lag_4", "CSMG3.<PERSON>_<PERSON><PERSON>_Lag_5", "CSMG3.SA_Vol_per_Volume_Lag_1", "CSMG3.SA_Vol_per_Volume_Lag_2", "CSMG3.SA_Vol_per_Volume_Lag_3", "CSMG3.SA_Vol_per_Volume_Lag_4", "CSMG3.SA_Vol_per_Volume_Lag_5", "CSMG3.SA_CMF_Lag_1", "CSMG3.SA_CMF_Lag_2", "CSMG3.SA_CMF_Lag_3", "CSMG3.SA_CMF_Lag_4", "CSMG3.SA_CMF_Lag_5", "CSMG3.SA_AD_Line_Lag_1", "CSMG3.SA_AD_Line_Lag_2", "CSMG3.SA_AD_Line_Lag_3", "CSMG3.SA_AD_Line_Lag_4", "CSMG3.SA_AD_Line_Lag_5", "KLBN11.SA_Close", "KLBN11.SA_High", "KLBN11.SA_Low", "KLBN11.SA_Open", "KLBN11.SA_Volume", "KLBN11.SA_Media_OHLC", "KLBN11.SA_MM10", "KLBN11.SA_MM25", "KLBN11.SA_MM100", "KLBN11.SA_Media_OHLC_PctChange", "KLBN11.SA_Open_PctChange", "KLBN11.SA_High_PctChange", "KLBN11.SA_Low_PctChange", "KLBN11.SA_Close_PctChange", "KLBN11.SA_Close_PctChange_Historical", "KLBN11.SA_Volatilidade", "KLBN11.SA_Spread", "KLBN11.SA_Parkinson_Volatility", "KLBN11.SA_MFI", "KLBN11.SA_MFI_Historical", "KLBN11.SA_EMV", "KLBN11.SA_EMV_MA", "KLBN11.SA_Amihud", "KLBN11.SA_Amihud_Historical", "KLBN11.SA_Roll_Spread", "KLBN11.SA_Roll_Spread_Historical", "KLBN11.SA_Hurst", "KLBN11.SA_Hurst_Historical", "KLBN11.SA_Vol_per_Volume", "KLBN11.SA_Vol_per_Volume_Historical", "KLBN11.SA_CMF", "KLBN11.SA_CMF_Historical", "KLBN11.SA_AD_Line", "KLBN11.SA_AD_Line_Historical", "KLBN11.SA_VO", "KLBN11.SA_High_Max_50", "KLBN11.SA_Low_Min_50", "KLBN11.SA_Segunda", "KLBN11.SA_Terca", "KLBN11.SA_Quarta", "KLBN11.SA_Quinta", "KLBN11.SA_Sexta", "KLBN11.SA_Mes_1", "KLBN11.SA_Mes_2", "KLBN11.SA_Mes_3", "KLBN11.SA_Mes_4", "KLBN11.SA_Mes_5", "KLBN11.SA_Mes_6", "KLBN11.SA_Mes_7", "KLBN11.SA_Mes_8", "KLBN11.SA_Mes_9", "KLBN11.SA_Mes_10", "KLBN11.SA_Mes_11", "KLBN11.SA_Mes_12", "KLBN11.SA_Quarter_1", "KLBN11.SA_Quarter_2", "KLBN11.SA_Quarter_3", "KLBN11.SA_Quarter_4", "KLBN11.SA_Last_Day_Quarter", "KLBN11.SA_Pre_Feriado_Brasil", "KLBN11.SA_Media_OHLC_Anterior", "KLBN11.SA_Sinal_Compra", "KLBN11.SA_Sinal_Venda", "KLBN11.SA_Media_OHLC_Futura", "KLBN11.SA_Open_PctChange_Lag_1", "KLBN11.SA_High_PctChange_Lag_1", "KLBN11.SA_Low_PctChange_Lag_1", "KLBN11.SA_Close_PctChange_Lag_1", "KLBN11.SA_Open_PctChange_Lag_2", "KLBN11.SA_High_PctChange_Lag_2", "KLBN11.SA_Low_PctChange_Lag_2", "KLBN11.SA_Close_PctChange_Lag_2", "KLBN11.SA_Open_PctChange_Lag_3", "KLBN11.SA_High_PctChange_Lag_3", "KLBN11.SA_Low_PctChange_Lag_3", "KLBN11.SA_Close_PctChange_Lag_3", "KLBN11.SA_Open_PctChange_Lag_4", "KLBN11.SA_High_PctChange_Lag_4", "KLBN11.SA_Low_PctChange_Lag_4", "KLBN11.SA_Close_PctChange_Lag_4", "KLBN11.SA_Open_PctChange_Lag_5", "KLBN11.SA_High_PctChange_Lag_5", "KLBN11.SA_Low_PctChange_Lag_5", "KLBN11.SA_Close_PctChange_Lag_5", "KLBN11.SA_Open_PctChange_Lag_6", "KLBN11.SA_High_PctChange_Lag_6", "KLBN11.SA_Low_PctChange_Lag_6", "KLBN11.SA_Close_PctChange_Lag_6", "KLBN11.SA_Open_PctChange_Lag_7", "KLBN11.SA_High_PctChange_Lag_7", "KLBN11.SA_Low_PctChange_Lag_7", "KLBN11.SA_Close_PctChange_Lag_7", "KLBN11.SA_Open_PctChange_Lag_8", "KLBN11.SA_High_PctChange_Lag_8", "KLBN11.SA_Low_PctChange_Lag_8", "KLBN11.SA_Close_PctChange_Lag_8", "KLBN11.SA_Open_PctChange_Lag_9", "KLBN11.SA_High_PctChange_Lag_9", "KLBN11.SA_Low_PctChange_Lag_9", "KLBN11.SA_Close_PctChange_Lag_9", "KLBN11.SA_Open_PctChange_Lag_10", "KLBN11.SA_High_PctChange_Lag_10", "KLBN11.SA_Low_PctChange_Lag_10", "KLBN11.SA_Close_PctChange_Lag_10", "KLBN11.SA_MM10_PctChange", "KLBN11.SA_Diff_OHLC_MM10", "KLBN11.SA_MM25_PctChange", "KLBN11.SA_Diff_OHLC_MM25", "KLBN11.SA_MM100_PctChange", "KLBN11.SA_Diff_OHLC_MM100", "KLBN11.SA_MM10_Menos_MM25", "KLBN11.SA_MM25_Menos_MM100", "KLBN11.SA_MM10_Menos_MM100", "KLBN11.SA_Volume_Lag_1", "KLBN11.SA_Volume_Lag_2", "KLBN11.SA_Volume_Lag_3", "KLBN11.SA_Volume_Lag_4", "KLBN11.SA_Volume_Lag_5", "KLBN11.SA_Spread_Lag_1", "KLBN11.SA_Spread_Lag_2", "KLBN11.SA_Spread_Lag_3", "KLBN11.SA_Spread_Lag_4", "KLBN11.SA_Spread_Lag_5", "KLBN11.SA_Volatilidade_Lag_1", "KLBN11.SA_Volatilidade_Lag_2", "KLBN11.SA_Volatilidade_Lag_3", "KLBN11.SA_Volatilidade_Lag_4", "KLBN11.SA_Volatilidade_Lag_5", "KLBN11.<PERSON>_Parkinson_Volatility_Lag_1", "KLBN11.<PERSON>_Parkinson_Volatility_Lag_2", "KLBN11.<PERSON>_Parkinson_Volatility_Lag_3", "KLBN11.<PERSON>_Parkinson_Volatility_Lag_4", "KLBN11.<PERSON>_Parkinson_Volatility_Lag_5", "KLBN11.SA_EMV_Lag_1", "KLBN11.SA_EMV_Lag_2", "KLBN11.SA_EMV_Lag_3", "KLBN11.SA_EMV_Lag_4", "KLBN11.SA_EMV_Lag_5", "KLBN11.SA_EMV_MA_Lag_1", "KLBN11.SA_EMV_MA_Lag_2", "KLBN11.SA_EMV_MA_Lag_3", "KLBN11.SA_EMV_MA_Lag_4", "KLBN11.SA_EMV_MA_Lag_5", "KLBN11.SA_VO_Lag_1", "KLBN11.SA_VO_Lag_2", "KLBN11.SA_VO_Lag_3", "KLBN11.SA_VO_Lag_4", "KLBN11.SA_VO_Lag_5", "KLBN11.SA_High_Max_50_Lag_1", "KLBN11.SA_High_Max_50_Lag_2", "KLBN11.SA_High_Max_50_Lag_3", "KLBN11.SA_High_Max_50_Lag_4", "KLBN11.SA_High_Max_50_Lag_5", "KLBN11.SA_Low_Min_50_Lag_1", "KLBN11.SA_Low_Min_50_Lag_2", "KLBN11.SA_Low_Min_50_Lag_3", "KLBN11.SA_Low_Min_50_Lag_4", "KLBN11.SA_Low_Min_50_Lag_5", "KLBN11.SA_MFI_Lag_1", "KLBN11.SA_MFI_Lag_2", "KLBN11.SA_MFI_Lag_3", "KLBN11.SA_MFI_Lag_4", "KLBN11.SA_MFI_Lag_5", "KLBN11.SA_Amihud_Lag_1", "KLBN11.SA_Amihud_Lag_2", "KLBN11.SA_Amihud_Lag_3", "KLBN11.SA_Amihud_Lag_4", "KLBN11.SA_Amihud_Lag_5", "KLBN11.SA_Roll_Spread_Lag_1", "KLBN11.SA_Roll_Spread_Lag_2", "KLBN11.SA_Roll_Spread_Lag_3", "KLBN11.SA_Roll_Spread_Lag_4", "KLBN11.SA_Roll_Spread_Lag_5", "KLBN11.SA_Hurst_Lag_1", "KLBN11.SA_Hurst_Lag_2", "KLBN11.SA_Hurst_Lag_3", "KLBN11.SA_Hurst_Lag_4", "KLBN11.<PERSON>_Hurst_Lag_5", "KLBN11.SA_Vol_per_Volume_Lag_1", "KLBN11.SA_Vol_per_Volume_Lag_2", "KLBN11.SA_Vol_per_Volume_Lag_3", "KLBN11.SA_Vol_per_Volume_Lag_4", "KLBN11.SA_Vol_per_Volume_Lag_5", "KLBN11.SA_CMF_Lag_1", "KLBN11.SA_CMF_Lag_2", "KLBN11.SA_CMF_Lag_3", "KLBN11.SA_CMF_Lag_4", "KLBN11.SA_CMF_Lag_5", "KLBN11.SA_AD_Line_Lag_1", "KLBN11.SA_AD_Line_Lag_2", "KLBN11.SA_AD_Line_Lag_3", "KLBN11.SA_AD_Line_Lag_4", "KLBN11.SA_AD_Line_Lag_5", "HYPE3.SA_Open", "HYPE3.SA_High", "HYPE3.SA_Low", "HYPE3.SA_Close", "HYPE3.SA_Volume", "HYPE3.SA_Media_OHLC", "HYPE3.SA_MM10", "HYPE3.SA_MM25", "HYPE3.SA_MM100", "HYPE3.SA_Media_OHLC_PctChange", "HYPE3.SA_Open_PctChange", "HYPE3.SA_High_PctChange", "HYPE3.SA_Low_PctChange", "HYPE3.SA_Close_PctChange", "HYPE3.SA_Close_PctChange_Historical", "HYPE3.SA_Volatilidade", "HYPE3.SA_Spread", "HYPE3.SA_Parkinson_Volatility", "HYPE3.SA_MFI", "HYPE3.SA_MFI_Historical", "HYPE3.SA_EMV", "HYPE3.SA_EMV_MA", "HYPE3.SA_Amihud", "HYPE3.SA_Amihud_Historical", "HYPE3.SA_Roll_Spread", "HYPE3.SA_Roll_Spread_Historical", "HYPE3.SA_Hurst", "HYPE3.<PERSON>_<PERSON><PERSON>_Historical", "HYPE3.SA_Vol_per_Volume", "HYPE3.SA_Vol_per_Volume_Historical", "HYPE3.SA_CMF", "HYPE3.SA_CMF_Historical", "HYPE3.SA_AD_Line", "HYPE3.SA_AD_Line_Historical", "HYPE3.SA_VO", "HYPE3.SA_High_Max_50", "HYPE3.SA_Low_Min_50", "HYPE3.SA_Segunda", "HYPE3.SA_Terca", "HYPE3.SA_Quarta", "HYPE3.SA_Quinta", "HYPE3.SA_Sexta", "HYPE3.SA_Mes_1", "HYPE3.SA_Mes_2", "HYPE3.SA_Mes_3", "HYPE3.SA_Mes_4", "HYPE3.SA_Mes_5", "HYPE3.SA_Mes_6", "HYPE3.SA_Mes_7", "HYPE3.SA_Mes_8", "HYPE3.SA_Mes_9", "HYPE3.SA_Mes_10", "HYPE3.SA_Mes_11", "HYPE3.SA_Mes_12", "HYPE3.SA_Quarter_1", "HYPE3.SA_Quarter_2", "HYPE3.SA_Quarter_3", "HYPE3.SA_Quarter_4", "HYPE3.SA_Last_Day_Quarter", "HYPE3.SA_Pre_Feriado_Brasil", "HYPE3.SA_Media_OHLC_Anterior", "HYPE3.SA_Sinal_Compra", "HYPE3.SA_Sinal_Venda", "HYPE3.SA_Media_OHLC_Futura", "HYPE3.SA_Open_PctChange_Lag_1", "HYPE3.SA_High_PctChange_Lag_1", "HYPE3.SA_Low_PctChange_Lag_1", "HYPE3.SA_Close_PctChange_Lag_1", "HYPE3.SA_Open_PctChange_Lag_2", "HYPE3.SA_High_PctChange_Lag_2", "HYPE3.SA_Low_PctChange_Lag_2", "HYPE3.SA_Close_PctChange_Lag_2", "HYPE3.SA_Open_PctChange_Lag_3", "HYPE3.SA_High_PctChange_Lag_3", "HYPE3.SA_Low_PctChange_Lag_3", "HYPE3.SA_Close_PctChange_Lag_3", "HYPE3.SA_Open_PctChange_Lag_4", "HYPE3.SA_High_PctChange_Lag_4", "HYPE3.SA_Low_PctChange_Lag_4", "HYPE3.SA_Close_PctChange_Lag_4", "HYPE3.SA_Open_PctChange_Lag_5", "HYPE3.SA_High_PctChange_Lag_5", "HYPE3.SA_Low_PctChange_Lag_5", "HYPE3.SA_Close_PctChange_Lag_5", "HYPE3.SA_Open_PctChange_Lag_6", "HYPE3.SA_High_PctChange_Lag_6", "HYPE3.SA_Low_PctChange_Lag_6", "HYPE3.SA_Close_PctChange_Lag_6", "HYPE3.SA_Open_PctChange_Lag_7", "HYPE3.SA_High_PctChange_Lag_7", "HYPE3.SA_Low_PctChange_Lag_7", "HYPE3.SA_Close_PctChange_Lag_7", "HYPE3.SA_Open_PctChange_Lag_8", "HYPE3.SA_High_PctChange_Lag_8", "HYPE3.SA_Low_PctChange_Lag_8", "HYPE3.SA_Close_PctChange_Lag_8", "HYPE3.SA_Open_PctChange_Lag_9", "HYPE3.SA_High_PctChange_Lag_9", "HYPE3.SA_Low_PctChange_Lag_9", "HYPE3.SA_Close_PctChange_Lag_9", "HYPE3.SA_Open_PctChange_Lag_10", "HYPE3.SA_High_PctChange_Lag_10", "HYPE3.SA_Low_PctChange_Lag_10", "HYPE3.SA_Close_PctChange_Lag_10", "HYPE3.SA_MM10_PctChange", "HYPE3.SA_Diff_OHLC_MM10", "HYPE3.SA_MM25_PctChange", "HYPE3.SA_Diff_OHLC_MM25", "HYPE3.SA_MM100_PctChange", "HYPE3.SA_Diff_OHLC_MM100", "HYPE3.SA_MM10_Menos_MM25", "HYPE3.SA_MM25_Menos_MM100", "HYPE3.SA_MM10_Menos_MM100", "HYPE3.SA_Volume_Lag_1", "HYPE3.SA_Volume_Lag_2", "HYPE3.SA_Volume_Lag_3", "HYPE3.SA_Volume_Lag_4", "HYPE3.SA_Volume_Lag_5", "HYPE3.SA_Spread_Lag_1", "HYPE3.SA_Spread_Lag_2", "HYPE3.SA_Spread_Lag_3", "HYPE3.SA_Spread_Lag_4", "HYPE3.SA_Spread_Lag_5", "HYPE3.SA_Volatilidade_Lag_1", "HYPE3.SA_Volatilidade_Lag_2", "HYPE3.SA_Volatilidade_Lag_3", "HYPE3.SA_Volatilidade_Lag_4", "HYPE3.SA_Volatilidade_Lag_5", "HYPE3.SA_Parkinson_Volatility_Lag_1", "HYPE3.<PERSON>_Parkinson_Volatility_Lag_2", "HYPE3.SA_Parkinson_Volatility_Lag_3", "HYPE3.SA_Parkinson_Volatility_Lag_4", "HYPE3.<PERSON>_Parkinson_Volatility_Lag_5", "HYPE3.SA_EMV_Lag_1", "HYPE3.SA_EMV_Lag_2", "HYPE3.SA_EMV_Lag_3", "HYPE3.SA_EMV_Lag_4", "HYPE3.SA_EMV_Lag_5", "HYPE3.SA_EMV_MA_Lag_1", "HYPE3.SA_EMV_MA_Lag_2", "HYPE3.SA_EMV_MA_Lag_3", "HYPE3.SA_EMV_MA_Lag_4", "HYPE3.SA_EMV_MA_Lag_5", "HYPE3.SA_VO_Lag_1", "HYPE3.SA_VO_Lag_2", "HYPE3.SA_VO_Lag_3", "HYPE3.SA_VO_Lag_4", "HYPE3.SA_VO_Lag_5", "HYPE3.SA_High_Max_50_Lag_1", "HYPE3.SA_High_Max_50_Lag_2", "HYPE3.SA_High_Max_50_Lag_3", "HYPE3.SA_High_Max_50_Lag_4", "HYPE3.SA_High_Max_50_Lag_5", "HYPE3.SA_Low_Min_50_Lag_1", "HYPE3.SA_Low_Min_50_Lag_2", "HYPE3.SA_Low_Min_50_Lag_3", "HYPE3.SA_Low_Min_50_Lag_4", "HYPE3.<PERSON>_Low_Min_50_Lag_5", "HYPE3.SA_MFI_Lag_1", "HYPE3.SA_MFI_Lag_2", "HYPE3.SA_MFI_Lag_3", "HYPE3.SA_MFI_Lag_4", "HYPE3.SA_MFI_Lag_5", "HYPE3.SA_Amihud_Lag_1", "HYPE3.SA_Amihud_Lag_2", "HYPE3.SA_Amihud_Lag_3", "HYPE3.SA_Amihud_Lag_4", "HYPE3.SA_Amihud_Lag_5", "HYPE3.SA_Roll_Spread_Lag_1", "HYPE3.SA_Roll_Spread_Lag_2", "HYPE3.SA_Roll_Spread_Lag_3", "HYPE3.SA_Roll_Spread_Lag_4", "HYPE3.SA_Roll_Spread_Lag_5", "HYPE3.<PERSON>_Hurst_Lag_1", "HYPE3.<PERSON>_Hu<PERSON>_Lag_2", "HYPE3.<PERSON>_Hu<PERSON>_Lag_3", "HYPE3.<PERSON>_<PERSON><PERSON>_Lag_4", "HYPE3.<PERSON>_<PERSON><PERSON>_Lag_5", "HYPE3.SA_Vol_per_Volume_Lag_1", "HYPE3.SA_Vol_per_Volume_Lag_2", "HYPE3.SA_Vol_per_Volume_Lag_3", "HYPE3.SA_Vol_per_Volume_Lag_4", "HYPE3.SA_Vol_per_Volume_Lag_5", "HYPE3.SA_CMF_Lag_1", "HYPE3.SA_CMF_Lag_2", "HYPE3.SA_CMF_Lag_3", "HYPE3.SA_CMF_Lag_4", "HYPE3.SA_CMF_Lag_5", "HYPE3.SA_AD_Line_Lag_1", "HYPE3.SA_AD_Line_Lag_2", "HYPE3.SA_AD_Line_Lag_3", "HYPE3.SA_AD_Line_Lag_4", "HYPE3.SA_AD_Line_Lag_5", "CXSE3.SA_Close", "CXSE3.SA_High", "CXSE3.SA_Low", "CXSE3.SA_Open", "CXSE3.SA_Volume", "CXSE3.SA_Media_OHLC", "CXSE3.SA_MM10", "CXSE3.SA_MM25", "CXSE3.SA_MM100", "CXSE3.SA_Media_OHLC_PctChange", "CXSE3.SA_Open_PctChange", "CXSE3.SA_High_PctChange", "CXSE3.SA_Low_PctChange", "CXSE3.SA_Close_PctChange", "CXSE3.SA_Close_PctChange_Historical", "CXSE3.SA_Volatilidade", "CXSE3.SA_Spread", "CXSE3.SA_Parkinson_Volatility", "CXSE3.SA_MFI", "CXSE3.SA_MFI_Historical", "CXSE3.SA_EMV", "CXSE3.SA_EMV_MA", "CXSE3.SA_Amihud", "CXSE3.SA_Amihud_Historical", "CXSE3.SA_Roll_Spread", "CXSE3.SA_Roll_Spread_Historical", "CXSE3.SA_Hurst", "CXSE3.<PERSON>_<PERSON><PERSON>_Historical", "CXSE3.SA_Vol_per_Volume", "CXSE3.SA_Vol_per_Volume_Historical", "CXSE3.SA_CMF", "CXSE3.SA_CMF_Historical", "CXSE3.SA_AD_Line", "CXSE3.SA_AD_Line_Historical", "CXSE3.SA_VO", "CXSE3.SA_High_Max_50", "CXSE3.SA_Low_Min_50", "CXSE3.SA_Segunda", "CXSE3.SA_Terca", "CXSE3.SA_Quarta", "CXSE3.SA_Quinta", "CXSE3.SA_Sexta", "CXSE3.SA_Mes_1", "CXSE3.SA_Mes_2", "CXSE3.SA_Mes_3", "CXSE3.SA_Mes_4", "CXSE3.SA_Mes_5", "CXSE3.SA_Mes_6", "CXSE3.SA_Mes_7", "CXSE3.SA_Mes_8", "CXSE3.SA_Mes_9", "CXSE3.SA_Mes_10", "CXSE3.SA_Mes_11", "CXSE3.SA_Mes_12", "CXSE3.SA_Quarter_1", "CXSE3.SA_Quarter_2", "CXSE3.SA_Quarter_3", "CXSE3.SA_Quarter_4", "CXSE3.SA_Last_Day_Quarter", "CXSE3.SA_Pre_Feriado_Brasil", "CXSE3.SA_Media_OHLC_Anterior", "CXSE3.SA_Sinal_Compra", "CXSE3.SA_Sinal_Venda", "CXSE3.SA_Media_OHLC_Futura", "CXSE3.SA_Open_PctChange_Lag_1", "CXSE3.SA_High_PctChange_Lag_1", "CXSE3.SA_Low_PctChange_Lag_1", "CXSE3.SA_Close_PctChange_Lag_1", "CXSE3.SA_Open_PctChange_Lag_2", "CXSE3.SA_High_PctChange_Lag_2", "CXSE3.SA_Low_PctChange_Lag_2", "CXSE3.SA_Close_PctChange_Lag_2", "CXSE3.SA_Open_PctChange_Lag_3", "CXSE3.SA_High_PctChange_Lag_3", "CXSE3.SA_Low_PctChange_Lag_3", "CXSE3.SA_Close_PctChange_Lag_3", "CXSE3.SA_Open_PctChange_Lag_4", "CXSE3.SA_High_PctChange_Lag_4", "CXSE3.SA_Low_PctChange_Lag_4", "CXSE3.SA_Close_PctChange_Lag_4", "CXSE3.SA_Open_PctChange_Lag_5", "CXSE3.SA_High_PctChange_Lag_5", "CXSE3.SA_Low_PctChange_Lag_5", "CXSE3.SA_Close_PctChange_Lag_5", "CXSE3.SA_Open_PctChange_Lag_6", "CXSE3.SA_High_PctChange_Lag_6", "CXSE3.SA_Low_PctChange_Lag_6", "CXSE3.SA_Close_PctChange_Lag_6", "CXSE3.SA_Open_PctChange_Lag_7", "CXSE3.SA_High_PctChange_Lag_7", "CXSE3.SA_Low_PctChange_Lag_7", "CXSE3.SA_Close_PctChange_Lag_7", "CXSE3.SA_Open_PctChange_Lag_8", "CXSE3.SA_High_PctChange_Lag_8", "CXSE3.SA_Low_PctChange_Lag_8", "CXSE3.SA_Close_PctChange_Lag_8", "CXSE3.SA_Open_PctChange_Lag_9", "CXSE3.SA_High_PctChange_Lag_9", "CXSE3.SA_Low_PctChange_Lag_9", "CXSE3.SA_Close_PctChange_Lag_9", "CXSE3.SA_Open_PctChange_Lag_10", "CXSE3.SA_High_PctChange_Lag_10", "CXSE3.SA_Low_PctChange_Lag_10", "CXSE3.SA_Close_PctChange_Lag_10", "CXSE3.SA_MM10_PctChange", "CXSE3.SA_Diff_OHLC_MM10", "CXSE3.SA_MM25_PctChange", "CXSE3.SA_Diff_OHLC_MM25", "CXSE3.SA_MM100_PctChange", "CXSE3.SA_Diff_OHLC_MM100", "CXSE3.SA_MM10_Menos_MM25", "CXSE3.SA_MM25_Menos_MM100", "CXSE3.SA_MM10_Menos_MM100", "CXSE3.SA_Volume_Lag_1", "CXSE3.SA_Volume_Lag_2", "CXSE3.SA_Volume_Lag_3", "CXSE3.SA_Volume_Lag_4", "CXSE3.SA_Volume_Lag_5", "CXSE3.SA_Spread_Lag_1", "CXSE3.SA_Spread_Lag_2", "CXSE3.SA_Spread_Lag_3", "CXSE3.SA_Spread_Lag_4", "CXSE3.SA_Spread_Lag_5", "CXSE3.SA_Volatilidade_Lag_1", "CXSE3.SA_Volatilidade_Lag_2", "CXSE3.SA_Volatilidade_Lag_3", "CXSE3.SA_Volatilidade_Lag_4", "CXSE3.SA_Volatilidade_Lag_5", "CXSE3.SA_Parkinson_Volatility_Lag_1", "CXSE3.SA_Parkinson_Volatility_Lag_2", "CXSE3.SA_Parkinson_Volatility_Lag_3", "CXSE3.SA_Parkinson_Volatility_Lag_4", "CXSE3.<PERSON>_Parkinson_Volatility_Lag_5", "CXSE3.SA_EMV_Lag_1", "CXSE3.SA_EMV_Lag_2", "CXSE3.SA_EMV_Lag_3", "CXSE3.SA_EMV_Lag_4", "CXSE3.SA_EMV_Lag_5", "CXSE3.SA_EMV_MA_Lag_1", "CXSE3.SA_EMV_MA_Lag_2", "CXSE3.SA_EMV_MA_Lag_3", "CXSE3.SA_EMV_MA_Lag_4", "CXSE3.SA_EMV_MA_Lag_5", "CXSE3.SA_VO_Lag_1", "CXSE3.SA_VO_Lag_2", "CXSE3.SA_VO_Lag_3", "CXSE3.SA_VO_Lag_4", "CXSE3.SA_VO_Lag_5", "CXSE3.SA_High_Max_50_Lag_1", "CXSE3.SA_High_Max_50_Lag_2", "CXSE3.SA_High_Max_50_Lag_3", "CXSE3.SA_High_Max_50_Lag_4", "CXSE3.SA_High_Max_50_Lag_5", "CXSE3.SA_Low_Min_50_Lag_1", "CXSE3.SA_Low_Min_50_Lag_2", "CXSE3.SA_Low_Min_50_Lag_3", "CXSE3.SA_Low_Min_50_Lag_4", "CXSE3.SA_Low_Min_50_Lag_5", "CXSE3.SA_MFI_Lag_1", "CXSE3.SA_MFI_Lag_2", "CXSE3.SA_MFI_Lag_3", "CXSE3.SA_MFI_Lag_4", "CXSE3.SA_MFI_Lag_5", "CXSE3.SA_Amihud_Lag_1", "CXSE3.SA_Amihud_Lag_2", "CXSE3.SA_Amihud_Lag_3", "CXSE3.SA_Amihud_Lag_4", "CXSE3.SA_Amihud_Lag_5", "CXSE3.SA_Roll_Spread_Lag_1", "CXSE3.SA_Roll_Spread_Lag_2", "CXSE3.SA_Roll_Spread_Lag_3", "CXSE3.SA_Roll_Spread_Lag_4", "CXSE3.SA_Roll_Spread_Lag_5", "CXSE3.SA_Hurst_Lag_1", "CXSE3.<PERSON>_Hurst_Lag_2", "CXSE3.<PERSON>_Hurst_Lag_3", "CXSE3.<PERSON>_Hu<PERSON>_Lag_4", "CXSE3.<PERSON>_<PERSON><PERSON>_Lag_5", "CXSE3.SA_Vol_per_Volume_Lag_1", "CXSE3.SA_Vol_per_Volume_Lag_2", "CXSE3.SA_Vol_per_Volume_Lag_3", "CXSE3.SA_Vol_per_Volume_Lag_4", "CXSE3.SA_Vol_per_Volume_Lag_5", "CXSE3.SA_CMF_Lag_1", "CXSE3.SA_CMF_Lag_2", "CXSE3.SA_CMF_Lag_3", "CXSE3.SA_CMF_Lag_4", "CXSE3.SA_CMF_Lag_5", "CXSE3.SA_AD_Line_Lag_1", "CXSE3.SA_AD_Line_Lag_2", "CXSE3.SA_AD_Line_Lag_3", "CXSE3.SA_AD_Line_Lag_4", "CXSE3.SA_AD_Line_Lag_5", "MELK3.SA_Close", "MELK3.SA_High", "MELK3.SA_Low", "MELK3.SA_Open", "MELK3.SA_Volume", "MELK3.SA_Media_OHLC", "MELK3.SA_MM10", "MELK3.SA_MM25", "MELK3.SA_MM100", "MELK3.SA_Media_OHLC_PctChange", "MELK3.SA_Open_PctChange", "MELK3.SA_High_PctChange", "MELK3.SA_Low_PctChange", "MELK3.SA_Close_PctChange", "MELK3.SA_Close_PctChange_Historical", "MELK3.SA_Volatilidade", "MELK3.SA_Spread", "MELK3.SA_Parkinson_Volatility", "MELK3.SA_MFI", "MELK3.SA_MFI_Historical", "MELK3.SA_EMV", "MELK3.SA_EMV_MA", "MELK3.SA_Amihud", "MELK3.SA_Amihud_Historical", "MELK3.SA_Roll_Spread", "MELK3.SA_Roll_Spread_Historical", "MELK3.SA_Hurst", "MELK3.SA_<PERSON><PERSON>_Historical", "MELK3.SA_Vol_per_Volume", "MELK3.SA_Vol_per_Volume_Historical", "MELK3.SA_CMF", "MELK3.SA_CMF_Historical", "MELK3.SA_AD_Line", "MELK3.SA_AD_Line_Historical", "MELK3.SA_VO", "MELK3.SA_High_Max_50", "MELK3.SA_Low_Min_50", "MELK3.SA_Segunda", "MELK3.SA_Terca", "MELK3.SA_Quarta", "MELK3.SA_Quinta", "MELK3.SA_Sexta", "MELK3.SA_Mes_1", "MELK3.SA_Mes_2", "MELK3.SA_Mes_3", "MELK3.SA_Mes_4", "MELK3.SA_Mes_5", "MELK3.SA_Mes_6", "MELK3.SA_Mes_7", "MELK3.SA_Mes_8", "MELK3.SA_Mes_9", "MELK3.SA_Mes_10", "MELK3.SA_Mes_11", "MELK3.SA_Mes_12", "MELK3.SA_Quarter_1", "MELK3.SA_Quarter_2", "MELK3.SA_Quarter_3", "MELK3.SA_Quarter_4", "MELK3.SA_Last_Day_Quarter", "MELK3.SA_Pre_Feriado_Brasil", "MELK3.SA_Media_OHLC_Anterior", "MELK3.SA_Sinal_Compra", "MELK3.SA_Sinal_Venda", "MELK3.SA_Media_OHLC_Futura", "MELK3.SA_Open_PctChange_Lag_1", "MELK3.SA_High_PctChange_Lag_1", "MELK3.SA_Low_PctChange_Lag_1", "MELK3.SA_Close_PctChange_Lag_1", "MELK3.SA_Open_PctChange_Lag_2", "MELK3.SA_High_PctChange_Lag_2", "MELK3.SA_Low_PctChange_Lag_2", "MELK3.SA_Close_PctChange_Lag_2", "MELK3.SA_Open_PctChange_Lag_3", "MELK3.SA_High_PctChange_Lag_3", "MELK3.SA_Low_PctChange_Lag_3", "MELK3.SA_Close_PctChange_Lag_3", "MELK3.SA_Open_PctChange_Lag_4", "MELK3.SA_High_PctChange_Lag_4", "MELK3.SA_Low_PctChange_Lag_4", "MELK3.SA_Close_PctChange_Lag_4", "MELK3.SA_Open_PctChange_Lag_5", "MELK3.SA_High_PctChange_Lag_5", "MELK3.SA_Low_PctChange_Lag_5", "MELK3.SA_Close_PctChange_Lag_5", "MELK3.SA_Open_PctChange_Lag_6", "MELK3.SA_High_PctChange_Lag_6", "MELK3.SA_Low_PctChange_Lag_6", "MELK3.SA_Close_PctChange_Lag_6", "MELK3.SA_Open_PctChange_Lag_7", "MELK3.SA_High_PctChange_Lag_7", "MELK3.SA_Low_PctChange_Lag_7", "MELK3.SA_Close_PctChange_Lag_7", "MELK3.SA_Open_PctChange_Lag_8", "MELK3.SA_High_PctChange_Lag_8", "MELK3.SA_Low_PctChange_Lag_8", "MELK3.SA_Close_PctChange_Lag_8", "MELK3.SA_Open_PctChange_Lag_9", "MELK3.SA_High_PctChange_Lag_9", "MELK3.SA_Low_PctChange_Lag_9", "MELK3.SA_Close_PctChange_Lag_9", "MELK3.SA_Open_PctChange_Lag_10", "MELK3.SA_High_PctChange_Lag_10", "MELK3.SA_Low_PctChange_Lag_10", "MELK3.SA_Close_PctChange_Lag_10", "MELK3.SA_MM10_PctChange", "MELK3.SA_Diff_OHLC_MM10", "MELK3.SA_MM25_PctChange", "MELK3.SA_Diff_OHLC_MM25", "MELK3.SA_MM100_PctChange", "MELK3.SA_Diff_OHLC_MM100", "MELK3.SA_MM10_Menos_MM25", "MELK3.SA_MM25_Menos_MM100", "MELK3.SA_MM10_Menos_MM100", "MELK3.SA_Volume_Lag_1", "MELK3.SA_Volume_Lag_2", "MELK3.SA_Volume_Lag_3", "MELK3.SA_Volume_Lag_4", "MELK3.SA_Volume_Lag_5", "MELK3.SA_Spread_Lag_1", "MELK3.SA_Spread_Lag_2", "MELK3.SA_Spread_Lag_3", "MELK3.SA_Spread_Lag_4", "MELK3.SA_Spread_Lag_5", "MELK3.SA_Volatilidade_Lag_1", "MELK3.SA_Volatilidade_Lag_2", "MELK3.SA_Volatilidade_Lag_3", "MELK3.SA_Volatilidade_Lag_4", "MELK3.SA_Volatilidade_Lag_5", "MELK3.SA_Parkinson_Volatility_Lag_1", "MELK3.SA_Parkinson_Volatility_Lag_2", "MELK3.SA_Parkinson_Volatility_Lag_3", "MELK3.SA_Parkinson_Volatility_Lag_4", "MELK3.<PERSON>_Parkinson_Volatility_Lag_5", "MELK3.SA_EMV_Lag_1", "MELK3.SA_EMV_Lag_2", "MELK3.SA_EMV_Lag_3", "MELK3.SA_EMV_Lag_4", "MELK3.SA_EMV_Lag_5", "MELK3.SA_EMV_MA_Lag_1", "MELK3.SA_EMV_MA_Lag_2", "MELK3.SA_EMV_MA_Lag_3", "MELK3.SA_EMV_MA_Lag_4", "MELK3.SA_EMV_MA_Lag_5", "MELK3.SA_VO_Lag_1", "MELK3.SA_VO_Lag_2", "MELK3.SA_VO_Lag_3", "MELK3.SA_VO_Lag_4", "MELK3.SA_VO_Lag_5", "MELK3.SA_High_Max_50_Lag_1", "MELK3.SA_High_Max_50_Lag_2", "MELK3.SA_High_Max_50_Lag_3", "MELK3.SA_High_Max_50_Lag_4", "MELK3.SA_High_Max_50_Lag_5", "MELK3.SA_Low_Min_50_Lag_1", "MELK3.SA_Low_Min_50_Lag_2", "MELK3.SA_Low_Min_50_Lag_3", "MELK3.SA_Low_Min_50_Lag_4", "MELK3.SA_Low_Min_50_Lag_5", "MELK3.SA_MFI_Lag_1", "MELK3.SA_MFI_Lag_2", "MELK3.SA_MFI_Lag_3", "MELK3.SA_MFI_Lag_4", "MELK3.SA_MFI_Lag_5", "MELK3.SA_Amihud_Lag_1", "MELK3.SA_Amihud_Lag_2", "MELK3.SA_Amihud_Lag_3", "MELK3.SA_Amihud_Lag_4", "MELK3.SA_Amihud_Lag_5", "MELK3.SA_Roll_Spread_Lag_1", "MELK3.SA_Roll_Spread_Lag_2", "MELK3.SA_Roll_Spread_Lag_3", "MELK3.SA_Roll_Spread_Lag_4", "MELK3.SA_Roll_Spread_Lag_5", "MELK3.SA_Hurst_Lag_1", "MELK3.<PERSON>_Hu<PERSON>_Lag_2", "MELK3.SA_Hurst_Lag_3", "MELK3.<PERSON>_<PERSON><PERSON>_Lag_4", "MELK3.<PERSON>_<PERSON><PERSON>_Lag_5", "MELK3.SA_Vol_per_Volume_Lag_1", "MELK3.SA_Vol_per_Volume_Lag_2", "MELK3.SA_Vol_per_Volume_Lag_3", "MELK3.SA_Vol_per_Volume_Lag_4", "MELK3.SA_Vol_per_Volume_Lag_5", "MELK3.SA_CMF_Lag_1", "MELK3.SA_CMF_Lag_2", "MELK3.SA_CMF_Lag_3", "MELK3.SA_CMF_Lag_4", "MELK3.SA_CMF_Lag_5", "MELK3.SA_AD_Line_Lag_1", "MELK3.SA_AD_Line_Lag_2", "MELK3.SA_AD_Line_Lag_3", "MELK3.SA_AD_Line_Lag_4", "MELK3.SA_AD_Line_Lag_5", "PSSA3.SA_Open", "PSSA3.SA_High", "PSSA3.SA_Low", "PSSA3.SA_Close", "PSSA3.SA_Volume", "PSSA3.SA_Media_OHLC", "PSSA3.SA_MM10", "PSSA3.SA_MM25", "PSSA3.SA_MM100", "PSSA3.SA_Media_OHLC_PctChange", "PSSA3.SA_Open_PctChange", "PSSA3.SA_High_PctChange", "PSSA3.SA_Low_PctChange", "PSSA3.SA_Close_PctChange", "PSSA3.SA_Close_PctChange_Historical", "PSSA3.SA_Volatilidade", "PSSA3.SA_Spread", "PSSA3.SA_Parkinson_Volatility", "PSSA3.SA_MFI", "PSSA3.SA_MFI_Historical", "PSSA3.SA_EMV", "PSSA3.SA_EMV_MA", "PSSA3.SA_Amihud", "PSSA3.SA_Amihud_Historical", "PSSA3.SA_Roll_Spread", "PSSA3.SA_Roll_Spread_Historical", "PSSA3.SA_Hurst", "PSSA3.SA_Hurst_Historical", "PSSA3.SA_Vol_per_Volume", "PSSA3.SA_Vol_per_Volume_Historical", "PSSA3.SA_CMF", "PSSA3.SA_CMF_Historical", "PSSA3.SA_AD_Line", "PSSA3.SA_AD_Line_Historical", "PSSA3.SA_VO", "PSSA3.SA_High_Max_50", "PSSA3.SA_Low_Min_50", "PSSA3.SA_Segunda", "PSSA3.SA_Terca", "PSSA3.SA_Quarta", "PSSA3.SA_Quinta", "PSSA3.SA_Sexta", "PSSA3.SA_Mes_1", "PSSA3.SA_Mes_2", "PSSA3.SA_Mes_3", "PSSA3.SA_Mes_4", "PSSA3.SA_Mes_5", "PSSA3.SA_Mes_6", "PSSA3.SA_Mes_7", "PSSA3.SA_Mes_8", "PSSA3.SA_Mes_9", "PSSA3.SA_Mes_10", "PSSA3.SA_Mes_11", "PSSA3.SA_Mes_12", "PSSA3.SA_Quarter_1", "PSSA3.SA_Quarter_2", "PSSA3.SA_Quarter_3", "PSSA3.SA_Quarter_4", "PSSA3.SA_Last_Day_Quarter", "PSSA3.SA_Pre_Feriado_Brasil", "PSSA3.SA_Media_OHLC_Anterior", "PSSA3.SA_Sinal_Compra", "PSSA3.SA_Sinal_Venda", "PSSA3.SA_Media_OHLC_Futura", "PSSA3.SA_Open_PctChange_Lag_1", "PSSA3.SA_High_PctChange_Lag_1", "PSSA3.SA_Low_PctChange_Lag_1", "PSSA3.SA_Close_PctChange_Lag_1", "PSSA3.SA_Open_PctChange_Lag_2", "PSSA3.SA_High_PctChange_Lag_2", "PSSA3.SA_Low_PctChange_Lag_2", "PSSA3.SA_Close_PctChange_Lag_2", "PSSA3.SA_Open_PctChange_Lag_3", "PSSA3.SA_High_PctChange_Lag_3", "PSSA3.SA_Low_PctChange_Lag_3", "PSSA3.SA_Close_PctChange_Lag_3", "PSSA3.SA_Open_PctChange_Lag_4", "PSSA3.SA_High_PctChange_Lag_4", "PSSA3.SA_Low_PctChange_Lag_4", "PSSA3.SA_Close_PctChange_Lag_4", "PSSA3.SA_Open_PctChange_Lag_5", "PSSA3.SA_High_PctChange_Lag_5", "PSSA3.SA_Low_PctChange_Lag_5", "PSSA3.SA_Close_PctChange_Lag_5", "PSSA3.SA_Open_PctChange_Lag_6", "PSSA3.SA_High_PctChange_Lag_6", "PSSA3.SA_Low_PctChange_Lag_6", "PSSA3.SA_Close_PctChange_Lag_6", "PSSA3.SA_Open_PctChange_Lag_7", "PSSA3.SA_High_PctChange_Lag_7", "PSSA3.SA_Low_PctChange_Lag_7", "PSSA3.SA_Close_PctChange_Lag_7", "PSSA3.SA_Open_PctChange_Lag_8", "PSSA3.SA_High_PctChange_Lag_8", "PSSA3.SA_Low_PctChange_Lag_8", "PSSA3.SA_Close_PctChange_Lag_8", "PSSA3.SA_Open_PctChange_Lag_9", "PSSA3.SA_High_PctChange_Lag_9", "PSSA3.SA_Low_PctChange_Lag_9", "PSSA3.SA_Close_PctChange_Lag_9", "PSSA3.SA_Open_PctChange_Lag_10", "PSSA3.SA_High_PctChange_Lag_10", "PSSA3.SA_Low_PctChange_Lag_10", "PSSA3.SA_Close_PctChange_Lag_10", "PSSA3.SA_MM10_PctChange", "PSSA3.SA_Diff_OHLC_MM10", "PSSA3.SA_MM25_PctChange", "PSSA3.SA_Diff_OHLC_MM25", "PSSA3.SA_MM100_PctChange", "PSSA3.SA_Diff_OHLC_MM100", "PSSA3.SA_MM10_Menos_MM25", "PSSA3.SA_MM25_Menos_MM100", "PSSA3.SA_MM10_Menos_MM100", "PSSA3.SA_Volume_Lag_1", "PSSA3.SA_Volume_Lag_2", "PSSA3.SA_Volume_Lag_3", "PSSA3.SA_Volume_Lag_4", "PSSA3.SA_Volume_Lag_5", "PSSA3.SA_Spread_Lag_1", "PSSA3.SA_Spread_Lag_2", "PSSA3.SA_Spread_Lag_3", "PSSA3.SA_Spread_Lag_4", "PSSA3.SA_Spread_Lag_5", "PSSA3.SA_Volatilidade_Lag_1", "PSSA3.SA_Volatilidade_Lag_2", "PSSA3.SA_Volatilidade_Lag_3", "PSSA3.SA_Volatilidade_Lag_4", "PSSA3.SA_Volatilidade_Lag_5", "PSSA3.SA_Parkinson_Volatility_Lag_1", "PSSA3.SA_Parkinson_Volatility_Lag_2", "PSSA3.SA_Parkinson_Volatility_Lag_3", "PSSA3.SA_Parkinson_Volatility_Lag_4", "PSSA3.SA_Parkinson_Volatility_Lag_5", "PSSA3.SA_EMV_Lag_1", "PSSA3.SA_EMV_Lag_2", "PSSA3.SA_EMV_Lag_3", "PSSA3.SA_EMV_Lag_4", "PSSA3.SA_EMV_Lag_5", "PSSA3.SA_EMV_MA_Lag_1", "PSSA3.SA_EMV_MA_Lag_2", "PSSA3.SA_EMV_MA_Lag_3", "PSSA3.SA_EMV_MA_Lag_4", "PSSA3.SA_EMV_MA_Lag_5", "PSSA3.SA_VO_Lag_1", "PSSA3.SA_VO_Lag_2", "PSSA3.SA_VO_Lag_3", "PSSA3.SA_VO_Lag_4", "PSSA3.SA_VO_Lag_5", "PSSA3.SA_High_Max_50_Lag_1", "PSSA3.SA_High_Max_50_Lag_2", "PSSA3.SA_High_Max_50_Lag_3", "PSSA3.SA_High_Max_50_Lag_4", "PSSA3.SA_High_Max_50_Lag_5", "PSSA3.SA_Low_Min_50_Lag_1", "PSSA3.SA_Low_Min_50_Lag_2", "PSSA3.SA_Low_Min_50_Lag_3", "PSSA3.SA_Low_Min_50_Lag_4", "PSSA3.SA_Low_Min_50_Lag_5", "PSSA3.SA_MFI_Lag_1", "PSSA3.SA_MFI_Lag_2", "PSSA3.SA_MFI_Lag_3", "PSSA3.SA_MFI_Lag_4", "PSSA3.SA_MFI_Lag_5", "PSSA3.SA_Amihud_Lag_1", "PSSA3.SA_Amihud_Lag_2", "PSSA3.SA_Amihud_Lag_3", "PSSA3.SA_Amihud_Lag_4", "PSSA3.SA_Amihud_Lag_5", "PSSA3.SA_Roll_Spread_Lag_1", "PSSA3.SA_Roll_Spread_Lag_2", "PSSA3.SA_Roll_Spread_Lag_3", "PSSA3.SA_Roll_Spread_Lag_4", "PSSA3.SA_Roll_Spread_Lag_5", "PSSA3.SA_Hurst_Lag_1", "PSSA3.SA_Hurst_Lag_2", "PSSA3.SA_Hurst_Lag_3", "PSSA3.SA_Hurst_Lag_4", "PSSA3.SA_Hurst_Lag_5", "PSSA3.SA_Vol_per_Volume_Lag_1", "PSSA3.SA_Vol_per_Volume_Lag_2", "PSSA3.SA_Vol_per_Volume_Lag_3", "PSSA3.SA_Vol_per_Volume_Lag_4", "PSSA3.SA_Vol_per_Volume_Lag_5", "PSSA3.SA_CMF_Lag_1", "PSSA3.SA_CMF_Lag_2", "PSSA3.SA_CMF_Lag_3", "PSSA3.SA_CMF_Lag_4", "PSSA3.SA_CMF_Lag_5", "PSSA3.SA_AD_Line_Lag_1", "PSSA3.SA_AD_Line_Lag_2", "PSSA3.SA_AD_Line_Lag_3", "PSSA3.SA_AD_Line_Lag_4", "PSSA3.SA_AD_Line_Lag_5", "GRND3.SA_Open", "GRND3.SA_High", "GRND3.SA_Low", "GRND3.SA_Close", "GRND3.SA_Volume", "GRND3.SA_Media_OHLC", "GRND3.SA_MM10", "GRND3.SA_MM25", "GRND3.SA_MM100", "GRND3.SA_Media_OHLC_PctChange", "GRND3.SA_Open_PctChange", "GRND3.SA_High_PctChange", "GRND3.SA_Low_PctChange", "GRND3.SA_Close_PctChange", "GRND3.SA_Close_PctChange_Historical", "GRND3.SA_Volatilidade", "GRND3.SA_Spread", "GRND3.SA_Parkinson_Volatility", "GRND3.SA_MFI", "GRND3.SA_MFI_Historical", "GRND3.SA_EMV", "GRND3.SA_EMV_MA", "GRND3.SA_Amihud", "GRND3.SA_Amihud_Historical", "GRND3.SA_Roll_Spread", "GRND3.SA_Roll_Spread_Historical", "GRND3.SA_Hurst", "GRND3.<PERSON>_<PERSON><PERSON>_Historical", "GRND3.SA_Vol_per_Volume", "GRND3.SA_Vol_per_Volume_Historical", "GRND3.SA_CMF", "GRND3.SA_CMF_Historical", "GRND3.SA_AD_Line", "GRND3.SA_AD_Line_Historical", "GRND3.SA_VO", "GRND3.SA_High_Max_50", "GRND3.SA_Low_Min_50", "GRND3.SA_Segunda", "GRND3.SA_Terca", "GRND3.SA_Quarta", "GRND3.SA_Quinta", "GRND3.SA_Sexta", "GRND3.SA_Mes_1", "GRND3.SA_Mes_2", "GRND3.SA_Mes_3", "GRND3.SA_Mes_4", "GRND3.SA_Mes_5", "GRND3.SA_Mes_6", "GRND3.SA_Mes_7", "GRND3.SA_Mes_8", "GRND3.SA_Mes_9", "GRND3.SA_Mes_10", "GRND3.SA_Mes_11", "GRND3.SA_Mes_12", "GRND3.SA_Quarter_1", "GRND3.SA_Quarter_2", "GRND3.SA_Quarter_3", "GRND3.SA_Quarter_4", "GRND3.SA_Last_Day_Quarter", "GRND3.SA_Pre_Feriado_Brasil", "GRND3.SA_Media_OHLC_Anterior", "GRND3.SA_Sinal_Compra", "GRND3.SA_Sinal_Venda", "GRND3.SA_Media_OHLC_Futura", "GRND3.SA_Open_PctChange_Lag_1", "GRND3.SA_High_PctChange_Lag_1", "GRND3.SA_Low_PctChange_Lag_1", "GRND3.SA_Close_PctChange_Lag_1", "GRND3.SA_Open_PctChange_Lag_2", "GRND3.SA_High_PctChange_Lag_2", "GRND3.SA_Low_PctChange_Lag_2", "GRND3.SA_Close_PctChange_Lag_2", "GRND3.SA_Open_PctChange_Lag_3", "GRND3.SA_High_PctChange_Lag_3", "GRND3.SA_Low_PctChange_Lag_3", "GRND3.SA_Close_PctChange_Lag_3", "GRND3.SA_Open_PctChange_Lag_4", "GRND3.SA_High_PctChange_Lag_4", "GRND3.SA_Low_PctChange_Lag_4", "GRND3.SA_Close_PctChange_Lag_4", "GRND3.SA_Open_PctChange_Lag_5", "GRND3.SA_High_PctChange_Lag_5", "GRND3.SA_Low_PctChange_Lag_5", "GRND3.SA_Close_PctChange_Lag_5", "GRND3.SA_Open_PctChange_Lag_6", "GRND3.SA_High_PctChange_Lag_6", "GRND3.SA_Low_PctChange_Lag_6", "GRND3.SA_Close_PctChange_Lag_6", "GRND3.SA_Open_PctChange_Lag_7", "GRND3.SA_High_PctChange_Lag_7", "GRND3.SA_Low_PctChange_Lag_7", "GRND3.SA_Close_PctChange_Lag_7", "GRND3.SA_Open_PctChange_Lag_8", "GRND3.SA_High_PctChange_Lag_8", "GRND3.SA_Low_PctChange_Lag_8", "GRND3.SA_Close_PctChange_Lag_8", "GRND3.SA_Open_PctChange_Lag_9", "GRND3.SA_High_PctChange_Lag_9", "GRND3.SA_Low_PctChange_Lag_9", "GRND3.SA_Close_PctChange_Lag_9", "GRND3.SA_Open_PctChange_Lag_10", "GRND3.SA_High_PctChange_Lag_10", "GRND3.SA_Low_PctChange_Lag_10", "GRND3.SA_Close_PctChange_Lag_10", "GRND3.SA_MM10_PctChange", "GRND3.SA_Diff_OHLC_MM10", "GRND3.SA_MM25_PctChange", "GRND3.SA_Diff_OHLC_MM25", "GRND3.SA_MM100_PctChange", "GRND3.SA_Diff_OHLC_MM100", "GRND3.SA_MM10_Menos_MM25", "GRND3.SA_MM25_Menos_MM100", "GRND3.SA_MM10_Menos_MM100", "GRND3.SA_Volume_Lag_1", "GRND3.SA_Volume_Lag_2", "GRND3.SA_Volume_Lag_3", "GRND3.SA_Volume_Lag_4", "GRND3.SA_Volume_Lag_5", "GRND3.SA_Spread_Lag_1", "GRND3.SA_Spread_Lag_2", "GRND3.SA_Spread_Lag_3", "GRND3.SA_Spread_Lag_4", "GRND3.SA_Spread_Lag_5", "GRND3.SA_Volatilidade_Lag_1", "GRND3.SA_Volatilidade_Lag_2", "GRND3.SA_Volatilidade_Lag_3", "GRND3.SA_Volatilidade_Lag_4", "GRND3.SA_Volatilidade_Lag_5", "GRND3.SA_Parkinson_Volatility_Lag_1", "GRND3.<PERSON>_Parkinson_Volatility_Lag_2", "GRND3.SA_Parkinson_Volatility_Lag_3", "GRND3.SA_Parkinson_Volatility_Lag_4", "GRND3.<PERSON>_Parkinson_Volatility_Lag_5", "GRND3.SA_EMV_Lag_1", "GRND3.SA_EMV_Lag_2", "GRND3.SA_EMV_Lag_3", "GRND3.SA_EMV_Lag_4", "GRND3.SA_EMV_Lag_5", "GRND3.SA_EMV_MA_Lag_1", "GRND3.SA_EMV_MA_Lag_2", "GRND3.SA_EMV_MA_Lag_3", "GRND3.SA_EMV_MA_Lag_4", "GRND3.SA_EMV_MA_Lag_5", "GRND3.SA_VO_Lag_1", "GRND3.SA_VO_Lag_2", "GRND3.SA_VO_Lag_3", "GRND3.SA_VO_Lag_4", "GRND3.SA_VO_Lag_5", "GRND3.SA_High_Max_50_Lag_1", "GRND3.SA_High_Max_50_Lag_2", "GRND3.SA_High_Max_50_Lag_3", "GRND3.SA_High_Max_50_Lag_4", "GRND3.SA_High_Max_50_Lag_5", "GRND3.SA_Low_Min_50_Lag_1", "GRND3.SA_Low_Min_50_Lag_2", "GRND3.SA_Low_Min_50_Lag_3", "GRND3.SA_Low_Min_50_Lag_4", "GRND3.SA_Low_Min_50_Lag_5", "GRND3.SA_MFI_Lag_1", "GRND3.SA_MFI_Lag_2", "GRND3.SA_MFI_Lag_3", "GRND3.SA_MFI_Lag_4", "GRND3.SA_MFI_Lag_5", "GRND3.SA_Amihud_Lag_1", "GRND3.SA_Amihud_Lag_2", "GRND3.SA_Amihud_Lag_3", "GRND3.SA_Amihud_Lag_4", "GRND3.SA_Amihud_Lag_5", "GRND3.SA_Roll_Spread_Lag_1", "GRND3.SA_Roll_Spread_Lag_2", "GRND3.SA_Roll_Spread_Lag_3", "GRND3.SA_Roll_Spread_Lag_4", "GRND3.SA_Roll_Spread_Lag_5", "GRND3.SA_Hurst_Lag_1", "GRND3.<PERSON>_<PERSON><PERSON>_Lag_2", "GRND3.<PERSON>_<PERSON><PERSON>_Lag_3", "GRND3.<PERSON>_<PERSON><PERSON>_Lag_4", "GRND3.<PERSON>_<PERSON><PERSON>_Lag_5", "GRND3.SA_Vol_per_Volume_Lag_1", "GRND3.SA_Vol_per_Volume_Lag_2", "GRND3.SA_Vol_per_Volume_Lag_3", "GRND3.SA_Vol_per_Volume_Lag_4", "GRND3.SA_Vol_per_Volume_Lag_5", "GRND3.SA_CMF_Lag_1", "GRND3.SA_CMF_Lag_2", "GRND3.SA_CMF_Lag_3", "GRND3.SA_CMF_Lag_4", "GRND3.SA_CMF_Lag_5", "GRND3.SA_AD_Line_Lag_1", "GRND3.SA_AD_Line_Lag_2", "GRND3.SA_AD_Line_Lag_3", "GRND3.SA_AD_Line_Lag_4", "GRND3.SA_AD_Line_Lag_5", "POMO4.SA_Open", "POMO4.SA_High", "POMO4.SA_Low", "POMO4.SA_Close", "POMO4.SA_Volume", "POMO4.SA_Media_OHLC", "POMO4.SA_MM10", "POMO4.SA_MM25", "POMO4.SA_MM100", "POMO4.SA_Media_OHLC_PctChange", "POMO4.SA_Open_PctChange", "POMO4.SA_High_PctChange", "POMO4.SA_Low_PctChange", "POMO4.SA_Close_PctChange", "POMO4.SA_Close_PctChange_Historical", "POMO4.SA_Volatilidade", "POMO4.SA_Spread", "POMO4.SA_Parkinson_Volatility", "POMO4.SA_MFI", "POMO4.SA_MFI_Historical", "POMO4.SA_EMV", "POMO4.SA_EMV_MA", "POMO4.SA_Amihud", "POMO4.SA_Amihud_Historical", "POMO4.SA_Roll_Spread", "POMO4.SA_Roll_Spread_Historical", "POMO4.SA_Hurst", "POMO4.<PERSON>_<PERSON><PERSON>_Historical", "POMO4.SA_Vol_per_Volume", "POMO4.SA_Vol_per_Volume_Historical", "POMO4.SA_CMF", "POMO4.SA_CMF_Historical", "POMO4.SA_AD_Line", "POMO4.SA_AD_Line_Historical", "POMO4.SA_VO", "POMO4.SA_High_Max_50", "POMO4.SA_Low_Min_50", "POMO4.SA_Segunda", "POMO4.SA_Terca", "POMO4.SA_Quarta", "POMO4.SA_Quinta", "POMO4.SA_Sexta", "POMO4.SA_Mes_1", "POMO4.SA_Mes_2", "POMO4.SA_Mes_3", "POMO4.SA_Mes_4", "POMO4.SA_Mes_5", "POMO4.SA_Mes_6", "POMO4.SA_Mes_7", "POMO4.SA_Mes_8", "POMO4.SA_Mes_9", "POMO4.SA_Mes_10", "POMO4.SA_Mes_11", "POMO4.SA_Mes_12", "POMO4.SA_Quarter_1", "POMO4.SA_Quarter_2", "POMO4.SA_Quarter_3", "POMO4.SA_Quarter_4", "POMO4.SA_Last_Day_Quarter", "POMO4.SA_Pre_Feriado_Brasil", "POMO4.SA_Media_OHLC_Anterior", "POMO4.SA_Sinal_Compra", "POMO4.SA_Sinal_Venda", "POMO4.SA_Media_OHLC_Futura", "POMO4.SA_Open_PctChange_Lag_1", "POMO4.SA_High_PctChange_Lag_1", "POMO4.SA_Low_PctChange_Lag_1", "POMO4.SA_Close_PctChange_Lag_1", "POMO4.SA_Open_PctChange_Lag_2", "POMO4.SA_High_PctChange_Lag_2", "POMO4.SA_Low_PctChange_Lag_2", "POMO4.SA_Close_PctChange_Lag_2", "POMO4.SA_Open_PctChange_Lag_3", "POMO4.SA_High_PctChange_Lag_3", "POMO4.SA_Low_PctChange_Lag_3", "POMO4.SA_Close_PctChange_Lag_3", "POMO4.SA_Open_PctChange_Lag_4", "POMO4.SA_High_PctChange_Lag_4", "POMO4.SA_Low_PctChange_Lag_4", "POMO4.SA_Close_PctChange_Lag_4", "POMO4.SA_Open_PctChange_Lag_5", "POMO4.SA_High_PctChange_Lag_5", "POMO4.SA_Low_PctChange_Lag_5", "POMO4.SA_Close_PctChange_Lag_5", "POMO4.SA_Open_PctChange_Lag_6", "POMO4.SA_High_PctChange_Lag_6", "POMO4.SA_Low_PctChange_Lag_6", "POMO4.SA_Close_PctChange_Lag_6", "POMO4.SA_Open_PctChange_Lag_7", "POMO4.SA_High_PctChange_Lag_7", "POMO4.SA_Low_PctChange_Lag_7", "POMO4.SA_Close_PctChange_Lag_7", "POMO4.SA_Open_PctChange_Lag_8", "POMO4.SA_High_PctChange_Lag_8", "POMO4.SA_Low_PctChange_Lag_8", "POMO4.SA_Close_PctChange_Lag_8", "POMO4.SA_Open_PctChange_Lag_9", "POMO4.SA_High_PctChange_Lag_9", "POMO4.SA_Low_PctChange_Lag_9", "POMO4.SA_Close_PctChange_Lag_9", "POMO4.SA_Open_PctChange_Lag_10", "POMO4.SA_High_PctChange_Lag_10", "POMO4.SA_Low_PctChange_Lag_10", "POMO4.SA_Close_PctChange_Lag_10", "POMO4.SA_MM10_PctChange", "POMO4.SA_Diff_OHLC_MM10", "POMO4.SA_MM25_PctChange", "POMO4.SA_Diff_OHLC_MM25", "POMO4.SA_MM100_PctChange", "POMO4.SA_Diff_OHLC_MM100", "POMO4.SA_MM10_Menos_MM25", "POMO4.SA_MM25_Menos_MM100", "POMO4.SA_MM10_Menos_MM100", "POMO4.SA_Volume_Lag_1", "POMO4.SA_Volume_Lag_2", "POMO4.SA_Volume_Lag_3", "POMO4.SA_Volume_Lag_4", "POMO4.SA_Volume_Lag_5", "POMO4.SA_Spread_Lag_1", "POMO4.SA_Spread_Lag_2", "POMO4.SA_Spread_Lag_3", "POMO4.SA_Spread_Lag_4", "POMO4.SA_Spread_Lag_5", "POMO4.SA_Volatilidade_Lag_1", "POMO4.SA_Volatilidade_Lag_2", "POMO4.SA_Volatilidade_Lag_3", "POMO4.SA_Volatilidade_Lag_4", "POMO4.SA_Volatilidade_Lag_5", "POMO4.SA_Parkinson_Volatility_Lag_1", "POMO4.<PERSON>_Parkinson_Volatility_Lag_2", "POMO4.SA_Parkinson_Volatility_Lag_3", "POMO4.SA_Parkinson_Volatility_Lag_4", "POMO4.<PERSON>_Parkinson_Volatility_Lag_5", "POMO4.SA_EMV_Lag_1", "POMO4.SA_EMV_Lag_2", "POMO4.SA_EMV_Lag_3", "POMO4.SA_EMV_Lag_4", "POMO4.SA_EMV_Lag_5", "POMO4.SA_EMV_MA_Lag_1", "POMO4.SA_EMV_MA_Lag_2", "POMO4.SA_EMV_MA_Lag_3", "POMO4.SA_EMV_MA_Lag_4", "POMO4.SA_EMV_MA_Lag_5", "POMO4.SA_VO_Lag_1", "POMO4.SA_VO_Lag_2", "POMO4.SA_VO_Lag_3", "POMO4.SA_VO_Lag_4", "POMO4.SA_VO_Lag_5", "POMO4.SA_High_Max_50_Lag_1", "POMO4.SA_High_Max_50_Lag_2", "POMO4.SA_High_Max_50_Lag_3", "POMO4.SA_High_Max_50_Lag_4", "POMO4.SA_High_Max_50_Lag_5", "POMO4.SA_Low_Min_50_Lag_1", "POMO4.SA_Low_Min_50_Lag_2", "POMO4.SA_Low_Min_50_Lag_3", "POMO4.SA_Low_Min_50_Lag_4", "POMO4.SA_Low_Min_50_Lag_5", "POMO4.SA_MFI_Lag_1", "POMO4.SA_MFI_Lag_2", "POMO4.SA_MFI_Lag_3", "POMO4.SA_MFI_Lag_4", "POMO4.SA_MFI_Lag_5", "POMO4.SA_Amihud_Lag_1", "POMO4.SA_Amihud_Lag_2", "POMO4.SA_Amihud_Lag_3", "POMO4.SA_Amihud_Lag_4", "POMO4.SA_Amihud_Lag_5", "POMO4.SA_Roll_Spread_Lag_1", "POMO4.SA_Roll_Spread_Lag_2", "POMO4.SA_Roll_Spread_Lag_3", "POMO4.SA_Roll_Spread_Lag_4", "POMO4.SA_Roll_Spread_Lag_5", "POMO4.<PERSON>_Hu<PERSON>_Lag_1", "POMO4.<PERSON>_<PERSON><PERSON>_Lag_2", "POMO4.<PERSON>_<PERSON><PERSON>_Lag_3", "POMO4.<PERSON>_<PERSON><PERSON>_Lag_4", "POMO4.<PERSON>_<PERSON><PERSON>_Lag_5", "POMO4.SA_Vol_per_Volume_Lag_1", "POMO4.SA_Vol_per_Volume_Lag_2", "POMO4.SA_Vol_per_Volume_Lag_3", "POMO4.SA_Vol_per_Volume_Lag_4", "POMO4.SA_Vol_per_Volume_Lag_5", "POMO4.SA_CMF_Lag_1", "POMO4.SA_CMF_Lag_2", "POMO4.SA_CMF_Lag_3", "POMO4.SA_CMF_Lag_4", "POMO4.SA_CMF_Lag_5", "POMO4.SA_AD_Line_Lag_1", "POMO4.SA_AD_Line_Lag_2", "POMO4.SA_AD_Line_Lag_3", "POMO4.SA_AD_Line_Lag_4", "POMO4.SA_AD_Line_Lag_5", "BRSR6.SA_Open", "BRSR6.SA_High", "BRSR6.SA_Low", "BRSR6.SA_Close", "BRSR6.SA_Volume", "BRSR6.SA_Media_OHLC", "BRSR6.SA_MM10", "BRSR6.SA_MM25", "BRSR6.SA_MM100", "BRSR6.SA_Media_OHLC_PctChange", "BRSR6.SA_Open_PctChange", "BRSR6.SA_High_PctChange", "BRSR6.SA_Low_PctChange", "BRSR6.SA_Close_PctChange", "BRSR6.SA_Close_PctChange_Historical", "BRSR6.SA_Volatilidade", "BRSR6.SA_Spread", "BRSR6.SA_Parkinson_Volatility", "BRSR6.SA_MFI", "BRSR6.SA_MFI_Historical", "BRSR6.SA_EMV", "BRSR6.SA_EMV_MA", "BRSR6.SA_Amihud", "BRSR6.SA_Amihud_Historical", "BRSR6.SA_Roll_Spread", "BRSR6.SA_Roll_Spread_Historical", "BRSR6.SA_Hurst", "BRSR6.SA_Hurst_Historical", "BRSR6.SA_Vol_per_Volume", "BRSR6.SA_Vol_per_Volume_Historical", "BRSR6.SA_CMF", "BRSR6.SA_CMF_Historical", "BRSR6.SA_AD_Line", "BRSR6.SA_AD_Line_Historical", "BRSR6.SA_VO", "BRSR6.SA_High_Max_50", "BRSR6.SA_Low_Min_50", "BRSR6.SA_Segunda", "BRSR6.SA_Terca", "BRSR6.SA_Quarta", "BRSR6.SA_Quinta", "BRSR6.SA_Sexta", "BRSR6.SA_Mes_1", "BRSR6.SA_Mes_2", "BRSR6.SA_Mes_3", "BRSR6.SA_Mes_4", "BRSR6.SA_Mes_5", "BRSR6.SA_Mes_6", "BRSR6.SA_Mes_7", "BRSR6.SA_Mes_8", "BRSR6.SA_Mes_9", "BRSR6.SA_Mes_10", "BRSR6.SA_Mes_11", "BRSR6.SA_Mes_12", "BRSR6.SA_Quarter_1", "BRSR6.SA_Quarter_2", "BRSR6.SA_Quarter_3", "BRSR6.SA_Quarter_4", "BRSR6.SA_Last_Day_Quarter", "BRSR6.SA_Pre_Feriado_Brasil", "BRSR6.SA_Media_OHLC_Anterior", "BRSR6.SA_Sinal_Compra", "BRSR6.SA_Sinal_Venda", "BRSR6.SA_Media_OHLC_Futura", "BRSR6.SA_Open_PctChange_Lag_1", "BRSR6.SA_High_PctChange_Lag_1", "BRSR6.SA_Low_PctChange_Lag_1", "BRSR6.SA_Close_PctChange_Lag_1", "BRSR6.SA_Open_PctChange_Lag_2", "BRSR6.SA_High_PctChange_Lag_2", "BRSR6.SA_Low_PctChange_Lag_2", "BRSR6.SA_Close_PctChange_Lag_2", "BRSR6.SA_Open_PctChange_Lag_3", "BRSR6.SA_High_PctChange_Lag_3", "BRSR6.SA_Low_PctChange_Lag_3", "BRSR6.SA_Close_PctChange_Lag_3", "BRSR6.SA_Open_PctChange_Lag_4", "BRSR6.SA_High_PctChange_Lag_4", "BRSR6.SA_Low_PctChange_Lag_4", "BRSR6.SA_Close_PctChange_Lag_4", "BRSR6.SA_Open_PctChange_Lag_5", "BRSR6.SA_High_PctChange_Lag_5", "BRSR6.SA_Low_PctChange_Lag_5", "BRSR6.SA_Close_PctChange_Lag_5", "BRSR6.SA_Open_PctChange_Lag_6", "BRSR6.SA_High_PctChange_Lag_6", "BRSR6.SA_Low_PctChange_Lag_6", "BRSR6.SA_Close_PctChange_Lag_6", "BRSR6.SA_Open_PctChange_Lag_7", "BRSR6.SA_High_PctChange_Lag_7", "BRSR6.SA_Low_PctChange_Lag_7", "BRSR6.SA_Close_PctChange_Lag_7", "BRSR6.SA_Open_PctChange_Lag_8", "BRSR6.SA_High_PctChange_Lag_8", "BRSR6.SA_Low_PctChange_Lag_8", "BRSR6.SA_Close_PctChange_Lag_8", "BRSR6.SA_Open_PctChange_Lag_9", "BRSR6.SA_High_PctChange_Lag_9", "BRSR6.SA_Low_PctChange_Lag_9", "BRSR6.SA_Close_PctChange_Lag_9", "BRSR6.SA_Open_PctChange_Lag_10", "BRSR6.SA_High_PctChange_Lag_10", "BRSR6.SA_Low_PctChange_Lag_10", "BRSR6.SA_Close_PctChange_Lag_10", "BRSR6.SA_MM10_PctChange", "BRSR6.SA_Diff_OHLC_MM10", "BRSR6.SA_MM25_PctChange", "BRSR6.SA_Diff_OHLC_MM25", "BRSR6.SA_MM100_PctChange", "BRSR6.SA_Diff_OHLC_MM100", "BRSR6.SA_MM10_Menos_MM25", "BRSR6.SA_MM25_Menos_MM100", "BRSR6.SA_MM10_Menos_MM100", "BRSR6.SA_Volume_Lag_1", "BRSR6.SA_Volume_Lag_2", "BRSR6.SA_Volume_Lag_3", "BRSR6.SA_Volume_Lag_4", "BRSR6.SA_Volume_Lag_5", "BRSR6.SA_Spread_Lag_1", "BRSR6.SA_Spread_Lag_2", "BRSR6.SA_Spread_Lag_3", "BRSR6.SA_Spread_Lag_4", "BRSR6.SA_Spread_Lag_5", "BRSR6.SA_Volatilidade_Lag_1", "BRSR6.SA_Volatilidade_Lag_2", "BRSR6.SA_Volatilidade_Lag_3", "BRSR6.SA_Volatilidade_Lag_4", "BRSR6.SA_Volatilidade_Lag_5", "BRSR6.SA_Parkinson_Volatility_Lag_1", "BRSR6.SA_Parkinson_Volatility_Lag_2", "BRSR6.SA_Parkinson_Volatility_Lag_3", "BRSR6.SA_Parkinson_Volatility_Lag_4", "BRSR6.SA_Parkinson_Volatility_Lag_5", "BRSR6.SA_EMV_Lag_1", "BRSR6.SA_EMV_Lag_2", "BRSR6.SA_EMV_Lag_3", "BRSR6.SA_EMV_Lag_4", "BRSR6.SA_EMV_Lag_5", "BRSR6.SA_EMV_MA_Lag_1", "BRSR6.SA_EMV_MA_Lag_2", "BRSR6.SA_EMV_MA_Lag_3", "BRSR6.SA_EMV_MA_Lag_4", "BRSR6.SA_EMV_MA_Lag_5", "BRSR6.SA_VO_Lag_1", "BRSR6.SA_VO_Lag_2", "BRSR6.SA_VO_Lag_3", "BRSR6.SA_VO_Lag_4", "BRSR6.SA_VO_Lag_5", "BRSR6.SA_High_Max_50_Lag_1", "BRSR6.SA_High_Max_50_Lag_2", "BRSR6.SA_High_Max_50_Lag_3", "BRSR6.SA_High_Max_50_Lag_4", "BRSR6.SA_High_Max_50_Lag_5", "BRSR6.SA_Low_Min_50_Lag_1", "BRSR6.SA_Low_Min_50_Lag_2", "BRSR6.SA_Low_Min_50_Lag_3", "BRSR6.SA_Low_Min_50_Lag_4", "BRSR6.SA_Low_Min_50_Lag_5", "BRSR6.SA_MFI_Lag_1", "BRSR6.SA_MFI_Lag_2", "BRSR6.SA_MFI_Lag_3", "BRSR6.SA_MFI_Lag_4", "BRSR6.SA_MFI_Lag_5", "BRSR6.SA_Amihud_Lag_1", "BRSR6.SA_Amihud_Lag_2", "BRSR6.SA_Amihud_Lag_3", "BRSR6.SA_Amihud_Lag_4", "BRSR6.SA_Amihud_Lag_5", "BRSR6.SA_Roll_Spread_Lag_1", "BRSR6.SA_Roll_Spread_Lag_2", "BRSR6.SA_Roll_Spread_Lag_3", "BRSR6.SA_Roll_Spread_Lag_4", "BRSR6.SA_Roll_Spread_Lag_5", "BRSR6.SA_Hurst_Lag_1", "BRSR6.SA_Hurst_Lag_2", "BRSR6.SA_Hurst_Lag_3", "BRSR6.SA_Hurst_Lag_4", "BRSR6.SA_Hurst_Lag_5", "BRSR6.SA_Vol_per_Volume_Lag_1", "BRSR6.SA_Vol_per_Volume_Lag_2", "BRSR6.SA_Vol_per_Volume_Lag_3", "BRSR6.SA_Vol_per_Volume_Lag_4", "BRSR6.SA_Vol_per_Volume_Lag_5", "BRSR6.SA_CMF_Lag_1", "BRSR6.SA_CMF_Lag_2", "BRSR6.SA_CMF_Lag_3", "BRSR6.SA_CMF_Lag_4", "BRSR6.SA_CMF_Lag_5", "BRSR6.SA_AD_Line_Lag_1", "BRSR6.SA_AD_Line_Lag_2", "BRSR6.SA_AD_Line_Lag_3", "BRSR6.SA_AD_Line_Lag_4", "BRSR6.SA_AD_Line_Lag_5", "BBAS3.SA_Open", "BBAS3.SA_High", "BBAS3.SA_Low", "BBAS3.SA_Close", "BBAS3.SA_Volume", "BBAS3.SA_Media_OHLC", "BBAS3.SA_MM10", "BBAS3.SA_MM25", "BBAS3.SA_MM100", "BBAS3.SA_Media_OHLC_PctChange", "BBAS3.SA_Open_PctChange", "BBAS3.SA_High_PctChange", "BBAS3.SA_Low_PctChange", "BBAS3.SA_Close_PctChange", "BBAS3.SA_Close_PctChange_Historical", "BBAS3.SA_Volatilidade", "BBAS3.SA_Spread", "BBAS3.SA_Parkinson_Volatility", "BBAS3.SA_MFI", "BBAS3.SA_MFI_Historical", "BBAS3.SA_EMV", "BBAS3.SA_EMV_MA", "BBAS3.SA_Amihud", "BBAS3.SA_Amihud_Historical", "BBAS3.SA_Roll_Spread", "BBAS3.SA_Roll_Spread_Historical", "BBAS3.SA_Hurst", "BBAS3.SA_<PERSON><PERSON>_Historical", "BBAS3.SA_Vol_per_Volume", "BBAS3.SA_Vol_per_Volume_Historical", "BBAS3.SA_CMF", "BBAS3.SA_CMF_Historical", "BBAS3.SA_AD_Line", "BBAS3.SA_AD_Line_Historical", "BBAS3.SA_VO", "BBAS3.SA_High_Max_50", "BBAS3.SA_Low_Min_50", "BBAS3.SA_Segunda", "BBAS3.SA_Terca", "BBAS3.SA_Quarta", "BBAS3.SA_Quinta", "BBAS3.SA_Sexta", "BBAS3.SA_Mes_1", "BBAS3.SA_Mes_2", "BBAS3.SA_Mes_3", "BBAS3.SA_Mes_4", "BBAS3.SA_Mes_5", "BBAS3.SA_Mes_6", "BBAS3.SA_Mes_7", "BBAS3.SA_Mes_8", "BBAS3.SA_Mes_9", "BBAS3.SA_Mes_10", "BBAS3.SA_Mes_11", "BBAS3.SA_Mes_12", "BBAS3.SA_Quarter_1", "BBAS3.SA_Quarter_2", "BBAS3.SA_Quarter_3", "BBAS3.SA_Quarter_4", "BBAS3.SA_Last_Day_Quarter", "BBAS3.SA_Pre_Feriado_Brasil", "BBAS3.SA_Media_OHLC_Anterior", "BBAS3.SA_Sinal_Compra", "BBAS3.SA_Sinal_Venda", "BBAS3.SA_Media_OHLC_Futura", "BBAS3.SA_Open_PctChange_Lag_1", "BBAS3.SA_High_PctChange_Lag_1", "BBAS3.SA_Low_PctChange_Lag_1", "BBAS3.SA_Close_PctChange_Lag_1", "BBAS3.SA_Open_PctChange_Lag_2", "BBAS3.SA_High_PctChange_Lag_2", "BBAS3.SA_Low_PctChange_Lag_2", "BBAS3.SA_Close_PctChange_Lag_2", "BBAS3.SA_Open_PctChange_Lag_3", "BBAS3.SA_High_PctChange_Lag_3", "BBAS3.SA_Low_PctChange_Lag_3", "BBAS3.SA_Close_PctChange_Lag_3", "BBAS3.SA_Open_PctChange_Lag_4", "BBAS3.SA_High_PctChange_Lag_4", "BBAS3.SA_Low_PctChange_Lag_4", "BBAS3.SA_Close_PctChange_Lag_4", "BBAS3.SA_Open_PctChange_Lag_5", "BBAS3.SA_High_PctChange_Lag_5", "BBAS3.SA_Low_PctChange_Lag_5", "BBAS3.SA_Close_PctChange_Lag_5", "BBAS3.SA_Open_PctChange_Lag_6", "BBAS3.SA_High_PctChange_Lag_6", "BBAS3.SA_Low_PctChange_Lag_6", "BBAS3.SA_Close_PctChange_Lag_6", "BBAS3.SA_Open_PctChange_Lag_7", "BBAS3.SA_High_PctChange_Lag_7", "BBAS3.SA_Low_PctChange_Lag_7", "BBAS3.SA_Close_PctChange_Lag_7", "BBAS3.SA_Open_PctChange_Lag_8", "BBAS3.SA_High_PctChange_Lag_8", "BBAS3.SA_Low_PctChange_Lag_8", "BBAS3.SA_Close_PctChange_Lag_8", "BBAS3.SA_Open_PctChange_Lag_9", "BBAS3.SA_High_PctChange_Lag_9", "BBAS3.SA_Low_PctChange_Lag_9", "BBAS3.SA_Close_PctChange_Lag_9", "BBAS3.SA_Open_PctChange_Lag_10", "BBAS3.SA_High_PctChange_Lag_10", "BBAS3.SA_Low_PctChange_Lag_10", "BBAS3.SA_Close_PctChange_Lag_10", "BBAS3.SA_MM10_PctChange", "BBAS3.SA_Diff_OHLC_MM10", "BBAS3.SA_MM25_PctChange", "BBAS3.SA_Diff_OHLC_MM25", "BBAS3.SA_MM100_PctChange", "BBAS3.SA_Diff_OHLC_MM100", "BBAS3.SA_MM10_Menos_MM25", "BBAS3.SA_MM25_Menos_MM100", "BBAS3.SA_MM10_Menos_MM100", "BBAS3.SA_Volume_Lag_1", "BBAS3.SA_Volume_Lag_2", "BBAS3.SA_Volume_Lag_3", "BBAS3.SA_Volume_Lag_4", "BBAS3.SA_Volume_Lag_5", "BBAS3.SA_Spread_Lag_1", "BBAS3.SA_Spread_Lag_2", "BBAS3.SA_Spread_Lag_3", "BBAS3.SA_Spread_Lag_4", "BBAS3.SA_Spread_Lag_5", "BBAS3.SA_Volatilidade_Lag_1", "BBAS3.SA_Volatilidade_Lag_2", "BBAS3.SA_Volatilidade_Lag_3", "BBAS3.SA_Volatilidade_Lag_4", "BBAS3.SA_Volatilidade_Lag_5", "BBAS3.SA_Parkinson_Volatility_Lag_1", "BBAS3.SA_Parkinson_Volatility_Lag_2", "BBAS3.SA_Parkinson_Volatility_Lag_3", "BBAS3.SA_Parkinson_Volatility_Lag_4", "BBAS3.SA_Parkinson_Volatility_Lag_5", "BBAS3.SA_EMV_Lag_1", "BBAS3.SA_EMV_Lag_2", "BBAS3.SA_EMV_Lag_3", "BBAS3.SA_EMV_Lag_4", "BBAS3.SA_EMV_Lag_5", "BBAS3.SA_EMV_MA_Lag_1", "BBAS3.SA_EMV_MA_Lag_2", "BBAS3.SA_EMV_MA_Lag_3", "BBAS3.SA_EMV_MA_Lag_4", "BBAS3.SA_EMV_MA_Lag_5", "BBAS3.SA_VO_Lag_1", "BBAS3.SA_VO_Lag_2", "BBAS3.SA_VO_Lag_3", "BBAS3.SA_VO_Lag_4", "BBAS3.SA_VO_Lag_5", "BBAS3.SA_High_Max_50_Lag_1", "BBAS3.SA_High_Max_50_Lag_2", "BBAS3.SA_High_Max_50_Lag_3", "BBAS3.SA_High_Max_50_Lag_4", "BBAS3.SA_High_Max_50_Lag_5", "BBAS3.SA_Low_Min_50_Lag_1", "BBAS3.SA_Low_Min_50_Lag_2", "BBAS3.SA_Low_Min_50_Lag_3", "BBAS3.SA_Low_Min_50_Lag_4", "BBAS3.SA_Low_Min_50_Lag_5", "BBAS3.SA_MFI_Lag_1", "BBAS3.SA_MFI_Lag_2", "BBAS3.SA_MFI_Lag_3", "BBAS3.SA_MFI_Lag_4", "BBAS3.SA_MFI_Lag_5", "BBAS3.SA_Amihud_Lag_1", "BBAS3.SA_Amihud_Lag_2", "BBAS3.SA_Amihud_Lag_3", "BBAS3.SA_Amihud_Lag_4", "BBAS3.SA_Amihud_Lag_5", "BBAS3.SA_Roll_Spread_Lag_1", "BBAS3.SA_Roll_Spread_Lag_2", "BBAS3.SA_Roll_Spread_Lag_3", "BBAS3.SA_Roll_Spread_Lag_4", "BBAS3.SA_Roll_Spread_Lag_5", "BBAS3.SA_Hurst_Lag_1", "BBAS3.SA_<PERSON><PERSON>_Lag_2", "BBAS3.SA_<PERSON><PERSON>_Lag_3", "BBAS3.<PERSON>_<PERSON><PERSON>_Lag_4", "BBAS3.<PERSON>_<PERSON><PERSON>_Lag_5", "BBAS3.SA_Vol_per_Volume_Lag_1", "BBAS3.SA_Vol_per_Volume_Lag_2", "BBAS3.SA_Vol_per_Volume_Lag_3", "BBAS3.SA_Vol_per_Volume_Lag_4", "BBAS3.SA_Vol_per_Volume_Lag_5", "BBAS3.SA_CMF_Lag_1", "BBAS3.SA_CMF_Lag_2", "BBAS3.SA_CMF_Lag_3", "BBAS3.SA_CMF_Lag_4", "BBAS3.SA_CMF_Lag_5", "BBAS3.SA_AD_Line_Lag_1", "BBAS3.SA_AD_Line_Lag_2", "BBAS3.SA_AD_Line_Lag_3", "BBAS3.SA_AD_Line_Lag_4", "BBAS3.SA_AD_Line_Lag_5", "POMO3.SA_Open", "POMO3.SA_High", "POMO3.SA_Low", "POMO3.SA_Close", "POMO3.SA_Volume", "POMO3.SA_Media_OHLC", "POMO3.SA_MM10", "POMO3.SA_MM25", "POMO3.SA_MM100", "POMO3.SA_Media_OHLC_PctChange", "POMO3.SA_Open_PctChange", "POMO3.SA_High_PctChange", "POMO3.SA_Low_PctChange", "POMO3.SA_Close_PctChange", "POMO3.SA_Close_PctChange_Historical", "POMO3.SA_Volatilidade", "POMO3.SA_Spread", "POMO3.SA_Parkinson_Volatility", "POMO3.SA_MFI", "POMO3.SA_MFI_Historical", "POMO3.SA_EMV", "POMO3.SA_EMV_MA", "POMO3.SA_Amihud", "POMO3.SA_Amihud_Historical", "POMO3.SA_Roll_Spread", "POMO3.SA_Roll_Spread_Historical", "POMO3.SA_Hurst", "POMO3.<PERSON>_<PERSON><PERSON>_Historical", "POMO3.SA_Vol_per_Volume", "POMO3.SA_Vol_per_Volume_Historical", "POMO3.SA_CMF", "POMO3.SA_CMF_Historical", "POMO3.SA_AD_Line", "POMO3.SA_AD_Line_Historical", "POMO3.SA_VO", "POMO3.SA_High_Max_50", "POMO3.SA_Low_Min_50", "POMO3.SA_Segunda", "POMO3.SA_Terca", "POMO3.SA_Quarta", "POMO3.SA_Quinta", "POMO3.SA_Sexta", "POMO3.SA_Mes_1", "POMO3.SA_Mes_2", "POMO3.SA_Mes_3", "POMO3.SA_Mes_4", "POMO3.SA_Mes_5", "POMO3.SA_Mes_6", "POMO3.SA_Mes_7", "POMO3.SA_Mes_8", "POMO3.SA_Mes_9", "POMO3.SA_Mes_10", "POMO3.SA_Mes_11", "POMO3.SA_Mes_12", "POMO3.SA_Quarter_1", "POMO3.SA_Quarter_2", "POMO3.SA_Quarter_3", "POMO3.SA_Quarter_4", "POMO3.SA_Last_Day_Quarter", "POMO3.SA_Pre_Feriado_Brasil", "POMO3.SA_Media_OHLC_Anterior", "POMO3.SA_Sinal_Compra", "POMO3.SA_Sinal_Venda", "POMO3.SA_Media_OHLC_Futura", "POMO3.SA_Open_PctChange_Lag_1", "POMO3.SA_High_PctChange_Lag_1", "POMO3.SA_Low_PctChange_Lag_1", "POMO3.SA_Close_PctChange_Lag_1", "POMO3.SA_Open_PctChange_Lag_2", "POMO3.SA_High_PctChange_Lag_2", "POMO3.SA_Low_PctChange_Lag_2", "POMO3.SA_Close_PctChange_Lag_2", "POMO3.SA_Open_PctChange_Lag_3", "POMO3.SA_High_PctChange_Lag_3", "POMO3.SA_Low_PctChange_Lag_3", "POMO3.SA_Close_PctChange_Lag_3", "POMO3.SA_Open_PctChange_Lag_4", "POMO3.SA_High_PctChange_Lag_4", "POMO3.SA_Low_PctChange_Lag_4", "POMO3.SA_Close_PctChange_Lag_4", "POMO3.SA_Open_PctChange_Lag_5", "POMO3.SA_High_PctChange_Lag_5", "POMO3.SA_Low_PctChange_Lag_5", "POMO3.SA_Close_PctChange_Lag_5", "POMO3.SA_Open_PctChange_Lag_6", "POMO3.SA_High_PctChange_Lag_6", "POMO3.SA_Low_PctChange_Lag_6", "POMO3.SA_Close_PctChange_Lag_6", "POMO3.SA_Open_PctChange_Lag_7", "POMO3.SA_High_PctChange_Lag_7", "POMO3.SA_Low_PctChange_Lag_7", "POMO3.SA_Close_PctChange_Lag_7", "POMO3.SA_Open_PctChange_Lag_8", "POMO3.SA_High_PctChange_Lag_8", "POMO3.SA_Low_PctChange_Lag_8", "POMO3.SA_Close_PctChange_Lag_8", "POMO3.SA_Open_PctChange_Lag_9", "POMO3.SA_High_PctChange_Lag_9", "POMO3.SA_Low_PctChange_Lag_9", "POMO3.SA_Close_PctChange_Lag_9", "POMO3.SA_Open_PctChange_Lag_10", "POMO3.SA_High_PctChange_Lag_10", "POMO3.SA_Low_PctChange_Lag_10", "POMO3.SA_Close_PctChange_Lag_10", "POMO3.SA_MM10_PctChange", "POMO3.SA_Diff_OHLC_MM10", "POMO3.SA_MM25_PctChange", "POMO3.SA_Diff_OHLC_MM25", "POMO3.SA_MM100_PctChange", "POMO3.SA_Diff_OHLC_MM100", "POMO3.SA_MM10_Menos_MM25", "POMO3.SA_MM25_Menos_MM100", "POMO3.SA_MM10_Menos_MM100", "POMO3.SA_Volume_Lag_1", "POMO3.SA_Volume_Lag_2", "POMO3.SA_Volume_Lag_3", "POMO3.SA_Volume_Lag_4", "POMO3.SA_Volume_Lag_5", "POMO3.SA_Spread_Lag_1", "POMO3.SA_Spread_Lag_2", "POMO3.SA_Spread_Lag_3", "POMO3.SA_Spread_Lag_4", "POMO3.SA_Spread_Lag_5", "POMO3.SA_Volatilidade_Lag_1", "POMO3.SA_Volatilidade_Lag_2", "POMO3.SA_Volatilidade_Lag_3", "POMO3.SA_Volatilidade_Lag_4", "POMO3.SA_Volatilidade_Lag_5", "POMO3.SA_Parkinson_Volatility_Lag_1", "POMO3.<PERSON>_Parkinson_Volatility_Lag_2", "POMO3.SA_Parkinson_Volatility_Lag_3", "POMO3.SA_Parkinson_Volatility_Lag_4", "POMO3.<PERSON>_Parkinson_Volatility_Lag_5", "POMO3.SA_EMV_Lag_1", "POMO3.SA_EMV_Lag_2", "POMO3.SA_EMV_Lag_3", "POMO3.SA_EMV_Lag_4", "POMO3.SA_EMV_Lag_5", "POMO3.SA_EMV_MA_Lag_1", "POMO3.SA_EMV_MA_Lag_2", "POMO3.SA_EMV_MA_Lag_3", "POMO3.SA_EMV_MA_Lag_4", "POMO3.SA_EMV_MA_Lag_5", "POMO3.SA_VO_Lag_1", "POMO3.SA_VO_Lag_2", "POMO3.SA_VO_Lag_3", "POMO3.SA_VO_Lag_4", "POMO3.SA_VO_Lag_5", "POMO3.SA_High_Max_50_Lag_1", "POMO3.SA_High_Max_50_Lag_2", "POMO3.SA_High_Max_50_Lag_3", "POMO3.SA_High_Max_50_Lag_4", "POMO3.SA_High_Max_50_Lag_5", "POMO3.SA_Low_Min_50_Lag_1", "POMO3.SA_Low_Min_50_Lag_2", "POMO3.SA_Low_Min_50_Lag_3", "POMO3.SA_Low_Min_50_Lag_4", "POMO3.SA_Low_Min_50_Lag_5", "POMO3.SA_MFI_Lag_1", "POMO3.SA_MFI_Lag_2", "POMO3.SA_MFI_Lag_3", "POMO3.SA_MFI_Lag_4", "POMO3.SA_MFI_Lag_5", "POMO3.SA_Amihud_Lag_1", "POMO3.SA_Amihud_Lag_2", "POMO3.SA_Amihud_Lag_3", "POMO3.SA_Amihud_Lag_4", "POMO3.SA_Amihud_Lag_5", "POMO3.SA_Roll_Spread_Lag_1", "POMO3.SA_Roll_Spread_Lag_2", "POMO3.SA_Roll_Spread_Lag_3", "POMO3.SA_Roll_Spread_Lag_4", "POMO3.SA_Roll_Spread_Lag_5", "POMO3.<PERSON>_Hurst_Lag_1", "POMO3.<PERSON>_<PERSON><PERSON>_Lag_2", "POMO3.<PERSON>_<PERSON><PERSON>_Lag_3", "POMO3.<PERSON>_<PERSON><PERSON>_Lag_4", "POMO3.<PERSON>_<PERSON><PERSON>_Lag_5", "POMO3.SA_Vol_per_Volume_Lag_1", "POMO3.SA_Vol_per_Volume_Lag_2", "POMO3.SA_Vol_per_Volume_Lag_3", "POMO3.SA_Vol_per_Volume_Lag_4", "POMO3.SA_Vol_per_Volume_Lag_5", "POMO3.SA_CMF_Lag_1", "POMO3.SA_CMF_Lag_2", "POMO3.SA_CMF_Lag_3", "POMO3.SA_CMF_Lag_4", "POMO3.SA_CMF_Lag_5", "POMO3.SA_AD_Line_Lag_1", "POMO3.SA_AD_Line_Lag_2", "POMO3.SA_AD_Line_Lag_3", "POMO3.SA_AD_Line_Lag_4", "POMO3.SA_AD_Line_Lag_5", "VIVT3.SA_Open", "VIVT3.SA_High", "VIVT3.SA_Low", "VIVT3.SA_Close", "VIVT3.SA_Volume", "VIVT3.SA_Media_OHLC", "VIVT3.SA_MM10", "VIVT3.SA_MM25", "VIVT3.SA_MM100", "VIVT3.SA_Media_OHLC_PctChange", "VIVT3.SA_Open_PctChange", "VIVT3.SA_High_PctChange", "VIVT3.SA_Low_PctChange", "VIVT3.SA_Close_PctChange", "VIVT3.SA_Close_PctChange_Historical", "VIVT3.SA_Volatilidade", "VIVT3.SA_Spread", "VIVT3.SA_Parkinson_Volatility", "VIVT3.SA_MFI", "VIVT3.SA_MFI_Historical", "VIVT3.SA_EMV", "VIVT3.SA_EMV_MA", "VIVT3.SA_Amihud", "VIVT3.SA_Amihud_Historical", "VIVT3.SA_Roll_Spread", "VIVT3.SA_Roll_Spread_Historical", "VIVT3.SA_Hurst", "VIVT3.<PERSON>_<PERSON><PERSON>_Historical", "VIVT3.SA_Vol_per_Volume", "VIVT3.SA_Vol_per_Volume_Historical", "VIVT3.SA_CMF", "VIVT3.SA_CMF_Historical", "VIVT3.SA_AD_Line", "VIVT3.SA_AD_Line_Historical", "VIVT3.SA_VO", "VIVT3.SA_High_Max_50", "VIVT3.SA_Low_Min_50", "VIVT3.SA_Segunda", "VIVT3.SA_Terca", "VIVT3.SA_Quarta", "VIVT3.SA_Quinta", "VIVT3.SA_Sexta", "VIVT3.SA_Mes_1", "VIVT3.SA_Mes_2", "VIVT3.SA_Mes_3", "VIVT3.SA_Mes_4", "VIVT3.SA_Mes_5", "VIVT3.SA_Mes_6", "VIVT3.SA_Mes_7", "VIVT3.SA_Mes_8", "VIVT3.SA_Mes_9", "VIVT3.SA_Mes_10", "VIVT3.SA_Mes_11", "VIVT3.SA_Mes_12", "VIVT3.SA_Quarter_1", "VIVT3.SA_Quarter_2", "VIVT3.SA_Quarter_3", "VIVT3.SA_Quarter_4", "VIVT3.SA_Last_Day_Quarter", "VIVT3.SA_Pre_Feriado_Brasil", "VIVT3.SA_Media_OHLC_Anterior", "VIVT3.SA_Sinal_Compra", "VIVT3.SA_Sinal_Venda", "VIVT3.SA_Media_OHLC_Futura", "VIVT3.SA_Open_PctChange_Lag_1", "VIVT3.SA_High_PctChange_Lag_1", "VIVT3.SA_Low_PctChange_Lag_1", "VIVT3.SA_Close_PctChange_Lag_1", "VIVT3.SA_Open_PctChange_Lag_2", "VIVT3.SA_High_PctChange_Lag_2", "VIVT3.SA_Low_PctChange_Lag_2", "VIVT3.SA_Close_PctChange_Lag_2", "VIVT3.SA_Open_PctChange_Lag_3", "VIVT3.SA_High_PctChange_Lag_3", "VIVT3.SA_Low_PctChange_Lag_3", "VIVT3.SA_Close_PctChange_Lag_3", "VIVT3.SA_Open_PctChange_Lag_4", "VIVT3.SA_High_PctChange_Lag_4", "VIVT3.SA_Low_PctChange_Lag_4", "VIVT3.SA_Close_PctChange_Lag_4", "VIVT3.SA_Open_PctChange_Lag_5", "VIVT3.SA_High_PctChange_Lag_5", "VIVT3.SA_Low_PctChange_Lag_5", "VIVT3.SA_Close_PctChange_Lag_5", "VIVT3.SA_Open_PctChange_Lag_6", "VIVT3.SA_High_PctChange_Lag_6", "VIVT3.SA_Low_PctChange_Lag_6", "VIVT3.SA_Close_PctChange_Lag_6", "VIVT3.SA_Open_PctChange_Lag_7", "VIVT3.SA_High_PctChange_Lag_7", "VIVT3.SA_Low_PctChange_Lag_7", "VIVT3.SA_Close_PctChange_Lag_7", "VIVT3.SA_Open_PctChange_Lag_8", "VIVT3.SA_High_PctChange_Lag_8", "VIVT3.SA_Low_PctChange_Lag_8", "VIVT3.SA_Close_PctChange_Lag_8", "VIVT3.SA_Open_PctChange_Lag_9", "VIVT3.SA_High_PctChange_Lag_9", "VIVT3.SA_Low_PctChange_Lag_9", "VIVT3.SA_Close_PctChange_Lag_9", "VIVT3.SA_Open_PctChange_Lag_10", "VIVT3.SA_High_PctChange_Lag_10", "VIVT3.SA_Low_PctChange_Lag_10", "VIVT3.SA_Close_PctChange_Lag_10", "VIVT3.SA_MM10_PctChange", "VIVT3.SA_Diff_OHLC_MM10", "VIVT3.SA_MM25_PctChange", "VIVT3.SA_Diff_OHLC_MM25", "VIVT3.SA_MM100_PctChange", "VIVT3.SA_Diff_OHLC_MM100", "VIVT3.SA_MM10_Menos_MM25", "VIVT3.SA_MM25_Menos_MM100", "VIVT3.SA_MM10_Menos_MM100", "VIVT3.SA_Volume_Lag_1", "VIVT3.SA_Volume_Lag_2", "VIVT3.SA_Volume_Lag_3", "VIVT3.SA_Volume_Lag_4", "VIVT3.SA_Volume_Lag_5", "VIVT3.SA_Spread_Lag_1", "VIVT3.SA_Spread_Lag_2", "VIVT3.SA_Spread_Lag_3", "VIVT3.SA_Spread_Lag_4", "VIVT3.SA_Spread_Lag_5", "VIVT3.SA_Volatilidade_Lag_1", "VIVT3.SA_Volatilidade_Lag_2", "VIVT3.SA_Volatilidade_Lag_3", "VIVT3.SA_Volatilidade_Lag_4", "VIVT3.SA_Volatilidade_Lag_5", "VIVT3.SA_Parkinson_Volatility_Lag_1", "VIVT3.SA_Parkinson_Volatility_Lag_2", "VIVT3.SA_Parkinson_Volatility_Lag_3", "VIVT3.SA_Parkinson_Volatility_Lag_4", "VIVT3.<PERSON>_Parkinson_Volatility_Lag_5", "VIVT3.SA_EMV_Lag_1", "VIVT3.SA_EMV_Lag_2", "VIVT3.SA_EMV_Lag_3", "VIVT3.SA_EMV_Lag_4", "VIVT3.SA_EMV_Lag_5", "VIVT3.SA_EMV_MA_Lag_1", "VIVT3.SA_EMV_MA_Lag_2", "VIVT3.SA_EMV_MA_Lag_3", "VIVT3.SA_EMV_MA_Lag_4", "VIVT3.SA_EMV_MA_Lag_5", "VIVT3.SA_VO_Lag_1", "VIVT3.SA_VO_Lag_2", "VIVT3.SA_VO_Lag_3", "VIVT3.SA_VO_Lag_4", "VIVT3.SA_VO_Lag_5", "VIVT3.SA_High_Max_50_Lag_1", "VIVT3.SA_High_Max_50_Lag_2", "VIVT3.SA_High_Max_50_Lag_3", "VIVT3.SA_High_Max_50_Lag_4", "VIVT3.SA_High_Max_50_Lag_5", "VIVT3.SA_Low_Min_50_Lag_1", "VIVT3.SA_Low_Min_50_Lag_2", "VIVT3.SA_Low_Min_50_Lag_3", "VIVT3.SA_Low_Min_50_Lag_4", "VIVT3.SA_Low_Min_50_Lag_5", "VIVT3.SA_MFI_Lag_1", "VIVT3.SA_MFI_Lag_2", "VIVT3.SA_MFI_Lag_3", "VIVT3.SA_MFI_Lag_4", "VIVT3.SA_MFI_Lag_5", "VIVT3.SA_Amihud_Lag_1", "VIVT3.SA_Amihud_Lag_2", "VIVT3.SA_Amihud_Lag_3", "VIVT3.SA_Amihud_Lag_4", "VIVT3.SA_Amihud_Lag_5", "VIVT3.SA_Roll_Spread_Lag_1", "VIVT3.SA_Roll_Spread_Lag_2", "VIVT3.SA_Roll_Spread_Lag_3", "VIVT3.SA_Roll_Spread_Lag_4", "VIVT3.SA_Roll_Spread_Lag_5", "VIVT3.<PERSON>_Hu<PERSON>_Lag_1", "VIVT3.<PERSON>_<PERSON><PERSON>_Lag_2", "VIVT3.<PERSON>_<PERSON><PERSON>_Lag_3", "VIVT3.<PERSON>_<PERSON><PERSON>_Lag_4", "VIVT3.<PERSON>_<PERSON><PERSON>_Lag_5", "VIVT3.SA_Vol_per_Volume_Lag_1", "VIVT3.SA_Vol_per_Volume_Lag_2", "VIVT3.SA_Vol_per_Volume_Lag_3", "VIVT3.SA_Vol_per_Volume_Lag_4", "VIVT3.SA_Vol_per_Volume_Lag_5", "VIVT3.SA_CMF_Lag_1", "VIVT3.SA_CMF_Lag_2", "VIVT3.SA_CMF_Lag_3", "VIVT3.SA_CMF_Lag_4", "VIVT3.SA_CMF_Lag_5", "VIVT3.SA_AD_Line_Lag_1", "VIVT3.SA_AD_Line_Lag_2", "VIVT3.SA_AD_Line_Lag_3", "VIVT3.SA_AD_Line_Lag_4", "VIVT3.SA_AD_Line_Lag_5", "RANI3.SA_Open", "RANI3.SA_High", "RANI3.SA_Low", "RANI3.SA_Close", "RANI3.SA_Volume", "RANI3.SA_Media_OHLC", "RANI3.SA_MM10", "RANI3.SA_MM25", "RANI3.SA_MM100", "RANI3.SA_Media_OHLC_PctChange", "RANI3.SA_Open_PctChange", "RANI3.SA_High_PctChange", "RANI3.SA_Low_PctChange", "RANI3.SA_Close_PctChange", "RANI3.SA_Close_PctChange_Historical", "RANI3.SA_Volatilidade", "RANI3.SA_Spread", "RANI3.SA_Parkinson_Volatility", "RANI3.SA_MFI", "RANI3.SA_MFI_Historical", "RANI3.SA_EMV", "RANI3.SA_EMV_MA", "RANI3.SA_Amihud", "RANI3.SA_Amihud_Historical", "RANI3.SA_Roll_Spread", "RANI3.SA_Roll_Spread_Historical", "RANI3.SA_Hurst", "RANI3.SA_<PERSON><PERSON>_Historical", "RANI3.SA_Vol_per_Volume", "RANI3.SA_Vol_per_Volume_Historical", "RANI3.SA_CMF", "RANI3.SA_CMF_Historical", "RANI3.SA_AD_Line", "RANI3.SA_AD_Line_Historical", "RANI3.SA_VO", "RANI3.SA_High_Max_50", "RANI3.SA_Low_Min_50", "RANI3.SA_Segunda", "RANI3.SA_Terca", "RANI3.SA_Quarta", "RANI3.SA_Quinta", "RANI3.SA_Sexta", "RANI3.SA_Mes_1", "RANI3.SA_Mes_2", "RANI3.SA_Mes_3", "RANI3.SA_Mes_4", "RANI3.SA_Mes_5", "RANI3.SA_Mes_6", "RANI3.SA_Mes_7", "RANI3.SA_Mes_8", "RANI3.SA_Mes_9", "RANI3.SA_Mes_10", "RANI3.SA_Mes_11", "RANI3.SA_Mes_12", "RANI3.SA_Quarter_1", "RANI3.SA_Quarter_2", "RANI3.SA_Quarter_3", "RANI3.SA_Quarter_4", "RANI3.SA_Last_Day_Quarter", "RANI3.SA_Pre_Feriado_Brasil", "RANI3.SA_Media_OHLC_Anterior", "RANI3.SA_Sinal_Compra", "RANI3.SA_Sinal_Venda", "RANI3.SA_Media_OHLC_Futura", "RANI3.SA_Open_PctChange_Lag_1", "RANI3.SA_High_PctChange_Lag_1", "RANI3.SA_Low_PctChange_Lag_1", "RANI3.SA_Close_PctChange_Lag_1", "RANI3.SA_Open_PctChange_Lag_2", "RANI3.SA_High_PctChange_Lag_2", "RANI3.SA_Low_PctChange_Lag_2", "RANI3.SA_Close_PctChange_Lag_2", "RANI3.SA_Open_PctChange_Lag_3", "RANI3.SA_High_PctChange_Lag_3", "RANI3.SA_Low_PctChange_Lag_3", "RANI3.SA_Close_PctChange_Lag_3", "RANI3.SA_Open_PctChange_Lag_4", "RANI3.SA_High_PctChange_Lag_4", "RANI3.SA_Low_PctChange_Lag_4", "RANI3.SA_Close_PctChange_Lag_4", "RANI3.SA_Open_PctChange_Lag_5", "RANI3.SA_High_PctChange_Lag_5", "RANI3.SA_Low_PctChange_Lag_5", "RANI3.SA_Close_PctChange_Lag_5", "RANI3.SA_Open_PctChange_Lag_6", "RANI3.SA_High_PctChange_Lag_6", "RANI3.SA_Low_PctChange_Lag_6", "RANI3.SA_Close_PctChange_Lag_6", "RANI3.SA_Open_PctChange_Lag_7", "RANI3.SA_High_PctChange_Lag_7", "RANI3.SA_Low_PctChange_Lag_7", "RANI3.SA_Close_PctChange_Lag_7", "RANI3.SA_Open_PctChange_Lag_8", "RANI3.SA_High_PctChange_Lag_8", "RANI3.SA_Low_PctChange_Lag_8", "RANI3.SA_Close_PctChange_Lag_8", "RANI3.SA_Open_PctChange_Lag_9", "RANI3.SA_High_PctChange_Lag_9", "RANI3.SA_Low_PctChange_Lag_9", "RANI3.SA_Close_PctChange_Lag_9", "RANI3.SA_Open_PctChange_Lag_10", "RANI3.SA_High_PctChange_Lag_10", "RANI3.SA_Low_PctChange_Lag_10", "RANI3.SA_Close_PctChange_Lag_10", "RANI3.SA_MM10_PctChange", "RANI3.SA_Diff_OHLC_MM10", "RANI3.SA_MM25_PctChange", "RANI3.SA_Diff_OHLC_MM25", "RANI3.SA_MM100_PctChange", "RANI3.SA_Diff_OHLC_MM100", "RANI3.SA_MM10_Menos_MM25", "RANI3.SA_MM25_Menos_MM100", "RANI3.SA_MM10_Menos_MM100", "RANI3.SA_Volume_Lag_1", "RANI3.SA_Volume_Lag_2", "RANI3.SA_Volume_Lag_3", "RANI3.SA_Volume_Lag_4", "RANI3.SA_Volume_Lag_5", "RANI3.SA_Spread_Lag_1", "RANI3.SA_Spread_Lag_2", "RANI3.SA_Spread_Lag_3", "RANI3.SA_Spread_Lag_4", "RANI3.SA_Spread_Lag_5", "RANI3.SA_Volatilidade_Lag_1", "RANI3.SA_Volatilidade_Lag_2", "RANI3.SA_Volatilidade_Lag_3", "RANI3.SA_Volatilidade_Lag_4", "RANI3.SA_Volatilidade_Lag_5", "RANI3.SA_Parkinson_Volatility_Lag_1", "RANI3.SA_Parkinson_Volatility_Lag_2", "RANI3.SA_Parkinson_Volatility_Lag_3", "RANI3.SA_Parkinson_Volatility_Lag_4", "RANI3.SA_Parkinson_Volatility_Lag_5", "RANI3.SA_EMV_Lag_1", "RANI3.SA_EMV_Lag_2", "RANI3.SA_EMV_Lag_3", "RANI3.SA_EMV_Lag_4", "RANI3.SA_EMV_Lag_5", "RANI3.SA_EMV_MA_Lag_1", "RANI3.SA_EMV_MA_Lag_2", "RANI3.SA_EMV_MA_Lag_3", "RANI3.SA_EMV_MA_Lag_4", "RANI3.SA_EMV_MA_Lag_5", "RANI3.SA_VO_Lag_1", "RANI3.SA_VO_Lag_2", "RANI3.SA_VO_Lag_3", "RANI3.SA_VO_Lag_4", "RANI3.SA_VO_Lag_5", "RANI3.SA_High_Max_50_Lag_1", "RANI3.SA_High_Max_50_Lag_2", "RANI3.SA_High_Max_50_Lag_3", "RANI3.SA_High_Max_50_Lag_4", "RANI3.SA_High_Max_50_Lag_5", "RANI3.SA_Low_Min_50_Lag_1", "RANI3.SA_Low_Min_50_Lag_2", "RANI3.SA_Low_Min_50_Lag_3", "RANI3.SA_Low_Min_50_Lag_4", "RANI3.SA_Low_Min_50_Lag_5", "RANI3.SA_MFI_Lag_1", "RANI3.SA_MFI_Lag_2", "RANI3.SA_MFI_Lag_3", "RANI3.SA_MFI_Lag_4", "RANI3.SA_MFI_Lag_5", "RANI3.SA_Amihud_Lag_1", "RANI3.SA_Amihud_Lag_2", "RANI3.SA_Amihud_Lag_3", "RANI3.SA_Amihud_Lag_4", "RANI3.SA_Amihud_Lag_5", "RANI3.SA_Roll_Spread_Lag_1", "RANI3.SA_Roll_Spread_Lag_2", "RANI3.SA_Roll_Spread_Lag_3", "RANI3.SA_Roll_Spread_Lag_4", "RANI3.SA_Roll_Spread_Lag_5", "RANI3.SA_Hurst_Lag_1", "RANI3.SA_<PERSON><PERSON>_Lag_2", "RANI3.SA_<PERSON>rst_Lag_3", "RANI3.<PERSON>_<PERSON><PERSON>_Lag_4", "RANI3.<PERSON>_<PERSON><PERSON>_Lag_5", "RANI3.SA_Vol_per_Volume_Lag_1", "RANI3.SA_Vol_per_Volume_Lag_2", "RANI3.SA_Vol_per_Volume_Lag_3", "RANI3.SA_Vol_per_Volume_Lag_4", "RANI3.SA_Vol_per_Volume_Lag_5", "RANI3.SA_CMF_Lag_1", "RANI3.SA_CMF_Lag_2", "RANI3.SA_CMF_Lag_3", "RANI3.SA_CMF_Lag_4", "RANI3.SA_CMF_Lag_5", "RANI3.SA_AD_Line_Lag_1", "RANI3.SA_AD_Line_Lag_2", "RANI3.SA_AD_Line_Lag_3", "RANI3.SA_AD_Line_Lag_4", "RANI3.SA_AD_Line_Lag_5", "TAEE4.SA_Close", "TAEE4.SA_High", "TAEE4.SA_Low", "TAEE4.SA_Open", "TAEE4.SA_Volume", "TAEE4.SA_Media_OHLC", "TAEE4.SA_MM10", "TAEE4.SA_MM25", "TAEE4.SA_MM100", "TAEE4.SA_Media_OHLC_PctChange", "TAEE4.SA_Open_PctChange", "TAEE4.SA_High_PctChange", "TAEE4.SA_Low_PctChange", "TAEE4.SA_Close_PctChange", "TAEE4.SA_Close_PctChange_Historical", "TAEE4.SA_Volatilidade", "TAEE4.SA_Spread", "TAEE4.SA_Parkinson_Volatility", "TAEE4.SA_MFI", "TAEE4.SA_MFI_Historical", "TAEE4.SA_EMV", "TAEE4.SA_EMV_MA", "TAEE4.SA_Amihud", "TAEE4.SA_Amihud_Historical", "TAEE4.SA_Roll_Spread", "TAEE4.SA_Roll_Spread_Historical", "TAEE4.SA_Hurst", "TAEE4.<PERSON>_<PERSON><PERSON>_Historical", "TAEE4.SA_Vol_per_Volume", "TAEE4.SA_Vol_per_Volume_Historical", "TAEE4.SA_CMF", "TAEE4.SA_CMF_Historical", "TAEE4.SA_AD_Line", "TAEE4.SA_AD_Line_Historical", "TAEE4.SA_VO", "TAEE4.SA_High_Max_50", "TAEE4.SA_Low_Min_50", "TAEE4.SA_Segunda", "TAEE4.SA_Terca", "TAEE4.SA_Quarta", "TAEE4.SA_Quinta", "TAEE4.SA_Sexta", "TAEE4.SA_Mes_1", "TAEE4.SA_Mes_2", "TAEE4.SA_Mes_3", "TAEE4.SA_Mes_4", "TAEE4.SA_Mes_5", "TAEE4.SA_Mes_6", "TAEE4.SA_Mes_7", "TAEE4.SA_Mes_8", "TAEE4.SA_Mes_9", "TAEE4.SA_Mes_10", "TAEE4.SA_Mes_11", "TAEE4.SA_Mes_12", "TAEE4.SA_Quarter_1", "TAEE4.SA_Quarter_2", "TAEE4.SA_Quarter_3", "TAEE4.SA_Quarter_4", "TAEE4.SA_Last_Day_Quarter", "TAEE4.SA_Pre_Feriado_Brasil", "TAEE4.SA_Media_OHLC_Anterior", "TAEE4.SA_Sinal_Compra", "TAEE4.SA_Sinal_Venda", "TAEE4.SA_Media_OHLC_Futura", "TAEE4.SA_Open_PctChange_Lag_1", "TAEE4.SA_High_PctChange_Lag_1", "TAEE4.SA_Low_PctChange_Lag_1", "TAEE4.SA_Close_PctChange_Lag_1", "TAEE4.SA_Open_PctChange_Lag_2", "TAEE4.SA_High_PctChange_Lag_2", "TAEE4.SA_Low_PctChange_Lag_2", "TAEE4.SA_Close_PctChange_Lag_2", "TAEE4.SA_Open_PctChange_Lag_3", "TAEE4.SA_High_PctChange_Lag_3", "TAEE4.SA_Low_PctChange_Lag_3", "TAEE4.SA_Close_PctChange_Lag_3", "TAEE4.SA_Open_PctChange_Lag_4", "TAEE4.SA_High_PctChange_Lag_4", "TAEE4.SA_Low_PctChange_Lag_4", "TAEE4.SA_Close_PctChange_Lag_4", "TAEE4.SA_Open_PctChange_Lag_5", "TAEE4.SA_High_PctChange_Lag_5", "TAEE4.SA_Low_PctChange_Lag_5", "TAEE4.SA_Close_PctChange_Lag_5", "TAEE4.SA_Open_PctChange_Lag_6", "TAEE4.SA_High_PctChange_Lag_6", "TAEE4.SA_Low_PctChange_Lag_6", "TAEE4.SA_Close_PctChange_Lag_6", "TAEE4.SA_Open_PctChange_Lag_7", "TAEE4.SA_High_PctChange_Lag_7", "TAEE4.SA_Low_PctChange_Lag_7", "TAEE4.SA_Close_PctChange_Lag_7", "TAEE4.SA_Open_PctChange_Lag_8", "TAEE4.SA_High_PctChange_Lag_8", "TAEE4.SA_Low_PctChange_Lag_8", "TAEE4.SA_Close_PctChange_Lag_8", "TAEE4.SA_Open_PctChange_Lag_9", "TAEE4.SA_High_PctChange_Lag_9", "TAEE4.SA_Low_PctChange_Lag_9", "TAEE4.SA_Close_PctChange_Lag_9", "TAEE4.SA_Open_PctChange_Lag_10", "TAEE4.SA_High_PctChange_Lag_10", "TAEE4.SA_Low_PctChange_Lag_10", "TAEE4.SA_Close_PctChange_Lag_10", "TAEE4.SA_MM10_PctChange", "TAEE4.SA_Diff_OHLC_MM10", "TAEE4.SA_MM25_PctChange", "TAEE4.SA_Diff_OHLC_MM25", "TAEE4.SA_MM100_PctChange", "TAEE4.SA_Diff_OHLC_MM100", "TAEE4.SA_MM10_Menos_MM25", "TAEE4.SA_MM25_Menos_MM100", "TAEE4.SA_MM10_Menos_MM100", "TAEE4.SA_Volume_Lag_1", "TAEE4.SA_Volume_Lag_2", "TAEE4.SA_Volume_Lag_3", "TAEE4.SA_Volume_Lag_4", "TAEE4.SA_Volume_Lag_5", "TAEE4.SA_Spread_Lag_1", "TAEE4.SA_Spread_Lag_2", "TAEE4.SA_Spread_Lag_3", "TAEE4.SA_Spread_Lag_4", "TAEE4.SA_Spread_Lag_5", "TAEE4.SA_Volatilidade_Lag_1", "TAEE4.SA_Volatilidade_Lag_2", "TAEE4.SA_Volatilidade_Lag_3", "TAEE4.SA_Volatilidade_Lag_4", "TAEE4.SA_Volatilidade_Lag_5", "TAEE4.SA_Parkinson_Volatility_Lag_1", "TAEE4.SA_Parkinson_Volatility_Lag_2", "TAEE4.SA_Parkinson_Volatility_Lag_3", "TAEE4.SA_Parkinson_Volatility_Lag_4", "TAEE4.SA_Parkinson_Volatility_Lag_5", "TAEE4.SA_EMV_Lag_1", "TAEE4.SA_EMV_Lag_2", "TAEE4.SA_EMV_Lag_3", "TAEE4.SA_EMV_Lag_4", "TAEE4.SA_EMV_Lag_5", "TAEE4.SA_EMV_MA_Lag_1", "TAEE4.SA_EMV_MA_Lag_2", "TAEE4.SA_EMV_MA_Lag_3", "TAEE4.SA_EMV_MA_Lag_4", "TAEE4.SA_EMV_MA_Lag_5", "TAEE4.SA_VO_Lag_1", "TAEE4.SA_VO_Lag_2", "TAEE4.SA_VO_Lag_3", "TAEE4.SA_VO_Lag_4", "TAEE4.SA_VO_Lag_5", "TAEE4.SA_High_Max_50_Lag_1", "TAEE4.SA_High_Max_50_Lag_2", "TAEE4.SA_High_Max_50_Lag_3", "TAEE4.SA_High_Max_50_Lag_4", "TAEE4.SA_High_Max_50_Lag_5", "TAEE4.SA_Low_Min_50_Lag_1", "TAEE4.SA_Low_Min_50_Lag_2", "TAEE4.SA_Low_Min_50_Lag_3", "TAEE4.SA_Low_Min_50_Lag_4", "TAEE4.SA_Low_Min_50_Lag_5", "TAEE4.SA_MFI_Lag_1", "TAEE4.SA_MFI_Lag_2", "TAEE4.SA_MFI_Lag_3", "TAEE4.SA_MFI_Lag_4", "TAEE4.SA_MFI_Lag_5", "TAEE4.SA_Amihud_Lag_1", "TAEE4.SA_Amihud_Lag_2", "TAEE4.SA_Amihud_Lag_3", "TAEE4.SA_Amihud_Lag_4", "TAEE4.SA_Amihud_Lag_5", "TAEE4.SA_Roll_Spread_Lag_1", "TAEE4.SA_Roll_Spread_Lag_2", "TAEE4.SA_Roll_Spread_Lag_3", "TAEE4.SA_Roll_Spread_Lag_4", "TAEE4.SA_Roll_Spread_Lag_5", "TAEE4.<PERSON>_Hurst_Lag_1", "TAEE4.<PERSON>_<PERSON><PERSON>_Lag_2", "TAEE4.<PERSON>_<PERSON><PERSON>_Lag_3", "TAEE4.<PERSON>_<PERSON><PERSON>_Lag_4", "TAEE4.<PERSON>_<PERSON><PERSON>_Lag_5", "TAEE4.SA_Vol_per_Volume_Lag_1", "TAEE4.SA_Vol_per_Volume_Lag_2", "TAEE4.SA_Vol_per_Volume_Lag_3", "TAEE4.SA_Vol_per_Volume_Lag_4", "TAEE4.SA_Vol_per_Volume_Lag_5", "TAEE4.SA_CMF_Lag_1", "TAEE4.SA_CMF_Lag_2", "TAEE4.SA_CMF_Lag_3", "TAEE4.SA_CMF_Lag_4", "TAEE4.SA_CMF_Lag_5", "TAEE4.SA_AD_Line_Lag_1", "TAEE4.SA_AD_Line_Lag_2", "TAEE4.SA_AD_Line_Lag_3", "TAEE4.SA_AD_Line_Lag_4", "TAEE4.SA_AD_Line_Lag_5", "SHUL4.SA_Open", "SHUL4.SA_High", "SHUL4.SA_Low", "SHUL4.SA_Close", "SHUL4.SA_Volume", "SHUL4.SA_Media_OHLC", "SHUL4.SA_MM10", "SHUL4.SA_MM25", "SHUL4.SA_MM100", "SHUL4.SA_Media_OHLC_PctChange", "SHUL4.SA_Open_PctChange", "SHUL4.SA_High_PctChange", "SHUL4.SA_Low_PctChange", "SHUL4.SA_Close_PctChange", "SHUL4.SA_Close_PctChange_Historical", "SHUL4.SA_Volatilidade", "SHUL4.SA_Spread", "SHUL4.SA_Parkinson_Volatility", "SHUL4.SA_MFI", "SHUL4.SA_MFI_Historical", "SHUL4.SA_EMV", "SHUL4.SA_EMV_MA", "SHUL4.SA_Amihud", "SHUL4.SA_Amihud_Historical", "SHUL4.SA_Roll_Spread", "SHUL4.SA_Roll_Spread_Historical", "SHUL4.SA_Hurst", "SHUL4.SA_Hurst_Historical", "SHUL4.SA_Vol_per_Volume", "SHUL4.SA_Vol_per_Volume_Historical", "SHUL4.SA_CMF", "SHUL4.SA_CMF_Historical", "SHUL4.SA_AD_Line", "SHUL4.SA_AD_Line_Historical", "SHUL4.SA_VO", "SHUL4.SA_High_Max_50", "SHUL4.SA_Low_Min_50", "SHUL4.SA_Segunda", "SHUL4.SA_Terca", "SHUL4.SA_Quarta", "SHUL4.SA_Quinta", "SHUL4.SA_Sexta", "SHUL4.SA_Mes_1", "SHUL4.SA_Mes_2", "SHUL4.SA_Mes_3", "SHUL4.SA_Mes_4", "SHUL4.SA_Mes_5", "SHUL4.SA_Mes_6", "SHUL4.SA_Mes_7", "SHUL4.SA_Mes_8", "SHUL4.SA_Mes_9", "SHUL4.SA_Mes_10", "SHUL4.SA_Mes_11", "SHUL4.SA_Mes_12", "SHUL4.SA_Quarter_1", "SHUL4.SA_Quarter_2", "SHUL4.SA_Quarter_3", "SHUL4.SA_Quarter_4", "SHUL4.SA_Last_Day_Quarter", "SHUL4.SA_Pre_Feriado_Brasil", "SHUL4.SA_Media_OHLC_Anterior", "SHUL4.SA_Sinal_Compra", "SHUL4.SA_Sinal_Venda", "SHUL4.SA_Media_OHLC_Futura", "SHUL4.SA_Open_PctChange_Lag_1", "SHUL4.SA_High_PctChange_Lag_1", "SHUL4.SA_Low_PctChange_Lag_1", "SHUL4.SA_Close_PctChange_Lag_1", "SHUL4.SA_Open_PctChange_Lag_2", "SHUL4.SA_High_PctChange_Lag_2", "SHUL4.SA_Low_PctChange_Lag_2", "SHUL4.SA_Close_PctChange_Lag_2", "SHUL4.SA_Open_PctChange_Lag_3", "SHUL4.SA_High_PctChange_Lag_3", "SHUL4.SA_Low_PctChange_Lag_3", "SHUL4.SA_Close_PctChange_Lag_3", "SHUL4.SA_Open_PctChange_Lag_4", "SHUL4.SA_High_PctChange_Lag_4", "SHUL4.SA_Low_PctChange_Lag_4", "SHUL4.SA_Close_PctChange_Lag_4", "SHUL4.SA_Open_PctChange_Lag_5", "SHUL4.SA_High_PctChange_Lag_5", "SHUL4.SA_Low_PctChange_Lag_5", "SHUL4.SA_Close_PctChange_Lag_5", "SHUL4.SA_Open_PctChange_Lag_6", "SHUL4.SA_High_PctChange_Lag_6", "SHUL4.SA_Low_PctChange_Lag_6", "SHUL4.SA_Close_PctChange_Lag_6", "SHUL4.SA_Open_PctChange_Lag_7", "SHUL4.SA_High_PctChange_Lag_7", "SHUL4.SA_Low_PctChange_Lag_7", "SHUL4.SA_Close_PctChange_Lag_7", "SHUL4.SA_Open_PctChange_Lag_8", "SHUL4.SA_High_PctChange_Lag_8", "SHUL4.SA_Low_PctChange_Lag_8", "SHUL4.SA_Close_PctChange_Lag_8", "SHUL4.SA_Open_PctChange_Lag_9", "SHUL4.SA_High_PctChange_Lag_9", "SHUL4.SA_Low_PctChange_Lag_9", "SHUL4.SA_Close_PctChange_Lag_9", "SHUL4.SA_Open_PctChange_Lag_10", "SHUL4.SA_High_PctChange_Lag_10", "SHUL4.SA_Low_PctChange_Lag_10", "SHUL4.SA_Close_PctChange_Lag_10", "SHUL4.SA_MM10_PctChange", "SHUL4.SA_Diff_OHLC_MM10", "SHUL4.SA_MM25_PctChange", "SHUL4.SA_Diff_OHLC_MM25", "SHUL4.SA_MM100_PctChange", "SHUL4.SA_Diff_OHLC_MM100", "SHUL4.SA_MM10_Menos_MM25", "SHUL4.SA_MM25_Menos_MM100", "SHUL4.SA_MM10_Menos_MM100", "SHUL4.SA_Volume_Lag_1", "SHUL4.SA_Volume_Lag_2", "SHUL4.SA_Volume_Lag_3", "SHUL4.SA_Volume_Lag_4", "SHUL4.SA_Volume_Lag_5", "SHUL4.SA_Spread_Lag_1", "SHUL4.SA_Spread_Lag_2", "SHUL4.SA_Spread_Lag_3", "SHUL4.SA_Spread_Lag_4", "SHUL4.SA_Spread_Lag_5", "SHUL4.SA_Volatilidade_Lag_1", "SHUL4.SA_Volatilidade_Lag_2", "SHUL4.SA_Volatilidade_Lag_3", "SHUL4.SA_Volatilidade_Lag_4", "SHUL4.SA_Volatilidade_Lag_5", "SHUL4.SA_Parkinson_Volatility_Lag_1", "SHUL4.<PERSON>_Parkinson_Volatility_Lag_2", "SHUL4.SA_Parkinson_Volatility_Lag_3", "SHUL4.SA_Parkinson_Volatility_Lag_4", "SHUL4.<PERSON>_Parkinson_Volatility_Lag_5", "SHUL4.SA_EMV_Lag_1", "SHUL4.SA_EMV_Lag_2", "SHUL4.SA_EMV_Lag_3", "SHUL4.SA_EMV_Lag_4", "SHUL4.SA_EMV_Lag_5", "SHUL4.SA_EMV_MA_Lag_1", "SHUL4.SA_EMV_MA_Lag_2", "SHUL4.SA_EMV_MA_Lag_3", "SHUL4.SA_EMV_MA_Lag_4", "SHUL4.SA_EMV_MA_Lag_5", "SHUL4.SA_VO_Lag_1", "SHUL4.SA_VO_Lag_2", "SHUL4.SA_VO_Lag_3", "SHUL4.SA_VO_Lag_4", "SHUL4.SA_VO_Lag_5", "SHUL4.SA_High_Max_50_Lag_1", "SHUL4.SA_High_Max_50_Lag_2", "SHUL4.SA_High_Max_50_Lag_3", "SHUL4.SA_High_Max_50_Lag_4", "SHUL4.SA_High_Max_50_Lag_5", "SHUL4.SA_Low_Min_50_Lag_1", "SHUL4.SA_Low_Min_50_Lag_2", "SHUL4.SA_Low_Min_50_Lag_3", "SHUL4.SA_Low_Min_50_Lag_4", "SHUL4.SA_Low_Min_50_Lag_5", "SHUL4.SA_MFI_Lag_1", "SHUL4.SA_MFI_Lag_2", "SHUL4.SA_MFI_Lag_3", "SHUL4.SA_MFI_Lag_4", "SHUL4.SA_MFI_Lag_5", "SHUL4.SA_Amihud_Lag_1", "SHUL4.SA_Amihud_Lag_2", "SHUL4.SA_Amihud_Lag_3", "SHUL4.SA_Amihud_Lag_4", "SHUL4.SA_Amihud_Lag_5", "SHUL4.SA_Roll_Spread_Lag_1", "SHUL4.SA_Roll_Spread_Lag_2", "SHUL4.SA_Roll_Spread_Lag_3", "SHUL4.SA_Roll_Spread_Lag_4", "SHUL4.SA_Roll_Spread_Lag_5", "SHUL4.SA_Hurst_Lag_1", "SHUL4.<PERSON>_<PERSON><PERSON>_Lag_2", "SHUL4.<PERSON>_<PERSON>rst_Lag_3", "SHUL4.<PERSON>_<PERSON><PERSON>_Lag_4", "SHUL4.<PERSON>_<PERSON><PERSON>_Lag_5", "SHUL4.SA_Vol_per_Volume_Lag_1", "SHUL4.SA_Vol_per_Volume_Lag_2", "SHUL4.SA_Vol_per_Volume_Lag_3", "SHUL4.SA_Vol_per_Volume_Lag_4", "SHUL4.SA_Vol_per_Volume_Lag_5", "SHUL4.SA_CMF_Lag_1", "SHUL4.SA_CMF_Lag_2", "SHUL4.SA_CMF_Lag_3", "SHUL4.SA_CMF_Lag_4", "SHUL4.SA_CMF_Lag_5", "SHUL4.SA_AD_Line_Lag_1", "SHUL4.SA_AD_Line_Lag_2", "SHUL4.SA_AD_Line_Lag_3", "SHUL4.SA_AD_Line_Lag_4", "SHUL4.SA_AD_Line_Lag_5", "PNVL3.SA_Open", "PNVL3.SA_High", "PNVL3.SA_Low", "PNVL3.SA_Close", "PNVL3.SA_Volume", "PNVL3.SA_Media_OHLC", "PNVL3.SA_MM10", "PNVL3.SA_MM25", "PNVL3.SA_MM100", "PNVL3.SA_Media_OHLC_PctChange", "PNVL3.SA_Open_PctChange", "PNVL3.SA_High_PctChange", "PNVL3.SA_Low_PctChange", "PNVL3.SA_Close_PctChange", "PNVL3.SA_Close_PctChange_Historical", "PNVL3.SA_Volatilidade", "PNVL3.SA_Spread", "PNVL3.SA_Parkinson_Volatility", "PNVL3.SA_MFI", "PNVL3.SA_MFI_Historical", "PNVL3.SA_EMV", "PNVL3.SA_EMV_MA", "PNVL3.SA_Amihud", "PNVL3.SA_Amihud_Historical", "PNVL3.SA_Roll_Spread", "PNVL3.SA_Roll_Spread_Historical", "PNVL3.SA_Hurst", "PNVL3.SA_Hurst_Historical", "PNVL3.SA_Vol_per_Volume", "PNVL3.SA_Vol_per_Volume_Historical", "PNVL3.SA_CMF", "PNVL3.SA_CMF_Historical", "PNVL3.SA_AD_Line", "PNVL3.SA_AD_Line_Historical", "PNVL3.SA_VO", "PNVL3.SA_High_Max_50", "PNVL3.SA_Low_Min_50", "PNVL3.SA_Segunda", "PNVL3.SA_Terca", "PNVL3.SA_Quarta", "PNVL3.SA_Quinta", "PNVL3.SA_Sexta", "PNVL3.SA_Mes_1", "PNVL3.SA_Mes_2", "PNVL3.SA_Mes_3", "PNVL3.SA_Mes_4", "PNVL3.SA_Mes_5", "PNVL3.SA_Mes_6", "PNVL3.SA_Mes_7", "PNVL3.SA_Mes_8", "PNVL3.SA_Mes_9", "PNVL3.SA_Mes_10", "PNVL3.SA_Mes_11", "PNVL3.SA_Mes_12", "PNVL3.SA_Quarter_1", "PNVL3.SA_Quarter_2", "PNVL3.SA_Quarter_3", "PNVL3.SA_Quarter_4", "PNVL3.SA_Last_Day_Quarter", "PNVL3.SA_Pre_Feriado_Brasil", "PNVL3.SA_Media_OHLC_Anterior", "PNVL3.SA_Sinal_Compra", "PNVL3.SA_Sinal_Venda", "PNVL3.SA_Media_OHLC_Futura", "PNVL3.SA_Open_PctChange_Lag_1", "PNVL3.SA_High_PctChange_Lag_1", "PNVL3.SA_Low_PctChange_Lag_1", "PNVL3.SA_Close_PctChange_Lag_1", "PNVL3.SA_Open_PctChange_Lag_2", "PNVL3.SA_High_PctChange_Lag_2", "PNVL3.SA_Low_PctChange_Lag_2", "PNVL3.SA_Close_PctChange_Lag_2", "PNVL3.SA_Open_PctChange_Lag_3", "PNVL3.SA_High_PctChange_Lag_3", "PNVL3.SA_Low_PctChange_Lag_3", "PNVL3.SA_Close_PctChange_Lag_3", "PNVL3.SA_Open_PctChange_Lag_4", "PNVL3.SA_High_PctChange_Lag_4", "PNVL3.SA_Low_PctChange_Lag_4", "PNVL3.SA_Close_PctChange_Lag_4", "PNVL3.SA_Open_PctChange_Lag_5", "PNVL3.SA_High_PctChange_Lag_5", "PNVL3.SA_Low_PctChange_Lag_5", "PNVL3.SA_Close_PctChange_Lag_5", "PNVL3.SA_Open_PctChange_Lag_6", "PNVL3.SA_High_PctChange_Lag_6", "PNVL3.SA_Low_PctChange_Lag_6", "PNVL3.SA_Close_PctChange_Lag_6", "PNVL3.SA_Open_PctChange_Lag_7", "PNVL3.SA_High_PctChange_Lag_7", "PNVL3.SA_Low_PctChange_Lag_7", "PNVL3.SA_Close_PctChange_Lag_7", "PNVL3.SA_Open_PctChange_Lag_8", "PNVL3.SA_High_PctChange_Lag_8", "PNVL3.SA_Low_PctChange_Lag_8", "PNVL3.SA_Close_PctChange_Lag_8", "PNVL3.SA_Open_PctChange_Lag_9", "PNVL3.SA_High_PctChange_Lag_9", "PNVL3.SA_Low_PctChange_Lag_9", "PNVL3.SA_Close_PctChange_Lag_9", "PNVL3.SA_Open_PctChange_Lag_10", "PNVL3.SA_High_PctChange_Lag_10", "PNVL3.SA_Low_PctChange_Lag_10", "PNVL3.SA_Close_PctChange_Lag_10", "PNVL3.SA_MM10_PctChange", "PNVL3.SA_Diff_OHLC_MM10", "PNVL3.SA_MM25_PctChange", "PNVL3.SA_Diff_OHLC_MM25", "PNVL3.SA_MM100_PctChange", "PNVL3.SA_Diff_OHLC_MM100", "PNVL3.SA_MM10_Menos_MM25", "PNVL3.SA_MM25_Menos_MM100", "PNVL3.SA_MM10_Menos_MM100", "PNVL3.SA_Volume_Lag_1", "PNVL3.SA_Volume_Lag_2", "PNVL3.SA_Volume_Lag_3", "PNVL3.SA_Volume_Lag_4", "PNVL3.SA_Volume_Lag_5", "PNVL3.SA_Spread_Lag_1", "PNVL3.SA_Spread_Lag_2", "PNVL3.SA_Spread_Lag_3", "PNVL3.SA_Spread_Lag_4", "PNVL3.SA_Spread_Lag_5", "PNVL3.SA_Volatilidade_Lag_1", "PNVL3.SA_Volatilidade_Lag_2", "PNVL3.SA_Volatilidade_Lag_3", "PNVL3.SA_Volatilidade_Lag_4", "PNVL3.SA_Volatilidade_Lag_5", "PNVL3.SA_Parkinson_Volatility_Lag_1", "PNVL3.SA_Parkinson_Volatility_Lag_2", "PNVL3.SA_Parkinson_Volatility_Lag_3", "PNVL3.SA_Parkinson_Volatility_Lag_4", "PNVL3.SA_Parkinson_Volatility_Lag_5", "PNVL3.SA_EMV_Lag_1", "PNVL3.SA_EMV_Lag_2", "PNVL3.SA_EMV_Lag_3", "PNVL3.SA_EMV_Lag_4", "PNVL3.SA_EMV_Lag_5", "PNVL3.SA_EMV_MA_Lag_1", "PNVL3.SA_EMV_MA_Lag_2", "PNVL3.SA_EMV_MA_Lag_3", "PNVL3.SA_EMV_MA_Lag_4", "PNVL3.SA_EMV_MA_Lag_5", "PNVL3.SA_VO_Lag_1", "PNVL3.SA_VO_Lag_2", "PNVL3.SA_VO_Lag_3", "PNVL3.SA_VO_Lag_4", "PNVL3.SA_VO_Lag_5", "PNVL3.SA_High_Max_50_Lag_1", "PNVL3.SA_High_Max_50_Lag_2", "PNVL3.SA_High_Max_50_Lag_3", "PNVL3.SA_High_Max_50_Lag_4", "PNVL3.SA_High_Max_50_Lag_5", "PNVL3.SA_Low_Min_50_Lag_1", "PNVL3.SA_Low_Min_50_Lag_2", "PNVL3.SA_Low_Min_50_Lag_3", "PNVL3.SA_Low_Min_50_Lag_4", "PNVL3.SA_Low_Min_50_Lag_5", "PNVL3.SA_MFI_Lag_1", "PNVL3.SA_MFI_Lag_2", "PNVL3.SA_MFI_Lag_3", "PNVL3.SA_MFI_Lag_4", "PNVL3.SA_MFI_Lag_5", "PNVL3.SA_Amihud_Lag_1", "PNVL3.SA_Amihud_Lag_2", "PNVL3.SA_Amihud_Lag_3", "PNVL3.SA_Amihud_Lag_4", "PNVL3.SA_Amihud_Lag_5", "PNVL3.SA_Roll_Spread_Lag_1", "PNVL3.SA_Roll_Spread_Lag_2", "PNVL3.SA_Roll_Spread_Lag_3", "PNVL3.SA_Roll_Spread_Lag_4", "PNVL3.SA_Roll_Spread_Lag_5", "PNVL3.SA_Hurst_Lag_1", "PNVL3.SA_Hurst_Lag_2", "PNVL3.SA_Hurst_Lag_3", "PNVL3.SA_Hurst_Lag_4", "PNVL3.SA_<PERSON>rst_Lag_5", "PNVL3.SA_Vol_per_Volume_Lag_1", "PNVL3.SA_Vol_per_Volume_Lag_2", "PNVL3.SA_Vol_per_Volume_Lag_3", "PNVL3.SA_Vol_per_Volume_Lag_4", "PNVL3.SA_Vol_per_Volume_Lag_5", "PNVL3.SA_CMF_Lag_1", "PNVL3.SA_CMF_Lag_2", "PNVL3.SA_CMF_Lag_3", "PNVL3.SA_CMF_Lag_4", "PNVL3.SA_CMF_Lag_5", "PNVL3.SA_AD_Line_Lag_1", "PNVL3.SA_AD_Line_Lag_2", "PNVL3.SA_AD_Line_Lag_3", "PNVL3.SA_AD_Line_Lag_4", "PNVL3.SA_AD_Line_Lag_5", "CMIG4.SA_Open", "CMIG4.SA_High", "CMIG4.SA_Low", "CMIG4.SA_Close", "CMIG4.SA_Volume", "CMIG4.SA_Media_OHLC", "CMIG4.SA_MM10", "CMIG4.SA_MM25", "CMIG4.SA_MM100", "CMIG4.SA_Media_OHLC_PctChange", "CMIG4.SA_Open_PctChange", "CMIG4.SA_High_PctChange", "CMIG4.SA_Low_PctChange", "CMIG4.SA_Close_PctChange", "CMIG4.SA_Close_PctChange_Historical", "CMIG4.SA_Volatilidade", "CMIG4.SA_Spread", "CMIG4.SA_Parkinson_Volatility", "CMIG4.SA_MFI", "CMIG4.SA_MFI_Historical", "CMIG4.SA_EMV", "CMIG4.SA_EMV_MA", "CMIG4.SA_Amihud", "CMIG4.SA_Amihud_Historical", "CMIG4.SA_Roll_Spread", "CMIG4.SA_Roll_Spread_Historical", "CMIG4.SA_Hurst", "CMIG4.SA_<PERSON><PERSON>_Historical", "CMIG4.SA_Vol_per_Volume", "CMIG4.SA_Vol_per_Volume_Historical", "CMIG4.SA_CMF", "CMIG4.SA_CMF_Historical", "CMIG4.SA_AD_Line", "CMIG4.SA_AD_Line_Historical", "CMIG4.SA_VO", "CMIG4.SA_High_Max_50", "CMIG4.SA_Low_Min_50", "CMIG4.SA_Segunda", "CMIG4.SA_Terca", "CMIG4.SA_Quarta", "CMIG4.SA_Quinta", "CMIG4.SA_Sexta", "CMIG4.SA_Mes_1", "CMIG4.SA_Mes_2", "CMIG4.SA_Mes_3", "CMIG4.SA_Mes_4", "CMIG4.SA_Mes_5", "CMIG4.SA_Mes_6", "CMIG4.SA_Mes_7", "CMIG4.SA_Mes_8", "CMIG4.SA_Mes_9", "CMIG4.SA_Mes_10", "CMIG4.SA_Mes_11", "CMIG4.SA_Mes_12", "CMIG4.SA_Quarter_1", "CMIG4.SA_Quarter_2", "CMIG4.SA_Quarter_3", "CMIG4.SA_Quarter_4", "CMIG4.SA_Last_Day_Quarter", "CMIG4.SA_Pre_Feriado_Brasil", "CMIG4.SA_Media_OHLC_Anterior", "CMIG4.SA_Sinal_Compra", "CMIG4.SA_Sinal_Venda", "CMIG4.SA_Media_OHLC_Futura", "CMIG4.SA_Open_PctChange_Lag_1", "CMIG4.SA_High_PctChange_Lag_1", "CMIG4.SA_Low_PctChange_Lag_1", "CMIG4.SA_Close_PctChange_Lag_1", "CMIG4.SA_Open_PctChange_Lag_2", "CMIG4.SA_High_PctChange_Lag_2", "CMIG4.SA_Low_PctChange_Lag_2", "CMIG4.SA_Close_PctChange_Lag_2", "CMIG4.SA_Open_PctChange_Lag_3", "CMIG4.SA_High_PctChange_Lag_3", "CMIG4.SA_Low_PctChange_Lag_3", "CMIG4.SA_Close_PctChange_Lag_3", "CMIG4.SA_Open_PctChange_Lag_4", "CMIG4.SA_High_PctChange_Lag_4", "CMIG4.SA_Low_PctChange_Lag_4", "CMIG4.SA_Close_PctChange_Lag_4", "CMIG4.SA_Open_PctChange_Lag_5", "CMIG4.SA_High_PctChange_Lag_5", "CMIG4.SA_Low_PctChange_Lag_5", "CMIG4.SA_Close_PctChange_Lag_5", "CMIG4.SA_Open_PctChange_Lag_6", "CMIG4.SA_High_PctChange_Lag_6", "CMIG4.SA_Low_PctChange_Lag_6", "CMIG4.SA_Close_PctChange_Lag_6", "CMIG4.SA_Open_PctChange_Lag_7", "CMIG4.SA_High_PctChange_Lag_7", "CMIG4.SA_Low_PctChange_Lag_7", "CMIG4.SA_Close_PctChange_Lag_7", "CMIG4.SA_Open_PctChange_Lag_8", "CMIG4.SA_High_PctChange_Lag_8", "CMIG4.SA_Low_PctChange_Lag_8", "CMIG4.SA_Close_PctChange_Lag_8", "CMIG4.SA_Open_PctChange_Lag_9", "CMIG4.SA_High_PctChange_Lag_9", "CMIG4.SA_Low_PctChange_Lag_9", "CMIG4.SA_Close_PctChange_Lag_9", "CMIG4.SA_Open_PctChange_Lag_10", "CMIG4.SA_High_PctChange_Lag_10", "CMIG4.SA_Low_PctChange_Lag_10", "CMIG4.SA_Close_PctChange_Lag_10", "CMIG4.SA_MM10_PctChange", "CMIG4.SA_Diff_OHLC_MM10", "CMIG4.SA_MM25_PctChange", "CMIG4.SA_Diff_OHLC_MM25", "CMIG4.SA_MM100_PctChange", "CMIG4.SA_Diff_OHLC_MM100", "CMIG4.SA_MM10_Menos_MM25", "CMIG4.SA_MM25_Menos_MM100", "CMIG4.SA_MM10_Menos_MM100", "CMIG4.SA_Volume_Lag_1", "CMIG4.SA_Volume_Lag_2", "CMIG4.SA_Volume_Lag_3", "CMIG4.SA_Volume_Lag_4", "CMIG4.SA_Volume_Lag_5", "CMIG4.SA_Spread_Lag_1", "CMIG4.SA_Spread_Lag_2", "CMIG4.SA_Spread_Lag_3", "CMIG4.SA_Spread_Lag_4", "CMIG4.SA_Spread_Lag_5", "CMIG4.SA_Volatilidade_Lag_1", "CMIG4.SA_Volatilidade_Lag_2", "CMIG4.SA_Volatilidade_Lag_3", "CMIG4.SA_Volatilidade_Lag_4", "CMIG4.SA_Volatilidade_Lag_5", "CMIG4.SA_Parkinson_Volatility_Lag_1", "CMIG4.SA_Parkinson_Volatility_Lag_2", "CMIG4.SA_Parkinson_Volatility_Lag_3", "CMIG4.SA_Parkinson_Volatility_Lag_4", "CMIG4.SA_Parkinson_Volatility_Lag_5", "CMIG4.SA_EMV_Lag_1", "CMIG4.SA_EMV_Lag_2", "CMIG4.SA_EMV_Lag_3", "CMIG4.SA_EMV_Lag_4", "CMIG4.SA_EMV_Lag_5", "CMIG4.SA_EMV_MA_Lag_1", "CMIG4.SA_EMV_MA_Lag_2", "CMIG4.SA_EMV_MA_Lag_3", "CMIG4.SA_EMV_MA_Lag_4", "CMIG4.SA_EMV_MA_Lag_5", "CMIG4.SA_VO_Lag_1", "CMIG4.SA_VO_Lag_2", "CMIG4.SA_VO_Lag_3", "CMIG4.SA_VO_Lag_4", "CMIG4.SA_VO_Lag_5", "CMIG4.SA_High_Max_50_Lag_1", "CMIG4.SA_High_Max_50_Lag_2", "CMIG4.SA_High_Max_50_Lag_3", "CMIG4.SA_High_Max_50_Lag_4", "CMIG4.SA_High_Max_50_Lag_5", "CMIG4.SA_Low_Min_50_Lag_1", "CMIG4.SA_Low_Min_50_Lag_2", "CMIG4.SA_Low_Min_50_Lag_3", "CMIG4.SA_Low_Min_50_Lag_4", "CMIG4.SA_Low_Min_50_Lag_5", "CMIG4.SA_MFI_Lag_1", "CMIG4.SA_MFI_Lag_2", "CMIG4.SA_MFI_Lag_3", "CMIG4.SA_MFI_Lag_4", "CMIG4.SA_MFI_Lag_5", "CMIG4.SA_Amihud_Lag_1", "CMIG4.SA_Amihud_Lag_2", "CMIG4.SA_Amihud_Lag_3", "CMIG4.SA_Amihud_Lag_4", "CMIG4.SA_Amihud_Lag_5", "CMIG4.SA_Roll_Spread_Lag_1", "CMIG4.SA_Roll_Spread_Lag_2", "CMIG4.SA_Roll_Spread_Lag_3", "CMIG4.SA_Roll_Spread_Lag_4", "CMIG4.SA_Roll_Spread_Lag_5", "CMIG4.SA_Hurst_Lag_1", "CMIG4.<PERSON>_Hu<PERSON>_Lag_2", "CMIG4.<PERSON>_Hurst_Lag_3", "CMIG4.<PERSON>_Hu<PERSON>_Lag_4", "CMIG4.<PERSON>_<PERSON><PERSON>_Lag_5", "CMIG4.SA_Vol_per_Volume_Lag_1", "CMIG4.SA_Vol_per_Volume_Lag_2", "CMIG4.SA_Vol_per_Volume_Lag_3", "CMIG4.SA_Vol_per_Volume_Lag_4", "CMIG4.SA_Vol_per_Volume_Lag_5", "CMIG4.SA_CMF_Lag_1", "CMIG4.SA_CMF_Lag_2", "CMIG4.SA_CMF_Lag_3", "CMIG4.SA_CMF_Lag_4", "CMIG4.SA_CMF_Lag_5", "CMIG4.SA_AD_Line_Lag_1", "CMIG4.SA_AD_Line_Lag_2", "CMIG4.SA_AD_Line_Lag_3", "CMIG4.SA_AD_Line_Lag_4", "CMIG4.SA_AD_Line_Lag_5", "ALUP11.SA_Close", "ALUP11.SA_High", "ALUP11.SA_Low", "ALUP11.SA_Open", "ALUP11.SA_Volume", "ALUP11.SA_Media_OHLC", "ALUP11.SA_MM10", "ALUP11.SA_MM25", "ALUP11.SA_MM100", "ALUP11.SA_Media_OHLC_PctChange", "ALUP11.SA_Open_PctChange", "ALUP11.SA_High_PctChange", "ALUP11.SA_Low_PctChange", "ALUP11.SA_Close_PctChange", "ALUP11.SA_Close_PctChange_Historical", "ALUP11.SA_Volatilidade", "ALUP11.SA_Spread", "ALUP11.SA_Parkinson_Volatility", "ALUP11.SA_MFI", "ALUP11.SA_MFI_Historical", "ALUP11.SA_EMV", "ALUP11.SA_EMV_MA", "ALUP11.SA_Amihud", "ALUP11.SA_Amihud_Historical", "ALUP11.SA_Roll_Spread", "ALUP11.SA_Roll_Spread_Historical", "ALUP11.SA_Hurst", "ALUP11.<PERSON>_<PERSON><PERSON>_Historical", "ALUP11.SA_Vol_per_Volume", "ALUP11.SA_Vol_per_Volume_Historical", "ALUP11.SA_CMF", "ALUP11.SA_CMF_Historical", "ALUP11.SA_AD_Line", "ALUP11.SA_AD_Line_Historical", "ALUP11.SA_VO", "ALUP11.SA_High_Max_50", "ALUP11.SA_Low_Min_50", "ALUP11.SA_Segunda", "ALUP11.SA_Terca", "ALUP11.SA_Quarta", "ALUP11.SA_Quinta", "ALUP11.SA_Sexta", "ALUP11.SA_Mes_1", "ALUP11.SA_Mes_2", "ALUP11.SA_Mes_3", "ALUP11.SA_Mes_4", "ALUP11.SA_Mes_5", "ALUP11.SA_Mes_6", "ALUP11.SA_Mes_7", "ALUP11.SA_Mes_8", "ALUP11.SA_Mes_9", "ALUP11.SA_Mes_10", "ALUP11.SA_Mes_11", "ALUP11.SA_Mes_12", "ALUP11.SA_Quarter_1", "ALUP11.SA_Quarter_2", "ALUP11.SA_Quarter_3", "ALUP11.SA_Quarter_4", "ALUP11.SA_Last_Day_Quarter", "ALUP11.SA_Pre_Feriado_Brasil", "ALUP11.SA_Media_OHLC_Anterior", "ALUP11.SA_Sinal_Compra", "ALUP11.SA_Sinal_Venda", "ALUP11.SA_Media_OHLC_Futura", "ALUP11.SA_Open_PctChange_Lag_1", "ALUP11.SA_High_PctChange_Lag_1", "ALUP11.SA_Low_PctChange_Lag_1", "ALUP11.SA_Close_PctChange_Lag_1", "ALUP11.SA_Open_PctChange_Lag_2", "ALUP11.SA_High_PctChange_Lag_2", "ALUP11.SA_Low_PctChange_Lag_2", "ALUP11.SA_Close_PctChange_Lag_2", "ALUP11.SA_Open_PctChange_Lag_3", "ALUP11.SA_High_PctChange_Lag_3", "ALUP11.SA_Low_PctChange_Lag_3", "ALUP11.SA_Close_PctChange_Lag_3", "ALUP11.SA_Open_PctChange_Lag_4", "ALUP11.SA_High_PctChange_Lag_4", "ALUP11.SA_Low_PctChange_Lag_4", "ALUP11.SA_Close_PctChange_Lag_4", "ALUP11.SA_Open_PctChange_Lag_5", "ALUP11.SA_High_PctChange_Lag_5", "ALUP11.SA_Low_PctChange_Lag_5", "ALUP11.SA_Close_PctChange_Lag_5", "ALUP11.SA_Open_PctChange_Lag_6", "ALUP11.SA_High_PctChange_Lag_6", "ALUP11.SA_Low_PctChange_Lag_6", "ALUP11.SA_Close_PctChange_Lag_6", "ALUP11.SA_Open_PctChange_Lag_7", "ALUP11.SA_High_PctChange_Lag_7", "ALUP11.SA_Low_PctChange_Lag_7", "ALUP11.SA_Close_PctChange_Lag_7", "ALUP11.SA_Open_PctChange_Lag_8", "ALUP11.SA_High_PctChange_Lag_8", "ALUP11.SA_Low_PctChange_Lag_8", "ALUP11.SA_Close_PctChange_Lag_8", "ALUP11.SA_Open_PctChange_Lag_9", "ALUP11.SA_High_PctChange_Lag_9", "ALUP11.SA_Low_PctChange_Lag_9", "ALUP11.SA_Close_PctChange_Lag_9", "ALUP11.SA_Open_PctChange_Lag_10", "ALUP11.SA_High_PctChange_Lag_10", "ALUP11.SA_Low_PctChange_Lag_10", "ALUP11.SA_Close_PctChange_Lag_10", "ALUP11.SA_MM10_PctChange", "ALUP11.SA_Diff_OHLC_MM10", "ALUP11.SA_MM25_PctChange", "ALUP11.SA_Diff_OHLC_MM25", "ALUP11.SA_MM100_PctChange", "ALUP11.SA_Diff_OHLC_MM100", "ALUP11.SA_MM10_Menos_MM25", "ALUP11.SA_MM25_Menos_MM100", "ALUP11.SA_MM10_Menos_MM100", "ALUP11.SA_Volume_Lag_1", "ALUP11.SA_Volume_Lag_2", "ALUP11.SA_Volume_Lag_3", "ALUP11.SA_Volume_Lag_4", "ALUP11.SA_Volume_Lag_5", "ALUP11.SA_Spread_Lag_1", "ALUP11.SA_Spread_Lag_2", "ALUP11.SA_Spread_Lag_3", "ALUP11.SA_Spread_Lag_4", "ALUP11.SA_Spread_Lag_5", "ALUP11.SA_Volatilidade_Lag_1", "ALUP11.SA_Volatilidade_Lag_2", "ALUP11.SA_Volatilidade_Lag_3", "ALUP11.SA_Volatilidade_Lag_4", "ALUP11.SA_Volatilidade_Lag_5", "ALUP11.<PERSON>_Parkinson_Volatility_Lag_1", "ALUP11.<PERSON>_Parkinson_Volatility_Lag_2", "ALUP11.<PERSON>_Parkinson_Volatility_Lag_3", "ALUP11.<PERSON>_Parkinson_Volatility_Lag_4", "ALUP11.<PERSON>_Parkinson_Volatility_Lag_5", "ALUP11.SA_EMV_Lag_1", "ALUP11.SA_EMV_Lag_2", "ALUP11.SA_EMV_Lag_3", "ALUP11.SA_EMV_Lag_4", "ALUP11.SA_EMV_Lag_5", "ALUP11.SA_EMV_MA_Lag_1", "ALUP11.SA_EMV_MA_Lag_2", "ALUP11.SA_EMV_MA_Lag_3", "ALUP11.SA_EMV_MA_Lag_4", "ALUP11.SA_EMV_MA_Lag_5", "ALUP11.SA_VO_Lag_1", "ALUP11.SA_VO_Lag_2", "ALUP11.SA_VO_Lag_3", "ALUP11.SA_VO_Lag_4", "ALUP11.SA_VO_Lag_5", "ALUP11.SA_High_Max_50_Lag_1", "ALUP11.SA_High_Max_50_Lag_2", "ALUP11.SA_High_Max_50_Lag_3", "ALUP11.SA_High_Max_50_Lag_4", "ALUP11.SA_High_Max_50_Lag_5", "ALUP11.SA_Low_Min_50_Lag_1", "ALUP11.SA_Low_Min_50_Lag_2", "ALUP11.SA_Low_Min_50_Lag_3", "ALUP11.SA_Low_Min_50_Lag_4", "ALUP11.<PERSON>_Low_Min_50_Lag_5", "ALUP11.SA_MFI_Lag_1", "ALUP11.SA_MFI_Lag_2", "ALUP11.SA_MFI_Lag_3", "ALUP11.SA_MFI_Lag_4", "ALUP11.SA_MFI_Lag_5", "ALUP11.SA_Amihud_Lag_1", "ALUP11.SA_Amihud_Lag_2", "ALUP11.SA_Amihud_Lag_3", "ALUP11.SA_Amihud_Lag_4", "ALUP11.SA_Amihud_Lag_5", "ALUP11.SA_Roll_Spread_Lag_1", "ALUP11.SA_Roll_Spread_Lag_2", "ALUP11.SA_Roll_Spread_Lag_3", "ALUP11.SA_Roll_Spread_Lag_4", "ALUP11.SA_Roll_Spread_Lag_5", "ALUP11.<PERSON>_Hurst_Lag_1", "ALUP11.<PERSON>_Hu<PERSON>_Lag_2", "ALUP11.<PERSON>_Hurst_Lag_3", "ALUP11.<PERSON>_<PERSON><PERSON>_Lag_4", "ALUP11.<PERSON>_<PERSON><PERSON>_Lag_5", "ALUP11.SA_Vol_per_Volume_Lag_1", "ALUP11.SA_Vol_per_Volume_Lag_2", "ALUP11.SA_Vol_per_Volume_Lag_3", "ALUP11.SA_Vol_per_Volume_Lag_4", "ALUP11.SA_Vol_per_Volume_Lag_5", "ALUP11.SA_CMF_Lag_1", "ALUP11.SA_CMF_Lag_2", "ALUP11.SA_CMF_Lag_3", "ALUP11.SA_CMF_Lag_4", "ALUP11.SA_CMF_Lag_5", "ALUP11.SA_AD_Line_Lag_1", "ALUP11.SA_AD_Line_Lag_2", "ALUP11.SA_AD_Line_Lag_3", "ALUP11.SA_AD_Line_Lag_4", "ALUP11.SA_AD_Line_Lag_5", "ROMI3.SA_Open", "ROMI3.SA_High", "ROMI3.SA_Low", "ROMI3.SA_Close", "ROMI3.SA_Volume", "ROMI3.SA_Media_OHLC", "ROMI3.SA_MM10", "ROMI3.SA_MM25", "ROMI3.SA_MM100", "ROMI3.SA_Media_OHLC_PctChange", "ROMI3.SA_Open_PctChange", "ROMI3.SA_High_PctChange", "ROMI3.SA_Low_PctChange", "ROMI3.SA_Close_PctChange", "ROMI3.SA_Close_PctChange_Historical", "ROMI3.SA_Volatilidade", "ROMI3.SA_Spread", "ROMI3.SA_Parkinson_Volatility", "ROMI3.SA_MFI", "ROMI3.SA_MFI_Historical", "ROMI3.SA_EMV", "ROMI3.SA_EMV_MA", "ROMI3.SA_Amihud", "ROMI3.SA_Amihud_Historical", "ROMI3.SA_Roll_Spread", "ROMI3.SA_Roll_Spread_Historical", "ROMI3.SA_Hurst", "ROMI3.SA_<PERSON>rst_Historical", "ROMI3.SA_Vol_per_Volume", "ROMI3.SA_Vol_per_Volume_Historical", "ROMI3.SA_CMF", "ROMI3.SA_CMF_Historical", "ROMI3.SA_AD_Line", "ROMI3.SA_AD_Line_Historical", "ROMI3.SA_VO", "ROMI3.SA_High_Max_50", "ROMI3.SA_Low_Min_50", "ROMI3.SA_Segunda", "ROMI3.SA_Terca", "ROMI3.SA_Quarta", "ROMI3.SA_Quinta", "ROMI3.SA_Sexta", "ROMI3.SA_Mes_1", "ROMI3.SA_Mes_2", "ROMI3.SA_Mes_3", "ROMI3.SA_Mes_4", "ROMI3.SA_Mes_5", "ROMI3.SA_Mes_6", "ROMI3.SA_Mes_7", "ROMI3.SA_Mes_8", "ROMI3.SA_Mes_9", "ROMI3.SA_Mes_10", "ROMI3.SA_Mes_11", "ROMI3.SA_Mes_12", "ROMI3.SA_Quarter_1", "ROMI3.SA_Quarter_2", "ROMI3.SA_Quarter_3", "ROMI3.SA_Quarter_4", "ROMI3.SA_Last_Day_Quarter", "ROMI3.SA_Pre_Feriado_Brasil", "ROMI3.SA_Media_OHLC_Anterior", "ROMI3.SA_Sinal_Compra", "ROMI3.SA_Sinal_Venda", "ROMI3.SA_Media_OHLC_Futura", "ROMI3.SA_Open_PctChange_Lag_1", "ROMI3.SA_High_PctChange_Lag_1", "ROMI3.SA_Low_PctChange_Lag_1", "ROMI3.SA_Close_PctChange_Lag_1", "ROMI3.SA_Open_PctChange_Lag_2", "ROMI3.SA_High_PctChange_Lag_2", "ROMI3.SA_Low_PctChange_Lag_2", "ROMI3.SA_Close_PctChange_Lag_2", "ROMI3.SA_Open_PctChange_Lag_3", "ROMI3.SA_High_PctChange_Lag_3", "ROMI3.SA_Low_PctChange_Lag_3", "ROMI3.SA_Close_PctChange_Lag_3", "ROMI3.SA_Open_PctChange_Lag_4", "ROMI3.SA_High_PctChange_Lag_4", "ROMI3.SA_Low_PctChange_Lag_4", "ROMI3.SA_Close_PctChange_Lag_4", "ROMI3.SA_Open_PctChange_Lag_5", "ROMI3.SA_High_PctChange_Lag_5", "ROMI3.SA_Low_PctChange_Lag_5", "ROMI3.SA_Close_PctChange_Lag_5", "ROMI3.SA_Open_PctChange_Lag_6", "ROMI3.SA_High_PctChange_Lag_6", "ROMI3.SA_Low_PctChange_Lag_6", "ROMI3.SA_Close_PctChange_Lag_6", "ROMI3.SA_Open_PctChange_Lag_7", "ROMI3.SA_High_PctChange_Lag_7", "ROMI3.SA_Low_PctChange_Lag_7", "ROMI3.SA_Close_PctChange_Lag_7", "ROMI3.SA_Open_PctChange_Lag_8", "ROMI3.SA_High_PctChange_Lag_8", "ROMI3.SA_Low_PctChange_Lag_8", "ROMI3.SA_Close_PctChange_Lag_8", "ROMI3.SA_Open_PctChange_Lag_9", "ROMI3.SA_High_PctChange_Lag_9", "ROMI3.SA_Low_PctChange_Lag_9", "ROMI3.SA_Close_PctChange_Lag_9", "ROMI3.SA_Open_PctChange_Lag_10", "ROMI3.SA_High_PctChange_Lag_10", "ROMI3.SA_Low_PctChange_Lag_10", "ROMI3.SA_Close_PctChange_Lag_10", "ROMI3.SA_MM10_PctChange", "ROMI3.SA_Diff_OHLC_MM10", "ROMI3.SA_MM25_PctChange", "ROMI3.SA_Diff_OHLC_MM25", "ROMI3.SA_MM100_PctChange", "ROMI3.SA_Diff_OHLC_MM100", "ROMI3.SA_MM10_Menos_MM25", "ROMI3.SA_MM25_Menos_MM100", "ROMI3.SA_MM10_Menos_MM100", "ROMI3.SA_Volume_Lag_1", "ROMI3.SA_Volume_Lag_2", "ROMI3.SA_Volume_Lag_3", "ROMI3.SA_Volume_Lag_4", "ROMI3.SA_Volume_Lag_5", "ROMI3.SA_Spread_Lag_1", "ROMI3.SA_Spread_Lag_2", "ROMI3.SA_Spread_Lag_3", "ROMI3.SA_Spread_Lag_4", "ROMI3.SA_Spread_Lag_5", "ROMI3.SA_Volatilidade_Lag_1", "ROMI3.SA_Volatilidade_Lag_2", "ROMI3.SA_Volatilidade_Lag_3", "ROMI3.SA_Volatilidade_Lag_4", "ROMI3.SA_Volatilidade_Lag_5", "ROMI3.SA_Parkinson_Volatility_Lag_1", "ROMI3.SA_Parkinson_Volatility_Lag_2", "ROMI3.SA_Parkinson_Volatility_Lag_3", "ROMI3.SA_Parkinson_Volatility_Lag_4", "ROMI3.SA_Parkinson_Volatility_Lag_5", "ROMI3.SA_EMV_Lag_1", "ROMI3.SA_EMV_Lag_2", "ROMI3.SA_EMV_Lag_3", "ROMI3.SA_EMV_Lag_4", "ROMI3.SA_EMV_Lag_5", "ROMI3.SA_EMV_MA_Lag_1", "ROMI3.SA_EMV_MA_Lag_2", "ROMI3.SA_EMV_MA_Lag_3", "ROMI3.SA_EMV_MA_Lag_4", "ROMI3.SA_EMV_MA_Lag_5", "ROMI3.SA_VO_Lag_1", "ROMI3.SA_VO_Lag_2", "ROMI3.SA_VO_Lag_3", "ROMI3.SA_VO_Lag_4", "ROMI3.SA_VO_Lag_5", "ROMI3.SA_High_Max_50_Lag_1", "ROMI3.SA_High_Max_50_Lag_2", "ROMI3.SA_High_Max_50_Lag_3", "ROMI3.SA_High_Max_50_Lag_4", "ROMI3.SA_High_Max_50_Lag_5", "ROMI3.SA_Low_Min_50_Lag_1", "ROMI3.SA_Low_Min_50_Lag_2", "ROMI3.SA_Low_Min_50_Lag_3", "ROMI3.SA_Low_Min_50_Lag_4", "ROMI3.SA_Low_Min_50_Lag_5", "ROMI3.SA_MFI_Lag_1", "ROMI3.SA_MFI_Lag_2", "ROMI3.SA_MFI_Lag_3", "ROMI3.SA_MFI_Lag_4", "ROMI3.SA_MFI_Lag_5", "ROMI3.SA_Amihud_Lag_1", "ROMI3.SA_Amihud_Lag_2", "ROMI3.SA_Amihud_Lag_3", "ROMI3.SA_Amihud_Lag_4", "ROMI3.SA_Amihud_Lag_5", "ROMI3.SA_Roll_Spread_Lag_1", "ROMI3.SA_Roll_Spread_Lag_2", "ROMI3.SA_Roll_Spread_Lag_3", "ROMI3.SA_Roll_Spread_Lag_4", "ROMI3.SA_Roll_Spread_Lag_5", "ROMI3.SA_Hurst_Lag_1", "ROMI3.SA_Hurst_Lag_2", "ROMI3.SA_Hurst_Lag_3", "ROMI3.SA_Hurst_Lag_4", "ROMI3.<PERSON>_<PERSON><PERSON>_Lag_5", "ROMI3.SA_Vol_per_Volume_Lag_1", "ROMI3.SA_Vol_per_Volume_Lag_2", "ROMI3.SA_Vol_per_Volume_Lag_3", "ROMI3.SA_Vol_per_Volume_Lag_4", "ROMI3.SA_Vol_per_Volume_Lag_5", "ROMI3.SA_CMF_Lag_1", "ROMI3.SA_CMF_Lag_2", "ROMI3.SA_CMF_Lag_3", "ROMI3.SA_CMF_Lag_4", "ROMI3.SA_CMF_Lag_5", "ROMI3.SA_AD_Line_Lag_1", "ROMI3.SA_AD_Line_Lag_2", "ROMI3.SA_AD_Line_Lag_3", "ROMI3.SA_AD_Line_Lag_4", "ROMI3.SA_AD_Line_Lag_5", "VULC3.SA_Open", "VULC3.SA_High", "VULC3.SA_Low", "VULC3.SA_Close", "VULC3.SA_Volume", "VULC3.SA_Media_OHLC", "VULC3.SA_MM10", "VULC3.SA_MM25", "VULC3.SA_MM100", "VULC3.SA_Media_OHLC_PctChange", "VULC3.SA_Open_PctChange", "VULC3.SA_High_PctChange", "VULC3.SA_Low_PctChange", "VULC3.SA_Close_PctChange", "VULC3.SA_Close_PctChange_Historical", "VULC3.SA_Volatilidade", "VULC3.SA_Spread", "VULC3.SA_Parkinson_Volatility", "VULC3.SA_MFI", "VULC3.SA_MFI_Historical", "VULC3.SA_EMV", "VULC3.SA_EMV_MA", "VULC3.SA_Amihud", "VULC3.SA_Amihud_Historical", "VULC3.SA_Roll_Spread", "VULC3.SA_Roll_Spread_Historical", "VULC3.SA_Hurst", "VULC3.SA_<PERSON><PERSON>_Historical", "VULC3.SA_Vol_per_Volume", "VULC3.SA_Vol_per_Volume_Historical", "VULC3.SA_CMF", "VULC3.SA_CMF_Historical", "VULC3.SA_AD_Line", "VULC3.SA_AD_Line_Historical", "VULC3.SA_VO", "VULC3.SA_High_Max_50", "VULC3.SA_Low_Min_50", "VULC3.SA_Segunda", "VULC3.SA_Terca", "VULC3.SA_Quarta", "VULC3.SA_Quinta", "VULC3.SA_Sexta", "VULC3.SA_Mes_1", "VULC3.SA_Mes_2", "VULC3.SA_Mes_3", "VULC3.SA_Mes_4", "VULC3.SA_Mes_5", "VULC3.SA_Mes_6", "VULC3.SA_Mes_7", "VULC3.SA_Mes_8", "VULC3.SA_Mes_9", "VULC3.SA_Mes_10", "VULC3.SA_Mes_11", "VULC3.SA_Mes_12", "VULC3.SA_Quarter_1", "VULC3.SA_Quarter_2", "VULC3.SA_Quarter_3", "VULC3.SA_Quarter_4", "VULC3.SA_Last_Day_Quarter", "VULC3.SA_Pre_Feriado_Brasil", "VULC3.SA_Media_OHLC_Anterior", "VULC3.SA_Sinal_Compra", "VULC3.SA_Sinal_Venda", "VULC3.SA_Media_OHLC_Futura", "VULC3.SA_Open_PctChange_Lag_1", "VULC3.SA_High_PctChange_Lag_1", "VULC3.SA_Low_PctChange_Lag_1", "VULC3.SA_Close_PctChange_Lag_1", "VULC3.SA_Open_PctChange_Lag_2", "VULC3.SA_High_PctChange_Lag_2", "VULC3.SA_Low_PctChange_Lag_2", "VULC3.SA_Close_PctChange_Lag_2", "VULC3.SA_Open_PctChange_Lag_3", "VULC3.SA_High_PctChange_Lag_3", "VULC3.SA_Low_PctChange_Lag_3", "VULC3.SA_Close_PctChange_Lag_3", "VULC3.SA_Open_PctChange_Lag_4", "VULC3.SA_High_PctChange_Lag_4", "VULC3.SA_Low_PctChange_Lag_4", "VULC3.SA_Close_PctChange_Lag_4", "VULC3.SA_Open_PctChange_Lag_5", "VULC3.SA_High_PctChange_Lag_5", "VULC3.SA_Low_PctChange_Lag_5", "VULC3.SA_Close_PctChange_Lag_5", "VULC3.SA_Open_PctChange_Lag_6", "VULC3.SA_High_PctChange_Lag_6", "VULC3.SA_Low_PctChange_Lag_6", "VULC3.SA_Close_PctChange_Lag_6", "VULC3.SA_Open_PctChange_Lag_7", "VULC3.SA_High_PctChange_Lag_7", "VULC3.SA_Low_PctChange_Lag_7", "VULC3.SA_Close_PctChange_Lag_7", "VULC3.SA_Open_PctChange_Lag_8", "VULC3.SA_High_PctChange_Lag_8", "VULC3.SA_Low_PctChange_Lag_8", "VULC3.SA_Close_PctChange_Lag_8", "VULC3.SA_Open_PctChange_Lag_9", "VULC3.SA_High_PctChange_Lag_9", "VULC3.SA_Low_PctChange_Lag_9", "VULC3.SA_Close_PctChange_Lag_9", "VULC3.SA_Open_PctChange_Lag_10", "VULC3.SA_High_PctChange_Lag_10", "VULC3.SA_Low_PctChange_Lag_10", "VULC3.SA_Close_PctChange_Lag_10", "VULC3.SA_MM10_PctChange", "VULC3.SA_Diff_OHLC_MM10", "VULC3.SA_MM25_PctChange", "VULC3.SA_Diff_OHLC_MM25", "VULC3.SA_MM100_PctChange", "VULC3.SA_Diff_OHLC_MM100", "VULC3.SA_MM10_Menos_MM25", "VULC3.SA_MM25_Menos_MM100", "VULC3.SA_MM10_Menos_MM100", "VULC3.SA_Volume_Lag_1", "VULC3.SA_Volume_Lag_2", "VULC3.SA_Volume_Lag_3", "VULC3.SA_Volume_Lag_4", "VULC3.SA_Volume_Lag_5", "VULC3.SA_Spread_Lag_1", "VULC3.SA_Spread_Lag_2", "VULC3.SA_Spread_Lag_3", "VULC3.SA_Spread_Lag_4", "VULC3.SA_Spread_Lag_5", "VULC3.SA_Volatilidade_Lag_1", "VULC3.SA_Volatilidade_Lag_2", "VULC3.SA_Volatilidade_Lag_3", "VULC3.SA_Volatilidade_Lag_4", "VULC3.SA_Volatilidade_Lag_5", "VULC3.SA_Parkinson_Volatility_Lag_1", "VULC3.SA_Parkinson_Volatility_Lag_2", "VULC3.SA_Parkinson_Volatility_Lag_3", "VULC3.SA_Parkinson_Volatility_Lag_4", "VULC3.SA_Parkinson_Volatility_Lag_5", "VULC3.SA_EMV_Lag_1", "VULC3.SA_EMV_Lag_2", "VULC3.SA_EMV_Lag_3", "VULC3.SA_EMV_Lag_4", "VULC3.SA_EMV_Lag_5", "VULC3.SA_EMV_MA_Lag_1", "VULC3.SA_EMV_MA_Lag_2", "VULC3.SA_EMV_MA_Lag_3", "VULC3.SA_EMV_MA_Lag_4", "VULC3.SA_EMV_MA_Lag_5", "VULC3.SA_VO_Lag_1", "VULC3.SA_VO_Lag_2", "VULC3.SA_VO_Lag_3", "VULC3.SA_VO_Lag_4", "VULC3.SA_VO_Lag_5", "VULC3.SA_High_Max_50_Lag_1", "VULC3.SA_High_Max_50_Lag_2", "VULC3.SA_High_Max_50_Lag_3", "VULC3.SA_High_Max_50_Lag_4", "VULC3.SA_High_Max_50_Lag_5", "VULC3.SA_Low_Min_50_Lag_1", "VULC3.SA_Low_Min_50_Lag_2", "VULC3.SA_Low_Min_50_Lag_3", "VULC3.SA_Low_Min_50_Lag_4", "VULC3.SA_Low_Min_50_Lag_5", "VULC3.SA_MFI_Lag_1", "VULC3.SA_MFI_Lag_2", "VULC3.SA_MFI_Lag_3", "VULC3.SA_MFI_Lag_4", "VULC3.SA_MFI_Lag_5", "VULC3.SA_Amihud_Lag_1", "VULC3.SA_Amihud_Lag_2", "VULC3.SA_Amihud_Lag_3", "VULC3.SA_Amihud_Lag_4", "VULC3.SA_Amihud_Lag_5", "VULC3.SA_Roll_Spread_Lag_1", "VULC3.SA_Roll_Spread_Lag_2", "VULC3.SA_Roll_Spread_Lag_3", "VULC3.SA_Roll_Spread_Lag_4", "VULC3.SA_Roll_Spread_Lag_5", "VULC3.SA_Hurst_Lag_1", "VULC3.<PERSON>_Hurst_Lag_2", "VULC3.SA_Hurst_Lag_3", "VULC3.<PERSON>_<PERSON>rst_Lag_4", "VULC3.<PERSON>_<PERSON><PERSON>_Lag_5", "VULC3.SA_Vol_per_Volume_Lag_1", "VULC3.SA_Vol_per_Volume_Lag_2", "VULC3.SA_Vol_per_Volume_Lag_3", "VULC3.SA_Vol_per_Volume_Lag_4", "VULC3.SA_Vol_per_Volume_Lag_5", "VULC3.SA_CMF_Lag_1", "VULC3.SA_CMF_Lag_2", "VULC3.SA_CMF_Lag_3", "VULC3.SA_CMF_Lag_4", "VULC3.SA_CMF_Lag_5", "VULC3.SA_AD_Line_Lag_1", "VULC3.SA_AD_Line_Lag_2", "VULC3.SA_AD_Line_Lag_3", "VULC3.SA_AD_Line_Lag_4", "VULC3.SA_AD_Line_Lag_5", "CAML3.SA_Close", "CAML3.SA_High", "CAML3.SA_Low", "CAML3.SA_Open", "CAML3.SA_Volume", "CAML3.SA_Media_OHLC", "CAML3.SA_MM10", "CAML3.SA_MM25", "CAML3.SA_MM100", "CAML3.SA_Media_OHLC_PctChange", "CAML3.SA_Open_PctChange", "CAML3.SA_High_PctChange", "CAML3.SA_Low_PctChange", "CAML3.SA_Close_PctChange", "CAML3.SA_Close_PctChange_Historical", "CAML3.SA_Volatilidade", "CAML3.SA_Spread", "CAML3.SA_Parkinson_Volatility", "CAML3.SA_MFI", "CAML3.SA_MFI_Historical", "CAML3.SA_EMV", "CAML3.SA_EMV_MA", "CAML3.SA_Amihud", "CAML3.SA_Amihud_Historical", "CAML3.SA_Roll_Spread", "CAML3.SA_Roll_Spread_Historical", "CAML3.SA_Hurst", "CAML3.<PERSON>_<PERSON><PERSON>_Historical", "CAML3.SA_Vol_per_Volume", "CAML3.SA_Vol_per_Volume_Historical", "CAML3.SA_CMF", "CAML3.SA_CMF_Historical", "CAML3.SA_AD_Line", "CAML3.SA_AD_Line_Historical", "CAML3.SA_VO", "CAML3.SA_High_Max_50", "CAML3.SA_Low_Min_50", "CAML3.SA_Segunda", "CAML3.SA_Terca", "CAML3.SA_Quarta", "CAML3.SA_Quinta", "CAML3.SA_Sexta", "CAML3.SA_Mes_1", "CAML3.SA_Mes_2", "CAML3.SA_Mes_3", "CAML3.SA_Mes_4", "CAML3.SA_Mes_5", "CAML3.SA_Mes_6", "CAML3.SA_Mes_7", "CAML3.SA_Mes_8", "CAML3.SA_Mes_9", "CAML3.SA_Mes_10", "CAML3.SA_Mes_11", "CAML3.SA_Mes_12", "CAML3.SA_Quarter_1", "CAML3.SA_Quarter_2", "CAML3.SA_Quarter_3", "CAML3.SA_Quarter_4", "CAML3.SA_Last_Day_Quarter", "CAML3.SA_Pre_Feriado_Brasil", "CAML3.SA_Media_OHLC_Anterior", "CAML3.SA_Sinal_Compra", "CAML3.SA_Sinal_Venda", "CAML3.SA_Media_OHLC_Futura", "CAML3.SA_Open_PctChange_Lag_1", "CAML3.SA_High_PctChange_Lag_1", "CAML3.SA_Low_PctChange_Lag_1", "CAML3.SA_Close_PctChange_Lag_1", "CAML3.SA_Open_PctChange_Lag_2", "CAML3.SA_High_PctChange_Lag_2", "CAML3.SA_Low_PctChange_Lag_2", "CAML3.SA_Close_PctChange_Lag_2", "CAML3.SA_Open_PctChange_Lag_3", "CAML3.SA_High_PctChange_Lag_3", "CAML3.SA_Low_PctChange_Lag_3", "CAML3.SA_Close_PctChange_Lag_3", "CAML3.SA_Open_PctChange_Lag_4", "CAML3.SA_High_PctChange_Lag_4", "CAML3.SA_Low_PctChange_Lag_4", "CAML3.SA_Close_PctChange_Lag_4", "CAML3.SA_Open_PctChange_Lag_5", "CAML3.SA_High_PctChange_Lag_5", "CAML3.SA_Low_PctChange_Lag_5", "CAML3.SA_Close_PctChange_Lag_5", "CAML3.SA_Open_PctChange_Lag_6", "CAML3.SA_High_PctChange_Lag_6", "CAML3.SA_Low_PctChange_Lag_6", "CAML3.SA_Close_PctChange_Lag_6", "CAML3.SA_Open_PctChange_Lag_7", "CAML3.SA_High_PctChange_Lag_7", "CAML3.SA_Low_PctChange_Lag_7", "CAML3.SA_Close_PctChange_Lag_7", "CAML3.SA_Open_PctChange_Lag_8", "CAML3.SA_High_PctChange_Lag_8", "CAML3.SA_Low_PctChange_Lag_8", "CAML3.SA_Close_PctChange_Lag_8", "CAML3.SA_Open_PctChange_Lag_9", "CAML3.SA_High_PctChange_Lag_9", "CAML3.SA_Low_PctChange_Lag_9", "CAML3.SA_Close_PctChange_Lag_9", "CAML3.SA_Open_PctChange_Lag_10", "CAML3.SA_High_PctChange_Lag_10", "CAML3.SA_Low_PctChange_Lag_10", "CAML3.SA_Close_PctChange_Lag_10", "CAML3.SA_MM10_PctChange", "CAML3.SA_Diff_OHLC_MM10", "CAML3.SA_MM25_PctChange", "CAML3.SA_Diff_OHLC_MM25", "CAML3.SA_MM100_PctChange", "CAML3.SA_Diff_OHLC_MM100", "CAML3.SA_MM10_Menos_MM25", "CAML3.SA_MM25_Menos_MM100", "CAML3.SA_MM10_Menos_MM100", "CAML3.SA_Volume_Lag_1", "CAML3.SA_Volume_Lag_2", "CAML3.SA_Volume_Lag_3", "CAML3.SA_Volume_Lag_4", "CAML3.SA_Volume_Lag_5", "CAML3.SA_Spread_Lag_1", "CAML3.SA_Spread_Lag_2", "CAML3.SA_Spread_Lag_3", "CAML3.SA_Spread_Lag_4", "CAML3.SA_Spread_Lag_5", "CAML3.SA_Volatilidade_Lag_1", "CAML3.SA_Volatilidade_Lag_2", "CAML3.SA_Volatilidade_Lag_3", "CAML3.SA_Volatilidade_Lag_4", "CAML3.SA_Volatilidade_Lag_5", "CAML3.SA_Parkinson_Volatility_Lag_1", "CAML3.SA_Parkinson_Volatility_Lag_2", "CAML3.SA_Parkinson_Volatility_Lag_3", "CAML3.SA_Parkinson_Volatility_Lag_4", "CAML3.SA_Parkinson_Volatility_Lag_5", "CAML3.SA_EMV_Lag_1", "CAML3.SA_EMV_Lag_2", "CAML3.SA_EMV_Lag_3", "CAML3.SA_EMV_Lag_4", "CAML3.SA_EMV_Lag_5", "CAML3.SA_EMV_MA_Lag_1", "CAML3.SA_EMV_MA_Lag_2", "CAML3.SA_EMV_MA_Lag_3", "CAML3.SA_EMV_MA_Lag_4", "CAML3.SA_EMV_MA_Lag_5", "CAML3.SA_VO_Lag_1", "CAML3.SA_VO_Lag_2", "CAML3.SA_VO_Lag_3", "CAML3.SA_VO_Lag_4", "CAML3.SA_VO_Lag_5", "CAML3.SA_High_Max_50_Lag_1", "CAML3.SA_High_Max_50_Lag_2", "CAML3.SA_High_Max_50_Lag_3", "CAML3.SA_High_Max_50_Lag_4", "CAML3.SA_High_Max_50_Lag_5", "CAML3.SA_Low_Min_50_Lag_1", "CAML3.SA_Low_Min_50_Lag_2", "CAML3.SA_Low_Min_50_Lag_3", "CAML3.SA_Low_Min_50_Lag_4", "CAML3.SA_Low_Min_50_Lag_5", "CAML3.SA_MFI_Lag_1", "CAML3.SA_MFI_Lag_2", "CAML3.SA_MFI_Lag_3", "CAML3.SA_MFI_Lag_4", "CAML3.SA_MFI_Lag_5", "CAML3.SA_Amihud_Lag_1", "CAML3.SA_Amihud_Lag_2", "CAML3.SA_Amihud_Lag_3", "CAML3.SA_Amihud_Lag_4", "CAML3.SA_Amihud_Lag_5", "CAML3.SA_Roll_Spread_Lag_1", "CAML3.SA_Roll_Spread_Lag_2", "CAML3.SA_Roll_Spread_Lag_3", "CAML3.SA_Roll_Spread_Lag_4", "CAML3.SA_Roll_Spread_Lag_5", "CAML3.SA_Hurst_Lag_1", "CAML3.<PERSON>_<PERSON><PERSON>_Lag_2", "CAML3.SA_<PERSON>rst_Lag_3", "CAML3.<PERSON>_<PERSON><PERSON>_Lag_4", "CAML3.<PERSON>_<PERSON><PERSON>_Lag_5", "CAML3.SA_Vol_per_Volume_Lag_1", "CAML3.SA_Vol_per_Volume_Lag_2", "CAML3.SA_Vol_per_Volume_Lag_3", "CAML3.SA_Vol_per_Volume_Lag_4", "CAML3.SA_Vol_per_Volume_Lag_5", "CAML3.SA_CMF_Lag_1", "CAML3.SA_CMF_Lag_2", "CAML3.SA_CMF_Lag_3", "CAML3.SA_CMF_Lag_4", "CAML3.SA_CMF_Lag_5", "CAML3.SA_AD_Line_Lag_1", "CAML3.SA_AD_Line_Lag_2", "CAML3.SA_AD_Line_Lag_3", "CAML3.SA_AD_Line_Lag_4", "CAML3.SA_AD_Line_Lag_5", "RDOR3.SA_Close", "RDOR3.SA_High", "RDOR3.SA_Low", "RDOR3.SA_Open", "RDOR3.SA_Volume", "RDOR3.SA_Media_OHLC", "RDOR3.SA_MM10", "RDOR3.SA_MM25", "RDOR3.SA_MM100", "RDOR3.SA_Media_OHLC_PctChange", "RDOR3.SA_Open_PctChange", "RDOR3.SA_High_PctChange", "RDOR3.SA_Low_PctChange", "RDOR3.SA_Close_PctChange", "RDOR3.SA_Close_PctChange_Historical", "RDOR3.SA_Volatilidade", "RDOR3.SA_Spread", "RDOR3.SA_Parkinson_Volatility", "RDOR3.SA_MFI", "RDOR3.SA_MFI_Historical", "RDOR3.SA_EMV", "RDOR3.SA_EMV_MA", "RDOR3.SA_Amihud", "RDOR3.SA_Amihud_Historical", "RDOR3.SA_Roll_Spread", "RDOR3.SA_Roll_Spread_Historical", "RDOR3.SA_Hurst", "RDOR3.<PERSON>_<PERSON><PERSON>_Historical", "RDOR3.SA_Vol_per_Volume", "RDOR3.SA_Vol_per_Volume_Historical", "RDOR3.SA_CMF", "RDOR3.SA_CMF_Historical", "RDOR3.SA_AD_Line", "RDOR3.SA_AD_Line_Historical", "RDOR3.SA_VO", "RDOR3.SA_High_Max_50", "RDOR3.SA_Low_Min_50", "RDOR3.SA_Segunda", "RDOR3.SA_Terca", "RDOR3.SA_Quarta", "RDOR3.SA_Quinta", "RDOR3.SA_Sexta", "RDOR3.SA_Mes_1", "RDOR3.SA_Mes_2", "RDOR3.SA_Mes_3", "RDOR3.SA_Mes_4", "RDOR3.SA_Mes_5", "RDOR3.SA_Mes_6", "RDOR3.SA_Mes_7", "RDOR3.SA_Mes_8", "RDOR3.SA_Mes_9", "RDOR3.SA_Mes_10", "RDOR3.SA_Mes_11", "RDOR3.SA_Mes_12", "RDOR3.SA_Quarter_1", "RDOR3.SA_Quarter_2", "RDOR3.SA_Quarter_3", "RDOR3.SA_Quarter_4", "RDOR3.SA_Last_Day_Quarter", "RDOR3.SA_Pre_Feriado_Brasil", "RDOR3.SA_Media_OHLC_Anterior", "RDOR3.SA_Sinal_Compra", "RDOR3.SA_Sinal_Venda", "RDOR3.SA_Media_OHLC_Futura", "RDOR3.SA_Open_PctChange_Lag_1", "RDOR3.SA_High_PctChange_Lag_1", "RDOR3.SA_Low_PctChange_Lag_1", "RDOR3.SA_Close_PctChange_Lag_1", "RDOR3.SA_Open_PctChange_Lag_2", "RDOR3.SA_High_PctChange_Lag_2", "RDOR3.SA_Low_PctChange_Lag_2", "RDOR3.SA_Close_PctChange_Lag_2", "RDOR3.SA_Open_PctChange_Lag_3", "RDOR3.SA_High_PctChange_Lag_3", "RDOR3.SA_Low_PctChange_Lag_3", "RDOR3.SA_Close_PctChange_Lag_3", "RDOR3.SA_Open_PctChange_Lag_4", "RDOR3.SA_High_PctChange_Lag_4", "RDOR3.SA_Low_PctChange_Lag_4", "RDOR3.SA_Close_PctChange_Lag_4", "RDOR3.SA_Open_PctChange_Lag_5", "RDOR3.SA_High_PctChange_Lag_5", "RDOR3.SA_Low_PctChange_Lag_5", "RDOR3.SA_Close_PctChange_Lag_5", "RDOR3.SA_Open_PctChange_Lag_6", "RDOR3.SA_High_PctChange_Lag_6", "RDOR3.SA_Low_PctChange_Lag_6", "RDOR3.SA_Close_PctChange_Lag_6", "RDOR3.SA_Open_PctChange_Lag_7", "RDOR3.SA_High_PctChange_Lag_7", "RDOR3.SA_Low_PctChange_Lag_7", "RDOR3.SA_Close_PctChange_Lag_7", "RDOR3.SA_Open_PctChange_Lag_8", "RDOR3.SA_High_PctChange_Lag_8", "RDOR3.SA_Low_PctChange_Lag_8", "RDOR3.SA_Close_PctChange_Lag_8", "RDOR3.SA_Open_PctChange_Lag_9", "RDOR3.SA_High_PctChange_Lag_9", "RDOR3.SA_Low_PctChange_Lag_9", "RDOR3.SA_Close_PctChange_Lag_9", "RDOR3.SA_Open_PctChange_Lag_10", "RDOR3.SA_High_PctChange_Lag_10", "RDOR3.SA_Low_PctChange_Lag_10", "RDOR3.SA_Close_PctChange_Lag_10", "RDOR3.SA_MM10_PctChange", "RDOR3.SA_Diff_OHLC_MM10", "RDOR3.SA_MM25_PctChange", "RDOR3.SA_Diff_OHLC_MM25", "RDOR3.SA_MM100_PctChange", "RDOR3.SA_Diff_OHLC_MM100", "RDOR3.SA_MM10_Menos_MM25", "RDOR3.SA_MM25_Menos_MM100", "RDOR3.SA_MM10_Menos_MM100", "RDOR3.SA_Volume_Lag_1", "RDOR3.SA_Volume_Lag_2", "RDOR3.SA_Volume_Lag_3", "RDOR3.SA_Volume_Lag_4", "RDOR3.SA_Volume_Lag_5", "RDOR3.SA_Spread_Lag_1", "RDOR3.SA_Spread_Lag_2", "RDOR3.SA_Spread_Lag_3", "RDOR3.SA_Spread_Lag_4", "RDOR3.SA_Spread_Lag_5", "RDOR3.SA_Volatilidade_Lag_1", "RDOR3.SA_Volatilidade_Lag_2", "RDOR3.SA_Volatilidade_Lag_3", "RDOR3.SA_Volatilidade_Lag_4", "RDOR3.SA_Volatilidade_Lag_5", "RDOR3.SA_Parkinson_Volatility_Lag_1", "RDOR3.<PERSON>_Parkinson_Volatility_Lag_2", "RDOR3.<PERSON>_Parkinson_Volatility_Lag_3", "RDOR3.<PERSON>_Parkinson_Volatility_Lag_4", "RDOR3.<PERSON>_Parkinson_Volatility_Lag_5", "RDOR3.SA_EMV_Lag_1", "RDOR3.SA_EMV_Lag_2", "RDOR3.SA_EMV_Lag_3", "RDOR3.SA_EMV_Lag_4", "RDOR3.SA_EMV_Lag_5", "RDOR3.SA_EMV_MA_Lag_1", "RDOR3.SA_EMV_MA_Lag_2", "RDOR3.SA_EMV_MA_Lag_3", "RDOR3.SA_EMV_MA_Lag_4", "RDOR3.SA_EMV_MA_Lag_5", "RDOR3.SA_VO_Lag_1", "RDOR3.SA_VO_Lag_2", "RDOR3.SA_VO_Lag_3", "RDOR3.SA_VO_Lag_4", "RDOR3.SA_VO_Lag_5", "RDOR3.SA_High_Max_50_Lag_1", "RDOR3.SA_High_Max_50_Lag_2", "RDOR3.SA_High_Max_50_Lag_3", "RDOR3.SA_High_Max_50_Lag_4", "RDOR3.SA_High_Max_50_Lag_5", "RDOR3.SA_Low_Min_50_Lag_1", "RDOR3.SA_Low_Min_50_Lag_2", "RDOR3.SA_Low_Min_50_Lag_3", "RDOR3.SA_Low_Min_50_Lag_4", "RDOR3.SA_Low_Min_50_Lag_5", "RDOR3.SA_MFI_Lag_1", "RDOR3.SA_MFI_Lag_2", "RDOR3.SA_MFI_Lag_3", "RDOR3.SA_MFI_Lag_4", "RDOR3.SA_MFI_Lag_5", "RDOR3.SA_Amihud_Lag_1", "RDOR3.SA_Amihud_Lag_2", "RDOR3.SA_Amihud_Lag_3", "RDOR3.SA_Amihud_Lag_4", "RDOR3.SA_Amihud_Lag_5", "RDOR3.SA_Roll_Spread_Lag_1", "RDOR3.SA_Roll_Spread_Lag_2", "RDOR3.SA_Roll_Spread_Lag_3", "RDOR3.SA_Roll_Spread_Lag_4", "RDOR3.SA_Roll_Spread_Lag_5", "RDOR3.<PERSON>_Hurst_Lag_1", "RDOR3.<PERSON>_<PERSON><PERSON>_Lag_2", "RDOR3.<PERSON>_<PERSON><PERSON>_Lag_3", "RDOR3.<PERSON>_<PERSON><PERSON>_Lag_4", "RDOR3.<PERSON>_<PERSON><PERSON>_Lag_5", "RDOR3.SA_Vol_per_Volume_Lag_1", "RDOR3.SA_Vol_per_Volume_Lag_2", "RDOR3.SA_Vol_per_Volume_Lag_3", "RDOR3.SA_Vol_per_Volume_Lag_4", "RDOR3.SA_Vol_per_Volume_Lag_5", "RDOR3.SA_CMF_Lag_1", "RDOR3.SA_CMF_Lag_2", "RDOR3.SA_CMF_Lag_3", "RDOR3.SA_CMF_Lag_4", "RDOR3.SA_CMF_Lag_5", "RDOR3.SA_AD_Line_Lag_1", "RDOR3.SA_AD_Line_Lag_2", "RDOR3.SA_AD_Line_Lag_3", "RDOR3.SA_AD_Line_Lag_4", "RDOR3.SA_AD_Line_Lag_5", "EZTC3.SA_Open", "EZTC3.SA_High", "EZTC3.SA_Low", "EZTC3.SA_Close", "EZTC3.SA_Volume", "EZTC3.SA_Media_OHLC", "EZTC3.SA_MM10", "EZTC3.SA_MM25", "EZTC3.SA_MM100", "EZTC3.SA_Media_OHLC_PctChange", "EZTC3.SA_Open_PctChange", "EZTC3.SA_High_PctChange", "EZTC3.SA_Low_PctChange", "EZTC3.SA_Close_PctChange", "EZTC3.SA_Close_PctChange_Historical", "EZTC3.SA_Volatilidade", "EZTC3.SA_Spread", "EZTC3.SA_Parkinson_Volatility", "EZTC3.SA_MFI", "EZTC3.SA_MFI_Historical", "EZTC3.SA_EMV", "EZTC3.SA_EMV_MA", "EZTC3.SA_Amihud", "EZTC3.SA_Amihud_Historical", "EZTC3.SA_Roll_Spread", "EZTC3.SA_Roll_Spread_Historical", "EZTC3.SA_Hurst", "EZTC3.SA_<PERSON>rst_Historical", "EZTC3.SA_Vol_per_Volume", "EZTC3.SA_Vol_per_Volume_Historical", "EZTC3.SA_CMF", "EZTC3.SA_CMF_Historical", "EZTC3.SA_AD_Line", "EZTC3.SA_AD_Line_Historical", "EZTC3.SA_VO", "EZTC3.SA_High_Max_50", "EZTC3.SA_Low_Min_50", "EZTC3.SA_Segunda", "EZTC3.SA_Terca", "EZTC3.SA_Quarta", "EZTC3.SA_Quinta", "EZTC3.SA_Sexta", "EZTC3.SA_Mes_1", "EZTC3.SA_Mes_2", "EZTC3.SA_Mes_3", "EZTC3.SA_Mes_4", "EZTC3.SA_Mes_5", "EZTC3.SA_Mes_6", "EZTC3.SA_Mes_7", "EZTC3.SA_Mes_8", "EZTC3.SA_Mes_9", "EZTC3.SA_Mes_10", "EZTC3.SA_Mes_11", "EZTC3.SA_Mes_12", "EZTC3.SA_Quarter_1", "EZTC3.SA_Quarter_2", "EZTC3.SA_Quarter_3", "EZTC3.SA_Quarter_4", "EZTC3.SA_Last_Day_Quarter", "EZTC3.SA_Pre_Feriado_Brasil", "EZTC3.SA_Media_OHLC_Anterior", "EZTC3.SA_Sinal_Compra", "EZTC3.SA_Sinal_Venda", "EZTC3.SA_Media_OHLC_Futura", "EZTC3.SA_Open_PctChange_Lag_1", "EZTC3.SA_High_PctChange_Lag_1", "EZTC3.SA_Low_PctChange_Lag_1", "EZTC3.SA_Close_PctChange_Lag_1", "EZTC3.SA_Open_PctChange_Lag_2", "EZTC3.SA_High_PctChange_Lag_2", "EZTC3.SA_Low_PctChange_Lag_2", "EZTC3.SA_Close_PctChange_Lag_2", "EZTC3.SA_Open_PctChange_Lag_3", "EZTC3.SA_High_PctChange_Lag_3", "EZTC3.SA_Low_PctChange_Lag_3", "EZTC3.SA_Close_PctChange_Lag_3", "EZTC3.SA_Open_PctChange_Lag_4", "EZTC3.SA_High_PctChange_Lag_4", "EZTC3.SA_Low_PctChange_Lag_4", "EZTC3.SA_Close_PctChange_Lag_4", "EZTC3.SA_Open_PctChange_Lag_5", "EZTC3.SA_High_PctChange_Lag_5", "EZTC3.SA_Low_PctChange_Lag_5", "EZTC3.SA_Close_PctChange_Lag_5", "EZTC3.SA_Open_PctChange_Lag_6", "EZTC3.SA_High_PctChange_Lag_6", "EZTC3.SA_Low_PctChange_Lag_6", "EZTC3.SA_Close_PctChange_Lag_6", "EZTC3.SA_Open_PctChange_Lag_7", "EZTC3.SA_High_PctChange_Lag_7", "EZTC3.SA_Low_PctChange_Lag_7", "EZTC3.SA_Close_PctChange_Lag_7", "EZTC3.SA_Open_PctChange_Lag_8", "EZTC3.SA_High_PctChange_Lag_8", "EZTC3.SA_Low_PctChange_Lag_8", "EZTC3.SA_Close_PctChange_Lag_8", "EZTC3.SA_Open_PctChange_Lag_9", "EZTC3.SA_High_PctChange_Lag_9", "EZTC3.SA_Low_PctChange_Lag_9", "EZTC3.SA_Close_PctChange_Lag_9", "EZTC3.SA_Open_PctChange_Lag_10", "EZTC3.SA_High_PctChange_Lag_10", "EZTC3.SA_Low_PctChange_Lag_10", "EZTC3.SA_Close_PctChange_Lag_10", "EZTC3.SA_MM10_PctChange", "EZTC3.SA_Diff_OHLC_MM10", "EZTC3.SA_MM25_PctChange", "EZTC3.SA_Diff_OHLC_MM25", "EZTC3.SA_MM100_PctChange", "EZTC3.SA_Diff_OHLC_MM100", "EZTC3.SA_MM10_Menos_MM25", "EZTC3.SA_MM25_Menos_MM100", "EZTC3.SA_MM10_Menos_MM100", "EZTC3.SA_Volume_Lag_1", "EZTC3.SA_Volume_Lag_2", "EZTC3.SA_Volume_Lag_3", "EZTC3.SA_Volume_Lag_4", "EZTC3.SA_Volume_Lag_5", "EZTC3.SA_Spread_Lag_1", "EZTC3.SA_Spread_Lag_2", "EZTC3.SA_Spread_Lag_3", "EZTC3.SA_Spread_Lag_4", "EZTC3.SA_Spread_Lag_5", "EZTC3.SA_Volatilidade_Lag_1", "EZTC3.SA_Volatilidade_Lag_2", "EZTC3.SA_Volatilidade_Lag_3", "EZTC3.SA_Volatilidade_Lag_4", "EZTC3.SA_Volatilidade_Lag_5", "EZTC3.SA_Parkinson_Volatility_Lag_1", "EZTC3.SA_Parkinson_Volatility_Lag_2", "EZTC3.SA_Parkinson_Volatility_Lag_3", "EZTC3.SA_Parkinson_Volatility_Lag_4", "EZTC3.SA_Parkinson_Volatility_Lag_5", "EZTC3.SA_EMV_Lag_1", "EZTC3.SA_EMV_Lag_2", "EZTC3.SA_EMV_Lag_3", "EZTC3.SA_EMV_Lag_4", "EZTC3.SA_EMV_Lag_5", "EZTC3.SA_EMV_MA_Lag_1", "EZTC3.SA_EMV_MA_Lag_2", "EZTC3.SA_EMV_MA_Lag_3", "EZTC3.SA_EMV_MA_Lag_4", "EZTC3.SA_EMV_MA_Lag_5", "EZTC3.SA_VO_Lag_1", "EZTC3.SA_VO_Lag_2", "EZTC3.SA_VO_Lag_3", "EZTC3.SA_VO_Lag_4", "EZTC3.SA_VO_Lag_5", "EZTC3.SA_High_Max_50_Lag_1", "EZTC3.SA_High_Max_50_Lag_2", "EZTC3.SA_High_Max_50_Lag_3", "EZTC3.SA_High_Max_50_Lag_4", "EZTC3.SA_High_Max_50_Lag_5", "EZTC3.SA_Low_Min_50_Lag_1", "EZTC3.SA_Low_Min_50_Lag_2", "EZTC3.SA_Low_Min_50_Lag_3", "EZTC3.SA_Low_Min_50_Lag_4", "EZTC3.SA_Low_Min_50_Lag_5", "EZTC3.SA_MFI_Lag_1", "EZTC3.SA_MFI_Lag_2", "EZTC3.SA_MFI_Lag_3", "EZTC3.SA_MFI_Lag_4", "EZTC3.SA_MFI_Lag_5", "EZTC3.SA_Amihud_Lag_1", "EZTC3.SA_Amihud_Lag_2", "EZTC3.SA_Amihud_Lag_3", "EZTC3.SA_Amihud_Lag_4", "EZTC3.SA_Amihud_Lag_5", "EZTC3.SA_Roll_Spread_Lag_1", "EZTC3.SA_Roll_Spread_Lag_2", "EZTC3.SA_Roll_Spread_Lag_3", "EZTC3.SA_Roll_Spread_Lag_4", "EZTC3.SA_Roll_Spread_Lag_5", "EZTC3.SA_Hurst_Lag_1", "EZTC3.SA_Hurst_Lag_2", "EZTC3.SA_Hurst_Lag_3", "EZTC3.SA_<PERSON>rst_Lag_4", "EZTC3.<PERSON>_<PERSON><PERSON>_Lag_5", "EZTC3.SA_Vol_per_Volume_Lag_1", "EZTC3.SA_Vol_per_Volume_Lag_2", "EZTC3.SA_Vol_per_Volume_Lag_3", "EZTC3.SA_Vol_per_Volume_Lag_4", "EZTC3.SA_Vol_per_Volume_Lag_5", "EZTC3.SA_CMF_Lag_1", "EZTC3.SA_CMF_Lag_2", "EZTC3.SA_CMF_Lag_3", "EZTC3.SA_CMF_Lag_4", "EZTC3.SA_CMF_Lag_5", "EZTC3.SA_AD_Line_Lag_1", "EZTC3.SA_AD_Line_Lag_2", "EZTC3.SA_AD_Line_Lag_3", "EZTC3.SA_AD_Line_Lag_4", "EZTC3.SA_AD_Line_Lag_5"], "features_cached": 3328, "has_features": true, "cached_features_list": ["STBP3.SA_Media_OHLC", "STBP3.SA_MFI_Historical", "STBP3.SA_Amihud_Historical", "STBP3.SA_Roll_Spread_Historical", "STBP3.SA_<PERSON><PERSON>_Historical", "STBP3.SA_Vol_per_Volume_Historical", "STBP3.SA_CMF_Historical", "STBP3.SA_AD_Line_Historical", "STBP3.SA_Segunda", "STBP3.SA_Terca", "STBP3.SA_Quarta", "STBP3.SA_Quinta", "STBP3.SA_Sexta", "STBP3.SA_Mes_1", "STBP3.SA_Mes_2", "STBP3.SA_Mes_3", "STBP3.SA_Mes_4", "STBP3.SA_Mes_5", "STBP3.SA_Mes_6", "STBP3.SA_Mes_7", "STBP3.SA_Mes_8", "STBP3.SA_Mes_9", "STBP3.SA_Mes_10", "STBP3.SA_Mes_11", "STBP3.SA_Mes_12", "STBP3.SA_Quarter_1", "STBP3.SA_Quarter_2", "STBP3.SA_Quarter_3", "STBP3.SA_Quarter_4", "STBP3.SA_Last_Day_Quarter", "STBP3.SA_Pre_Feriado_Brasil", "STBP3.SA_Sinal_Compra", "STBP3.SA_Sinal_Venda", "STBP3.SA_Media_OHLC_Futura", "STBP3.SA_Volume_Lag_1", "STBP3.SA_Volume_Lag_2", "STBP3.SA_Volume_Lag_3", "STBP3.SA_Volume_Lag_4", "STBP3.SA_Volume_Lag_5", "STBP3.SA_Spread_Lag_1", "STBP3.SA_Spread_Lag_2", "STBP3.SA_Spread_Lag_3", "STBP3.SA_Spread_Lag_4", "STBP3.SA_Spread_Lag_5", "STBP3.SA_Volatilidade_Lag_1", "STBP3.SA_Volatilidade_Lag_2", "STBP3.SA_Volatilidade_Lag_3", "STBP3.SA_Volatilidade_Lag_4", "STBP3.SA_Volatilidade_Lag_5", "STBP3.SA_Parkinson_Volatility_Lag_1", "STBP3.SA_Parkinson_Volatility_Lag_2", "STBP3.SA_Parkinson_Volatility_Lag_3", "STBP3.SA_Parkinson_Volatility_Lag_4", "STBP3.<PERSON>_Parkinson_Volatility_Lag_5", "STBP3.SA_EMV_Lag_1", "STBP3.SA_EMV_Lag_2", "STBP3.SA_EMV_Lag_3", "STBP3.SA_EMV_Lag_4", "STBP3.SA_EMV_Lag_5", "STBP3.SA_EMV_MA_Lag_1", "STBP3.SA_EMV_MA_Lag_2", "STBP3.SA_EMV_MA_Lag_3", "STBP3.SA_EMV_MA_Lag_4", "STBP3.SA_EMV_MA_Lag_5", "STBP3.SA_VO_Lag_1", "STBP3.SA_VO_Lag_2", "STBP3.SA_VO_Lag_3", "STBP3.SA_VO_Lag_4", "STBP3.SA_VO_Lag_5", "STBP3.SA_MFI_Lag_1", "STBP3.SA_MFI_Lag_2", "STBP3.SA_MFI_Lag_3", "STBP3.SA_MFI_Lag_4", "STBP3.SA_MFI_Lag_5", "STBP3.SA_Amihud_Lag_1", "STBP3.SA_Amihud_Lag_2", "STBP3.SA_Amihud_Lag_3", "STBP3.SA_Amihud_Lag_4", "STBP3.SA_Amihud_Lag_5", "STBP3.SA_Roll_Spread_Lag_1", "STBP3.SA_Roll_Spread_Lag_2", "STBP3.SA_Roll_Spread_Lag_3", "STBP3.SA_Roll_Spread_Lag_4", "STBP3.SA_Roll_Spread_Lag_5", "STBP3.SA_Hurst_Lag_1", "STBP3.SA_Hurst_Lag_2", "STBP3.SA_Hurst_Lag_3", "STBP3.SA_Hurst_Lag_4", "STBP3.<PERSON>_<PERSON><PERSON>_Lag_5", "STBP3.SA_Vol_per_Volume_Lag_1", "STBP3.SA_Vol_per_Volume_Lag_2", "STBP3.SA_Vol_per_Volume_Lag_3", "STBP3.SA_Vol_per_Volume_Lag_4", "STBP3.SA_Vol_per_Volume_Lag_5", "STBP3.SA_CMF_Lag_1", "STBP3.SA_CMF_Lag_2", "STBP3.SA_CMF_Lag_3", "STBP3.SA_CMF_Lag_4", "STBP3.SA_CMF_Lag_5", "STBP3.SA_AD_Line_Lag_1", "STBP3.SA_AD_Line_Lag_2", "STBP3.SA_AD_Line_Lag_3", "STBP3.SA_AD_Line_Lag_4", "STBP3.SA_AD_Line_Lag_5", "WEGE3.SA_Media_OHLC", "WEGE3.SA_MFI_Historical", "WEGE3.SA_Amihud_Historical", "WEGE3.SA_Roll_Spread_Historical", "WEGE3.<PERSON>_<PERSON><PERSON>_Historical", "WEGE3.SA_Vol_per_Volume_Historical", "WEGE3.SA_CMF_Historical", "WEGE3.SA_AD_Line_Historical", "WEGE3.SA_Segunda", "WEGE3.SA_Terca", "WEGE3.SA_Quarta", "WEGE3.SA_Quinta", "WEGE3.SA_Sexta", "WEGE3.SA_Mes_1", "WEGE3.SA_Mes_2", "WEGE3.SA_Mes_3", "WEGE3.SA_Mes_4", "WEGE3.SA_Mes_5", "WEGE3.SA_Mes_6", "WEGE3.SA_Mes_7", "WEGE3.SA_Mes_8", "WEGE3.SA_Mes_9", "WEGE3.SA_Mes_10", "WEGE3.SA_Mes_11", "WEGE3.SA_Mes_12", "WEGE3.SA_Quarter_1", "WEGE3.SA_Quarter_2", "WEGE3.SA_Quarter_3", "WEGE3.SA_Quarter_4", "WEGE3.SA_Last_Day_Quarter", "WEGE3.SA_Pre_Feriado_Brasil", "WEGE3.SA_Sinal_Compra", "WEGE3.SA_Sinal_Venda", "WEGE3.SA_Media_OHLC_Futura", "WEGE3.SA_Volume_Lag_1", "WEGE3.SA_Volume_Lag_2", "WEGE3.SA_Volume_Lag_3", "WEGE3.SA_Volume_Lag_4", "WEGE3.SA_Volume_Lag_5", "WEGE3.SA_Spread_Lag_1", "WEGE3.SA_Spread_Lag_2", "WEGE3.SA_Spread_Lag_3", "WEGE3.SA_Spread_Lag_4", "WEGE3.SA_Spread_Lag_5", "WEGE3.SA_Volatilidade_Lag_1", "WEGE3.SA_Volatilidade_Lag_2", "WEGE3.SA_Volatilidade_Lag_3", "WEGE3.SA_Volatilidade_Lag_4", "WEGE3.SA_Volatilidade_Lag_5", "WEGE3.SA_Parkinson_Volatility_Lag_1", "WEGE3.<PERSON>_Parkinson_Volatility_Lag_2", "WEGE3.<PERSON>_Parkinson_Volatility_Lag_3", "WEGE3.<PERSON>_Parkinson_Volatility_Lag_4", "WEGE3.<PERSON>_Parkinson_Volatility_Lag_5", "WEGE3.SA_EMV_Lag_1", "WEGE3.SA_EMV_Lag_2", "WEGE3.SA_EMV_Lag_3", "WEGE3.SA_EMV_Lag_4", "WEGE3.SA_EMV_Lag_5", "WEGE3.SA_EMV_MA_Lag_1", "WEGE3.SA_EMV_MA_Lag_2", "WEGE3.SA_EMV_MA_Lag_3", "WEGE3.SA_EMV_MA_Lag_4", "WEGE3.SA_EMV_MA_Lag_5", "WEGE3.SA_VO_Lag_1", "WEGE3.SA_VO_Lag_2", "WEGE3.SA_VO_Lag_3", "WEGE3.SA_VO_Lag_4", "WEGE3.SA_VO_Lag_5", "WEGE3.SA_MFI_Lag_1", "WEGE3.SA_MFI_Lag_2", "WEGE3.SA_MFI_Lag_3", "WEGE3.SA_MFI_Lag_4", "WEGE3.SA_MFI_Lag_5", "WEGE3.SA_Amihud_Lag_1", "WEGE3.SA_Amihud_Lag_2", "WEGE3.SA_Amihud_Lag_3", "WEGE3.SA_Amihud_Lag_4", "WEGE3.SA_Amihud_Lag_5", "WEGE3.SA_Roll_Spread_Lag_1", "WEGE3.SA_Roll_Spread_Lag_2", "WEGE3.SA_Roll_Spread_Lag_3", "WEGE3.SA_Roll_Spread_Lag_4", "WEGE3.SA_Roll_Spread_Lag_5", "WEGE3.<PERSON>_Hurst_Lag_1", "WEGE3.<PERSON>_Hu<PERSON>_Lag_2", "WEGE3.<PERSON>_Hu<PERSON>_Lag_3", "WEGE3.<PERSON>_<PERSON><PERSON>_Lag_4", "WEGE3.<PERSON>_<PERSON><PERSON>_Lag_5", "WEGE3.SA_Vol_per_Volume_Lag_1", "WEGE3.SA_Vol_per_Volume_Lag_2", "WEGE3.SA_Vol_per_Volume_Lag_3", "WEGE3.SA_Vol_per_Volume_Lag_4", "WEGE3.SA_Vol_per_Volume_Lag_5", "WEGE3.SA_CMF_Lag_1", "WEGE3.SA_CMF_Lag_2", "WEGE3.SA_CMF_Lag_3", "WEGE3.SA_CMF_Lag_4", "WEGE3.SA_CMF_Lag_5", "WEGE3.SA_AD_Line_Lag_1", "WEGE3.SA_AD_Line_Lag_2", "WEGE3.SA_AD_Line_Lag_3", "WEGE3.SA_AD_Line_Lag_4", "WEGE3.SA_AD_Line_Lag_5", "PORT3.SA_Media_OHLC", "PORT3.SA_MFI_Historical", "PORT3.SA_Amihud_Historical", "PORT3.SA_Roll_Spread_Historical", "PORT3.<PERSON>_<PERSON><PERSON>_Historical", "PORT3.SA_Vol_per_Volume_Historical", "PORT3.SA_CMF_Historical", "PORT3.SA_AD_Line_Historical", "PORT3.SA_Segunda", "PORT3.SA_Terca", "PORT3.SA_Quarta", "PORT3.SA_Quinta", "PORT3.SA_Sexta", "PORT3.SA_Mes_1", "PORT3.SA_Mes_2", "PORT3.SA_Mes_3", "PORT3.SA_Mes_4", "PORT3.SA_Mes_5", "PORT3.SA_Mes_6", "PORT3.SA_Mes_7", "PORT3.SA_Mes_8", "PORT3.SA_Mes_9", "PORT3.SA_Mes_10", "PORT3.SA_Mes_11", "PORT3.SA_Mes_12", "PORT3.SA_Quarter_1", "PORT3.SA_Quarter_2", "PORT3.SA_Quarter_3", "PORT3.SA_Quarter_4", "PORT3.SA_Last_Day_Quarter", "PORT3.SA_Pre_Feriado_Brasil", "PORT3.SA_Sinal_Compra", "PORT3.SA_Sinal_Venda", "PORT3.SA_Media_OHLC_Futura", "PORT3.SA_Volume_Lag_1", "PORT3.SA_Volume_Lag_2", "PORT3.SA_Volume_Lag_3", "PORT3.SA_Volume_Lag_4", "PORT3.SA_Volume_Lag_5", "PORT3.SA_Spread_Lag_1", "PORT3.SA_Spread_Lag_2", "PORT3.SA_Spread_Lag_3", "PORT3.SA_Spread_Lag_4", "PORT3.SA_Spread_Lag_5", "PORT3.SA_Volatilidade_Lag_1", "PORT3.SA_Volatilidade_Lag_2", "PORT3.SA_Volatilidade_Lag_3", "PORT3.SA_Volatilidade_Lag_4", "PORT3.SA_Volatilidade_Lag_5", "PORT3.SA_Parkinson_Volatility_Lag_1", "PORT3.<PERSON>_Parkinson_Volatility_Lag_2", "PORT3.SA_Parkinson_Volatility_Lag_3", "PORT3.SA_Parkinson_Volatility_Lag_4", "PORT3.<PERSON>_Parkinson_Volatility_Lag_5", "PORT3.SA_EMV_Lag_1", "PORT3.SA_EMV_Lag_2", "PORT3.SA_EMV_Lag_3", "PORT3.SA_EMV_Lag_4", "PORT3.SA_EMV_Lag_5", "PORT3.SA_EMV_MA_Lag_1", "PORT3.SA_EMV_MA_Lag_2", "PORT3.SA_EMV_MA_Lag_3", "PORT3.SA_EMV_MA_Lag_4", "PORT3.SA_EMV_MA_Lag_5", "PORT3.SA_VO_Lag_1", "PORT3.SA_VO_Lag_2", "PORT3.SA_VO_Lag_3", "PORT3.SA_VO_Lag_4", "PORT3.SA_VO_Lag_5", "PORT3.SA_MFI_Lag_1", "PORT3.SA_MFI_Lag_2", "PORT3.SA_MFI_Lag_3", "PORT3.SA_MFI_Lag_4", "PORT3.SA_MFI_Lag_5", "PORT3.SA_Amihud_Lag_1", "PORT3.SA_Amihud_Lag_2", "PORT3.SA_Amihud_Lag_3", "PORT3.SA_Amihud_Lag_4", "PORT3.SA_Amihud_Lag_5", "PORT3.SA_Roll_Spread_Lag_1", "PORT3.SA_Roll_Spread_Lag_2", "PORT3.SA_Roll_Spread_Lag_3", "PORT3.SA_Roll_Spread_Lag_4", "PORT3.SA_Roll_Spread_Lag_5", "PORT3.<PERSON>_Hu<PERSON>_Lag_1", "PORT3.<PERSON>_<PERSON><PERSON>_Lag_2", "PORT3.<PERSON>_<PERSON><PERSON>_Lag_3", "PORT3.<PERSON>_<PERSON><PERSON>_Lag_4", "PORT3.<PERSON>_<PERSON><PERSON>_Lag_5", "PORT3.SA_Vol_per_Volume_Lag_1", "PORT3.SA_Vol_per_Volume_Lag_2", "PORT3.SA_Vol_per_Volume_Lag_3", "PORT3.SA_Vol_per_Volume_Lag_4", "PORT3.SA_Vol_per_Volume_Lag_5", "PORT3.SA_CMF_Lag_1", "PORT3.SA_CMF_Lag_2", "PORT3.SA_CMF_Lag_3", "PORT3.SA_CMF_Lag_4", "PORT3.SA_CMF_Lag_5", "PORT3.SA_AD_Line_Lag_1", "PORT3.SA_AD_Line_Lag_2", "PORT3.SA_AD_Line_Lag_3", "PORT3.SA_AD_Line_Lag_4", "PORT3.SA_AD_Line_Lag_5", "MDIA3.SA_Media_OHLC", "MDIA3.SA_MFI_Historical", "MDIA3.SA_Amihud_Historical", "MDIA3.SA_Roll_Spread_Historical", "MDIA3.SA_<PERSON><PERSON>_Historical", "MDIA3.SA_Vol_per_Volume_Historical", "MDIA3.SA_CMF_Historical", "MDIA3.SA_AD_Line_Historical", "MDIA3.SA_Segunda", "MDIA3.SA_Terca", "MDIA3.SA_Quarta", "MDIA3.SA_Quinta", "MDIA3.SA_Sexta", "MDIA3.SA_Mes_1", "MDIA3.SA_Mes_2", "MDIA3.SA_Mes_3", "MDIA3.SA_Mes_4", "MDIA3.SA_Mes_5", "MDIA3.SA_Mes_6", "MDIA3.SA_Mes_7", "MDIA3.SA_Mes_8", "MDIA3.SA_Mes_9", "MDIA3.SA_Mes_10", "MDIA3.SA_Mes_11", "MDIA3.SA_Mes_12", "MDIA3.SA_Quarter_1", "MDIA3.SA_Quarter_2", "MDIA3.SA_Quarter_3", "MDIA3.SA_Quarter_4", "MDIA3.SA_Last_Day_Quarter", "MDIA3.SA_Pre_Feriado_Brasil", "MDIA3.SA_Sinal_Compra", "MDIA3.SA_Sinal_Venda", "MDIA3.SA_Media_OHLC_Futura", "MDIA3.SA_Volume_Lag_1", "MDIA3.SA_Volume_Lag_2", "MDIA3.SA_Volume_Lag_3", "MDIA3.SA_Volume_Lag_4", "MDIA3.SA_Volume_Lag_5", "MDIA3.SA_Spread_Lag_1", "MDIA3.SA_Spread_Lag_2", "MDIA3.SA_Spread_Lag_3", "MDIA3.SA_Spread_Lag_4", "MDIA3.SA_Spread_Lag_5", "MDIA3.SA_Volatilidade_Lag_1", "MDIA3.SA_Volatilidade_Lag_2", "MDIA3.SA_Volatilidade_Lag_3", "MDIA3.SA_Volatilidade_Lag_4", "MDIA3.SA_Volatilidade_Lag_5", "MDIA3.SA_Parkinson_Volatility_Lag_1", "MDIA3.SA_Parkinson_Volatility_Lag_2", "MDIA3.SA_Parkinson_Volatility_Lag_3", "MDIA3.SA_Parkinson_Volatility_Lag_4", "MDIA3.SA_Parkinson_Volatility_Lag_5", "MDIA3.SA_EMV_Lag_1", "MDIA3.SA_EMV_Lag_2", "MDIA3.SA_EMV_Lag_3", "MDIA3.SA_EMV_Lag_4", "MDIA3.SA_EMV_Lag_5", "MDIA3.SA_EMV_MA_Lag_1", "MDIA3.SA_EMV_MA_Lag_2", "MDIA3.SA_EMV_MA_Lag_3", "MDIA3.SA_EMV_MA_Lag_4", "MDIA3.SA_EMV_MA_Lag_5", "MDIA3.SA_VO_Lag_1", "MDIA3.SA_VO_Lag_2", "MDIA3.SA_VO_Lag_3", "MDIA3.SA_VO_Lag_4", "MDIA3.SA_VO_Lag_5", "MDIA3.SA_MFI_Lag_1", "MDIA3.SA_MFI_Lag_2", "MDIA3.SA_MFI_Lag_3", "MDIA3.SA_MFI_Lag_4", "MDIA3.SA_MFI_Lag_5", "MDIA3.SA_Amihud_Lag_1", "MDIA3.SA_Amihud_Lag_2", "MDIA3.SA_Amihud_Lag_3", "MDIA3.SA_Amihud_Lag_4", "MDIA3.SA_Amihud_Lag_5", "MDIA3.SA_Roll_Spread_Lag_1", "MDIA3.SA_Roll_Spread_Lag_2", "MDIA3.SA_Roll_Spread_Lag_3", "MDIA3.SA_Roll_Spread_Lag_4", "MDIA3.SA_Roll_Spread_Lag_5", "MDIA3.SA_Hurst_Lag_1", "MDIA3.SA_Hurst_Lag_2", "MDIA3.SA_Hurst_Lag_3", "MDIA3.SA_<PERSON>rst_Lag_4", "MDIA3.<PERSON>_<PERSON><PERSON>_Lag_5", "MDIA3.SA_Vol_per_Volume_Lag_1", "MDIA3.SA_Vol_per_Volume_Lag_2", "MDIA3.SA_Vol_per_Volume_Lag_3", "MDIA3.SA_Vol_per_Volume_Lag_4", "MDIA3.SA_Vol_per_Volume_Lag_5", "MDIA3.SA_CMF_Lag_1", "MDIA3.SA_CMF_Lag_2", "MDIA3.SA_CMF_Lag_3", "MDIA3.SA_CMF_Lag_4", "MDIA3.SA_CMF_Lag_5", "MDIA3.SA_AD_Line_Lag_1", "MDIA3.SA_AD_Line_Lag_2", "MDIA3.SA_AD_Line_Lag_3", "MDIA3.SA_AD_Line_Lag_4", "MDIA3.SA_AD_Line_Lag_5", "PETR3.SA_Media_OHLC", "PETR3.SA_MFI_Historical", "PETR3.SA_Amihud_Historical", "PETR3.SA_Roll_Spread_Historical", "PETR3.SA_<PERSON><PERSON>_Historical", "PETR3.SA_Vol_per_Volume_Historical", "PETR3.SA_CMF_Historical", "PETR3.SA_AD_Line_Historical", "PETR3.SA_Segunda", "PETR3.SA_Terca", "PETR3.SA_Quarta", "PETR3.SA_Quinta", "PETR3.SA_Sexta", "PETR3.SA_Mes_1", "PETR3.SA_Mes_2", "PETR3.SA_Mes_3", "PETR3.SA_Mes_4", "PETR3.SA_Mes_5", "PETR3.SA_Mes_6", "PETR3.SA_Mes_7", "PETR3.SA_Mes_8", "PETR3.SA_Mes_9", "PETR3.SA_Mes_10", "PETR3.SA_Mes_11", "PETR3.SA_Mes_12", "PETR3.SA_Quarter_1", "PETR3.SA_Quarter_2", "PETR3.SA_Quarter_3", "PETR3.SA_Quarter_4", "PETR3.SA_Last_Day_Quarter", "PETR3.SA_Pre_Feriado_Brasil", "PETR3.SA_Sinal_Compra", "PETR3.SA_Sinal_Venda", "PETR3.SA_Media_OHLC_Futura", "PETR3.SA_Volume_Lag_1", "PETR3.SA_Volume_Lag_2", "PETR3.SA_Volume_Lag_3", "PETR3.SA_Volume_Lag_4", "PETR3.SA_Volume_Lag_5", "PETR3.SA_Spread_Lag_1", "PETR3.SA_Spread_Lag_2", "PETR3.SA_Spread_Lag_3", "PETR3.SA_Spread_Lag_4", "PETR3.SA_Spread_Lag_5", "PETR3.SA_Volatilidade_Lag_1", "PETR3.SA_Volatilidade_Lag_2", "PETR3.SA_Volatilidade_Lag_3", "PETR3.SA_Volatilidade_Lag_4", "PETR3.SA_Volatilidade_Lag_5", "PETR3.SA_Parkinson_Volatility_Lag_1", "PETR3.<PERSON>_Parkinson_Volatility_Lag_2", "PETR3.SA_Parkinson_Volatility_Lag_3", "PETR3.SA_Parkinson_Volatility_Lag_4", "PETR3.<PERSON>_Parkinson_Volatility_Lag_5", "PETR3.SA_EMV_Lag_1", "PETR3.SA_EMV_Lag_2", "PETR3.SA_EMV_Lag_3", "PETR3.SA_EMV_Lag_4", "PETR3.SA_EMV_Lag_5", "PETR3.SA_EMV_MA_Lag_1", "PETR3.SA_EMV_MA_Lag_2", "PETR3.SA_EMV_MA_Lag_3", "PETR3.SA_EMV_MA_Lag_4", "PETR3.SA_EMV_MA_Lag_5", "PETR3.SA_VO_Lag_1", "PETR3.SA_VO_Lag_2", "PETR3.SA_VO_Lag_3", "PETR3.SA_VO_Lag_4", "PETR3.SA_VO_Lag_5", "PETR3.SA_MFI_Lag_1", "PETR3.SA_MFI_Lag_2", "PETR3.SA_MFI_Lag_3", "PETR3.SA_MFI_Lag_4", "PETR3.SA_MFI_Lag_5", "PETR3.SA_Amihud_Lag_1", "PETR3.SA_Amihud_Lag_2", "PETR3.SA_Amihud_Lag_3", "PETR3.SA_Amihud_Lag_4", "PETR3.SA_Amihud_Lag_5", "PETR3.SA_Roll_Spread_Lag_1", "PETR3.SA_Roll_Spread_Lag_2", "PETR3.SA_Roll_Spread_Lag_3", "PETR3.SA_Roll_Spread_Lag_4", "PETR3.SA_Roll_Spread_Lag_5", "PETR3.<PERSON>_Hurst_Lag_1", "PETR3.<PERSON>_<PERSON><PERSON>_Lag_2", "PETR3.<PERSON>_<PERSON><PERSON>_Lag_3", "PETR3.<PERSON>_<PERSON><PERSON>_Lag_4", "PETR3.<PERSON>_<PERSON><PERSON>_Lag_5", "PETR3.SA_Vol_per_Volume_Lag_1", "PETR3.SA_Vol_per_Volume_Lag_2", "PETR3.SA_Vol_per_Volume_Lag_3", "PETR3.SA_Vol_per_Volume_Lag_4", "PETR3.SA_Vol_per_Volume_Lag_5", "PETR3.SA_CMF_Lag_1", "PETR3.SA_CMF_Lag_2", "PETR3.SA_CMF_Lag_3", "PETR3.SA_CMF_Lag_4", "PETR3.SA_CMF_Lag_5", "PETR3.SA_AD_Line_Lag_1", "PETR3.SA_AD_Line_Lag_2", "PETR3.SA_AD_Line_Lag_3", "PETR3.SA_AD_Line_Lag_4", "PETR3.SA_AD_Line_Lag_5", "ODPV3.SA_Media_OHLC", "ODPV3.SA_MFI_Historical", "ODPV3.SA_Amihud_Historical", "ODPV3.SA_Roll_Spread_Historical", "ODPV3.SA_Hurst_Historical", "ODPV3.SA_Vol_per_Volume_Historical", "ODPV3.SA_CMF_Historical", "ODPV3.SA_AD_Line_Historical", "ODPV3.SA_Segunda", "ODPV3.SA_Terca", "ODPV3.SA_Quarta", "ODPV3.SA_Quinta", "ODPV3.SA_Sexta", "ODPV3.SA_Mes_1", "ODPV3.SA_Mes_2", "ODPV3.SA_Mes_3", "ODPV3.SA_Mes_4", "ODPV3.SA_Mes_5", "ODPV3.SA_Mes_6", "ODPV3.SA_Mes_7", "ODPV3.SA_Mes_8", "ODPV3.SA_Mes_9", "ODPV3.SA_Mes_10", "ODPV3.SA_Mes_11", "ODPV3.SA_Mes_12", "ODPV3.SA_Quarter_1", "ODPV3.SA_Quarter_2", "ODPV3.SA_Quarter_3", "ODPV3.SA_Quarter_4", "ODPV3.SA_Last_Day_Quarter", "ODPV3.SA_Pre_Feriado_Brasil", "ODPV3.SA_Sinal_Compra", "ODPV3.SA_Sinal_Venda", "ODPV3.SA_Media_OHLC_Futura", "ODPV3.SA_Volume_Lag_1", "ODPV3.SA_Volume_Lag_2", "ODPV3.SA_Volume_Lag_3", "ODPV3.SA_Volume_Lag_4", "ODPV3.SA_Volume_Lag_5", "ODPV3.SA_Spread_Lag_1", "ODPV3.SA_Spread_Lag_2", "ODPV3.SA_Spread_Lag_3", "ODPV3.SA_Spread_Lag_4", "ODPV3.SA_Spread_Lag_5", "ODPV3.SA_Volatilidade_Lag_1", "ODPV3.SA_Volatilidade_Lag_2", "ODPV3.SA_Volatilidade_Lag_3", "ODPV3.SA_Volatilidade_Lag_4", "ODPV3.SA_Volatilidade_Lag_5", "ODPV3.SA_Parkinson_Volatility_Lag_1", "ODPV3.SA_Parkinson_Volatility_Lag_2", "ODPV3.SA_Parkinson_Volatility_Lag_3", "ODPV3.SA_Parkinson_Volatility_Lag_4", "ODPV3.SA_Parkinson_Volatility_Lag_5", "ODPV3.SA_EMV_Lag_1", "ODPV3.SA_EMV_Lag_2", "ODPV3.SA_EMV_Lag_3", "ODPV3.SA_EMV_Lag_4", "ODPV3.SA_EMV_Lag_5", "ODPV3.SA_EMV_MA_Lag_1", "ODPV3.SA_EMV_MA_Lag_2", "ODPV3.SA_EMV_MA_Lag_3", "ODPV3.SA_EMV_MA_Lag_4", "ODPV3.SA_EMV_MA_Lag_5", "ODPV3.SA_VO_Lag_1", "ODPV3.SA_VO_Lag_2", "ODPV3.SA_VO_Lag_3", "ODPV3.SA_VO_Lag_4", "ODPV3.SA_VO_Lag_5", "ODPV3.SA_MFI_Lag_1", "ODPV3.SA_MFI_Lag_2", "ODPV3.SA_MFI_Lag_3", "ODPV3.SA_MFI_Lag_4", "ODPV3.SA_MFI_Lag_5", "ODPV3.SA_Amihud_Lag_1", "ODPV3.SA_Amihud_Lag_2", "ODPV3.SA_Amihud_Lag_3", "ODPV3.SA_Amihud_Lag_4", "ODPV3.SA_Amihud_Lag_5", "ODPV3.SA_Roll_Spread_Lag_1", "ODPV3.SA_Roll_Spread_Lag_2", "ODPV3.SA_Roll_Spread_Lag_3", "ODPV3.SA_Roll_Spread_Lag_4", "ODPV3.SA_Roll_Spread_Lag_5", "ODPV3.SA_Hurst_Lag_1", "ODPV3.SA_Hurst_Lag_2", "ODPV3.SA_Hurst_Lag_3", "ODPV3.SA_<PERSON>rst_Lag_4", "ODPV3.SA_<PERSON><PERSON>_Lag_5", "ODPV3.SA_Vol_per_Volume_Lag_1", "ODPV3.SA_Vol_per_Volume_Lag_2", "ODPV3.SA_Vol_per_Volume_Lag_3", "ODPV3.SA_Vol_per_Volume_Lag_4", "ODPV3.SA_Vol_per_Volume_Lag_5", "ODPV3.SA_CMF_Lag_1", "ODPV3.SA_CMF_Lag_2", "ODPV3.SA_CMF_Lag_3", "ODPV3.SA_CMF_Lag_4", "ODPV3.SA_CMF_Lag_5", "ODPV3.SA_AD_Line_Lag_1", "ODPV3.SA_AD_Line_Lag_2", "ODPV3.SA_AD_Line_Lag_3", "ODPV3.SA_AD_Line_Lag_4", "ODPV3.SA_AD_Line_Lag_5", "GGBR4.SA_Media_OHLC", "GGBR4.SA_MFI_Historical", "GGBR4.SA_Amihud_Historical", "GGBR4.SA_Roll_Spread_Historical", "GGBR4.SA_Hurst_Historical", "GGBR4.SA_Vol_per_Volume_Historical", "GGBR4.SA_CMF_Historical", "GGBR4.SA_AD_Line_Historical", "GGBR4.SA_Segunda", "GGBR4.SA_Terca", "GGBR4.SA_Quarta", "GGBR4.SA_Quinta", "GGBR4.SA_Sexta", "GGBR4.SA_Mes_1", "GGBR4.SA_Mes_2", "GGBR4.SA_Mes_3", "GGBR4.SA_Mes_4", "GGBR4.SA_Mes_5", "GGBR4.SA_Mes_6", "GGBR4.SA_Mes_7", "GGBR4.SA_Mes_8", "GGBR4.SA_Mes_9", "GGBR4.SA_Mes_10", "GGBR4.SA_Mes_11", "GGBR4.SA_Mes_12", "GGBR4.SA_Quarter_1", "GGBR4.SA_Quarter_2", "GGBR4.SA_Quarter_3", "GGBR4.SA_Quarter_4", "GGBR4.SA_Last_Day_Quarter", "GGBR4.SA_Pre_Feriado_Brasil", "GGBR4.SA_Sinal_Compra", "GGBR4.SA_Sinal_Venda", "GGBR4.SA_Media_OHLC_Futura", "GGBR4.SA_Volume_Lag_1", "GGBR4.SA_Volume_Lag_2", "GGBR4.SA_Volume_Lag_3", "GGBR4.SA_Volume_Lag_4", "GGBR4.SA_Volume_Lag_5", "GGBR4.SA_Spread_Lag_1", "GGBR4.SA_Spread_Lag_2", "GGBR4.SA_Spread_Lag_3", "GGBR4.SA_Spread_Lag_4", "GGBR4.SA_Spread_Lag_5", "GGBR4.SA_Volatilidade_Lag_1", "GGBR4.SA_Volatilidade_Lag_2", "GGBR4.SA_Volatilidade_Lag_3", "GGBR4.SA_Volatilidade_Lag_4", "GGBR4.SA_Volatilidade_Lag_5", "GGBR4.SA_Parkinson_Volatility_Lag_1", "GGBR4.SA_Parkinson_Volatility_Lag_2", "GGBR4.SA_Parkinson_Volatility_Lag_3", "GGBR4.SA_Parkinson_Volatility_Lag_4", "GGBR4.SA_Parkinson_Volatility_Lag_5", "GGBR4.SA_EMV_Lag_1", "GGBR4.SA_EMV_Lag_2", "GGBR4.SA_EMV_Lag_3", "GGBR4.SA_EMV_Lag_4", "GGBR4.SA_EMV_Lag_5", "GGBR4.SA_EMV_MA_Lag_1", "GGBR4.SA_EMV_MA_Lag_2", "GGBR4.SA_EMV_MA_Lag_3", "GGBR4.SA_EMV_MA_Lag_4", "GGBR4.SA_EMV_MA_Lag_5", "GGBR4.SA_VO_Lag_1", "GGBR4.SA_VO_Lag_2", "GGBR4.SA_VO_Lag_3", "GGBR4.SA_VO_Lag_4", "GGBR4.SA_VO_Lag_5", "GGBR4.SA_MFI_Lag_1", "GGBR4.SA_MFI_Lag_2", "GGBR4.SA_MFI_Lag_3", "GGBR4.SA_MFI_Lag_4", "GGBR4.SA_MFI_Lag_5", "GGBR4.SA_Amihud_Lag_1", "GGBR4.SA_Amihud_Lag_2", "GGBR4.SA_Amihud_Lag_3", "GGBR4.SA_Amihud_Lag_4", "GGBR4.SA_Amihud_Lag_5", "GGBR4.SA_Roll_Spread_Lag_1", "GGBR4.SA_Roll_Spread_Lag_2", "GGBR4.SA_Roll_Spread_Lag_3", "GGBR4.SA_Roll_Spread_Lag_4", "GGBR4.SA_Roll_Spread_Lag_5", "GGBR4.SA_Hurst_Lag_1", "GGBR4.<PERSON>_<PERSON>rst_Lag_2", "GGBR4.SA_<PERSON>rst_Lag_3", "GGBR4.<PERSON>_<PERSON>rst_Lag_4", "GGBR4.<PERSON>_<PERSON><PERSON>_Lag_5", "GGBR4.SA_Vol_per_Volume_Lag_1", "GGBR4.SA_Vol_per_Volume_Lag_2", "GGBR4.SA_Vol_per_Volume_Lag_3", "GGBR4.SA_Vol_per_Volume_Lag_4", "GGBR4.SA_Vol_per_Volume_Lag_5", "GGBR4.SA_CMF_Lag_1", "GGBR4.SA_CMF_Lag_2", "GGBR4.SA_CMF_Lag_3", "GGBR4.SA_CMF_Lag_4", "GGBR4.SA_CMF_Lag_5", "GGBR4.SA_AD_Line_Lag_1", "GGBR4.SA_AD_Line_Lag_2", "GGBR4.SA_AD_Line_Lag_3", "GGBR4.SA_AD_Line_Lag_4", "GGBR4.SA_AD_Line_Lag_5", "RADL3.SA_Media_OHLC", "RADL3.SA_MFI_Historical", "RADL3.SA_Amihud_Historical", "RADL3.SA_Roll_Spread_Historical", "RADL3.<PERSON>_<PERSON><PERSON>_Historical", "RADL3.SA_Vol_per_Volume_Historical", "RADL3.SA_CMF_Historical", "RADL3.SA_AD_Line_Historical", "RADL3.SA_Segunda", "RADL3.SA_Terca", "RADL3.SA_Quarta", "RADL3.SA_Quinta", "RADL3.SA_Sexta", "RADL3.SA_Mes_1", "RADL3.SA_Mes_2", "RADL3.SA_Mes_3", "RADL3.SA_Mes_4", "RADL3.SA_Mes_5", "RADL3.SA_Mes_6", "RADL3.SA_Mes_7", "RADL3.SA_Mes_8", "RADL3.SA_Mes_9", "RADL3.SA_Mes_10", "RADL3.SA_Mes_11", "RADL3.SA_Mes_12", "RADL3.SA_Quarter_1", "RADL3.SA_Quarter_2", "RADL3.SA_Quarter_3", "RADL3.SA_Quarter_4", "RADL3.SA_Last_Day_Quarter", "RADL3.SA_Pre_Feriado_Brasil", "RADL3.SA_Sinal_Compra", "RADL3.SA_Sinal_Venda", "RADL3.SA_Media_OHLC_Futura", "RADL3.SA_Volume_Lag_1", "RADL3.SA_Volume_Lag_2", "RADL3.SA_Volume_Lag_3", "RADL3.SA_Volume_Lag_4", "RADL3.SA_Volume_Lag_5", "RADL3.SA_Spread_Lag_1", "RADL3.SA_Spread_Lag_2", "RADL3.SA_Spread_Lag_3", "RADL3.SA_Spread_Lag_4", "RADL3.SA_Spread_Lag_5", "RADL3.SA_Volatilidade_Lag_1", "RADL3.SA_Volatilidade_Lag_2", "RADL3.SA_Volatilidade_Lag_3", "RADL3.SA_Volatilidade_Lag_4", "RADL3.SA_Volatilidade_Lag_5", "RADL3.<PERSON>_Parkinson_Volatility_Lag_1", "RADL3.<PERSON>_Parkinson_Volatility_Lag_2", "RADL3.<PERSON>_Parkinson_Volatility_Lag_3", "RADL3.<PERSON>_Parkinson_Volatility_Lag_4", "RADL3.<PERSON>_Parkinson_Volatility_Lag_5", "RADL3.SA_EMV_Lag_1", "RADL3.SA_EMV_Lag_2", "RADL3.SA_EMV_Lag_3", "RADL3.SA_EMV_Lag_4", "RADL3.SA_EMV_Lag_5", "RADL3.SA_EMV_MA_Lag_1", "RADL3.SA_EMV_MA_Lag_2", "RADL3.SA_EMV_MA_Lag_3", "RADL3.SA_EMV_MA_Lag_4", "RADL3.SA_EMV_MA_Lag_5", "RADL3.SA_VO_Lag_1", "RADL3.SA_VO_Lag_2", "RADL3.SA_VO_Lag_3", "RADL3.SA_VO_Lag_4", "RADL3.SA_VO_Lag_5", "RADL3.SA_MFI_Lag_1", "RADL3.SA_MFI_Lag_2", "RADL3.SA_MFI_Lag_3", "RADL3.SA_MFI_Lag_4", "RADL3.SA_MFI_Lag_5", "RADL3.SA_Amihud_Lag_1", "RADL3.SA_Amihud_Lag_2", "RADL3.SA_Amihud_Lag_3", "RADL3.SA_Amihud_Lag_4", "RADL3.SA_Amihud_Lag_5", "RADL3.SA_Roll_Spread_Lag_1", "RADL3.SA_Roll_Spread_Lag_2", "RADL3.SA_Roll_Spread_Lag_3", "RADL3.SA_Roll_Spread_Lag_4", "RADL3.SA_Roll_Spread_Lag_5", "RADL3.<PERSON>_Hu<PERSON>_Lag_1", "RADL3.<PERSON>_<PERSON><PERSON>_Lag_2", "RADL3.<PERSON>_<PERSON><PERSON>_Lag_3", "RADL3.<PERSON>_<PERSON><PERSON>_Lag_4", "RADL3.<PERSON>_<PERSON><PERSON>_Lag_5", "RADL3.SA_Vol_per_Volume_Lag_1", "RADL3.SA_Vol_per_Volume_Lag_2", "RADL3.SA_Vol_per_Volume_Lag_3", "RADL3.SA_Vol_per_Volume_Lag_4", "RADL3.SA_Vol_per_Volume_Lag_5", "RADL3.SA_CMF_Lag_1", "RADL3.SA_CMF_Lag_2", "RADL3.SA_CMF_Lag_3", "RADL3.SA_CMF_Lag_4", "RADL3.SA_CMF_Lag_5", "RADL3.SA_AD_Line_Lag_1", "RADL3.SA_AD_Line_Lag_2", "RADL3.SA_AD_Line_Lag_3", "RADL3.SA_AD_Line_Lag_4", "RADL3.SA_AD_Line_Lag_5", "CMIG3.SA_Media_OHLC", "CMIG3.SA_MFI_Historical", "CMIG3.SA_Amihud_Historical", "CMIG3.SA_Roll_Spread_Historical", "CMIG3.<PERSON>_<PERSON><PERSON>_Historical", "CMIG3.SA_Vol_per_Volume_Historical", "CMIG3.SA_CMF_Historical", "CMIG3.SA_AD_Line_Historical", "CMIG3.SA_Segunda", "CMIG3.SA_Terca", "CMIG3.SA_Quarta", "CMIG3.SA_Quinta", "CMIG3.SA_Sexta", "CMIG3.SA_Mes_1", "CMIG3.SA_Mes_2", "CMIG3.SA_Mes_3", "CMIG3.SA_Mes_4", "CMIG3.SA_Mes_5", "CMIG3.SA_Mes_6", "CMIG3.SA_Mes_7", "CMIG3.SA_Mes_8", "CMIG3.SA_Mes_9", "CMIG3.SA_Mes_10", "CMIG3.SA_Mes_11", "CMIG3.SA_Mes_12", "CMIG3.SA_Quarter_1", "CMIG3.SA_Quarter_2", "CMIG3.SA_Quarter_3", "CMIG3.SA_Quarter_4", "CMIG3.SA_Last_Day_Quarter", "CMIG3.SA_Pre_Feriado_Brasil", "CMIG3.SA_Sinal_Compra", "CMIG3.SA_Sinal_Venda", "CMIG3.SA_Media_OHLC_Futura", "CMIG3.SA_Volume_Lag_1", "CMIG3.SA_Volume_Lag_2", "CMIG3.SA_Volume_Lag_3", "CMIG3.SA_Volume_Lag_4", "CMIG3.SA_Volume_Lag_5", "CMIG3.SA_Spread_Lag_1", "CMIG3.SA_Spread_Lag_2", "CMIG3.SA_Spread_Lag_3", "CMIG3.SA_Spread_Lag_4", "CMIG3.SA_Spread_Lag_5", "CMIG3.SA_Volatilidade_Lag_1", "CMIG3.SA_Volatilidade_Lag_2", "CMIG3.SA_Volatilidade_Lag_3", "CMIG3.SA_Volatilidade_Lag_4", "CMIG3.SA_Volatilidade_Lag_5", "CMIG3.SA_Parkinson_Volatility_Lag_1", "CMIG3.<PERSON>_Parkinson_Volatility_Lag_2", "CMIG3.SA_Parkinson_Volatility_Lag_3", "CMIG3.SA_Parkinson_Volatility_Lag_4", "CMIG3.<PERSON>_Parkinson_Volatility_Lag_5", "CMIG3.SA_EMV_Lag_1", "CMIG3.SA_EMV_Lag_2", "CMIG3.SA_EMV_Lag_3", "CMIG3.SA_EMV_Lag_4", "CMIG3.SA_EMV_Lag_5", "CMIG3.SA_EMV_MA_Lag_1", "CMIG3.SA_EMV_MA_Lag_2", "CMIG3.SA_EMV_MA_Lag_3", "CMIG3.SA_EMV_MA_Lag_4", "CMIG3.SA_EMV_MA_Lag_5", "CMIG3.SA_VO_Lag_1", "CMIG3.SA_VO_Lag_2", "CMIG3.SA_VO_Lag_3", "CMIG3.SA_VO_Lag_4", "CMIG3.SA_VO_Lag_5", "CMIG3.SA_MFI_Lag_1", "CMIG3.SA_MFI_Lag_2", "CMIG3.SA_MFI_Lag_3", "CMIG3.SA_MFI_Lag_4", "CMIG3.SA_MFI_Lag_5", "CMIG3.SA_Amihud_Lag_1", "CMIG3.SA_Amihud_Lag_2", "CMIG3.SA_Amihud_Lag_3", "CMIG3.SA_Amihud_Lag_4", "CMIG3.SA_Amihud_Lag_5", "CMIG3.SA_Roll_Spread_Lag_1", "CMIG3.SA_Roll_Spread_Lag_2", "CMIG3.SA_Roll_Spread_Lag_3", "CMIG3.SA_Roll_Spread_Lag_4", "CMIG3.SA_Roll_Spread_Lag_5", "CMIG3.SA_Hurst_Lag_1", "CMIG3.<PERSON>_Hu<PERSON>_Lag_2", "CMIG3.<PERSON>_Hurst_Lag_3", "CMIG3.<PERSON>_<PERSON><PERSON>_Lag_4", "CMIG3.<PERSON>_<PERSON><PERSON>_Lag_5", "CMIG3.SA_Vol_per_Volume_Lag_1", "CMIG3.SA_Vol_per_Volume_Lag_2", "CMIG3.SA_Vol_per_Volume_Lag_3", "CMIG3.SA_Vol_per_Volume_Lag_4", "CMIG3.SA_Vol_per_Volume_Lag_5", "CMIG3.SA_CMF_Lag_1", "CMIG3.SA_CMF_Lag_2", "CMIG3.SA_CMF_Lag_3", "CMIG3.SA_CMF_Lag_4", "CMIG3.SA_CMF_Lag_5", "CMIG3.SA_AD_Line_Lag_1", "CMIG3.SA_AD_Line_Lag_2", "CMIG3.SA_AD_Line_Lag_3", "CMIG3.SA_AD_Line_Lag_4", "CMIG3.SA_AD_Line_Lag_5", "CSMG3.SA_Media_OHLC", "CSMG3.SA_MFI_Historical", "CSMG3.SA_Amihud_Historical", "CSMG3.SA_Roll_Spread_Historical", "CSMG3.SA_<PERSON><PERSON>_Historical", "CSMG3.SA_Vol_per_Volume_Historical", "CSMG3.SA_CMF_Historical", "CSMG3.SA_AD_Line_Historical", "CSMG3.SA_Segunda", "CSMG3.SA_Terca", "CSMG3.SA_Quarta", "CSMG3.SA_Quinta", "CSMG3.SA_Sexta", "CSMG3.SA_Mes_1", "CSMG3.SA_Mes_2", "CSMG3.SA_Mes_3", "CSMG3.SA_Mes_4", "CSMG3.SA_Mes_5", "CSMG3.SA_Mes_6", "CSMG3.SA_Mes_7", "CSMG3.SA_Mes_8", "CSMG3.SA_Mes_9", "CSMG3.SA_Mes_10", "CSMG3.SA_Mes_11", "CSMG3.SA_Mes_12", "CSMG3.SA_Quarter_1", "CSMG3.SA_Quarter_2", "CSMG3.SA_Quarter_3", "CSMG3.SA_Quarter_4", "CSMG3.SA_Last_Day_Quarter", "CSMG3.SA_Pre_Feriado_Brasil", "CSMG3.SA_Sinal_Compra", "CSMG3.SA_Sinal_Venda", "CSMG3.SA_Media_OHLC_Futura", "CSMG3.SA_Volume_Lag_1", "CSMG3.SA_Volume_Lag_2", "CSMG3.SA_Volume_Lag_3", "CSMG3.SA_Volume_Lag_4", "CSMG3.SA_Volume_Lag_5", "CSMG3.SA_Spread_Lag_1", "CSMG3.SA_Spread_Lag_2", "CSMG3.SA_Spread_Lag_3", "CSMG3.SA_Spread_Lag_4", "CSMG3.SA_Spread_Lag_5", "CSMG3.SA_Volatilidade_Lag_1", "CSMG3.SA_Volatilidade_Lag_2", "CSMG3.SA_Volatilidade_Lag_3", "CSMG3.SA_Volatilidade_Lag_4", "CSMG3.SA_Volatilidade_Lag_5", "CSMG3.SA_Parkinson_Volatility_Lag_1", "CSMG3.SA_Parkinson_Volatility_Lag_2", "CSMG3.SA_Parkinson_Volatility_Lag_3", "CSMG3.SA_Parkinson_Volatility_Lag_4", "CSMG3.SA_Parkinson_Volatility_Lag_5", "CSMG3.SA_EMV_Lag_1", "CSMG3.SA_EMV_Lag_2", "CSMG3.SA_EMV_Lag_3", "CSMG3.SA_EMV_Lag_4", "CSMG3.SA_EMV_Lag_5", "CSMG3.SA_EMV_MA_Lag_1", "CSMG3.SA_EMV_MA_Lag_2", "CSMG3.SA_EMV_MA_Lag_3", "CSMG3.SA_EMV_MA_Lag_4", "CSMG3.SA_EMV_MA_Lag_5", "CSMG3.SA_VO_Lag_1", "CSMG3.SA_VO_Lag_2", "CSMG3.SA_VO_Lag_3", "CSMG3.SA_VO_Lag_4", "CSMG3.SA_VO_Lag_5", "CSMG3.SA_MFI_Lag_1", "CSMG3.SA_MFI_Lag_2", "CSMG3.SA_MFI_Lag_3", "CSMG3.SA_MFI_Lag_4", "CSMG3.SA_MFI_Lag_5", "CSMG3.SA_Amihud_Lag_1", "CSMG3.SA_Amihud_Lag_2", "CSMG3.SA_Amihud_Lag_3", "CSMG3.SA_Amihud_Lag_4", "CSMG3.SA_Amihud_Lag_5", "CSMG3.SA_Roll_Spread_Lag_1", "CSMG3.SA_Roll_Spread_Lag_2", "CSMG3.SA_Roll_Spread_Lag_3", "CSMG3.SA_Roll_Spread_Lag_4", "CSMG3.SA_Roll_Spread_Lag_5", "CSMG3.SA_Hurst_Lag_1", "CSMG3.SA_Hurst_Lag_2", "CSMG3.SA_Hurst_Lag_3", "CSMG3.<PERSON>_<PERSON>rst_Lag_4", "CSMG3.<PERSON>_<PERSON><PERSON>_Lag_5", "CSMG3.SA_Vol_per_Volume_Lag_1", "CSMG3.SA_Vol_per_Volume_Lag_2", "CSMG3.SA_Vol_per_Volume_Lag_3", "CSMG3.SA_Vol_per_Volume_Lag_4", "CSMG3.SA_Vol_per_Volume_Lag_5", "CSMG3.SA_CMF_Lag_1", "CSMG3.SA_CMF_Lag_2", "CSMG3.SA_CMF_Lag_3", "CSMG3.SA_CMF_Lag_4", "CSMG3.SA_CMF_Lag_5", "CSMG3.SA_AD_Line_Lag_1", "CSMG3.SA_AD_Line_Lag_2", "CSMG3.SA_AD_Line_Lag_3", "CSMG3.SA_AD_Line_Lag_4", "CSMG3.SA_AD_Line_Lag_5", "KLBN11.SA_Media_OHLC", "KLBN11.SA_MFI_Historical", "KLBN11.SA_Amihud_Historical", "KLBN11.SA_Roll_Spread_Historical", "KLBN11.SA_Hurst_Historical", "KLBN11.SA_Vol_per_Volume_Historical", "KLBN11.SA_CMF_Historical", "KLBN11.SA_AD_Line_Historical", "KLBN11.SA_Segunda", "KLBN11.SA_Terca", "KLBN11.SA_Quarta", "KLBN11.SA_Quinta", "KLBN11.SA_Sexta", "KLBN11.SA_Mes_1", "KLBN11.SA_Mes_2", "KLBN11.SA_Mes_3", "KLBN11.SA_Mes_4", "KLBN11.SA_Mes_5", "KLBN11.SA_Mes_6", "KLBN11.SA_Mes_7", "KLBN11.SA_Mes_8", "KLBN11.SA_Mes_9", "KLBN11.SA_Mes_10", "KLBN11.SA_Mes_11", "KLBN11.SA_Mes_12", "KLBN11.SA_Quarter_1", "KLBN11.SA_Quarter_2", "KLBN11.SA_Quarter_3", "KLBN11.SA_Quarter_4", "KLBN11.SA_Last_Day_Quarter", "KLBN11.SA_Pre_Feriado_Brasil", "KLBN11.SA_Sinal_Compra", "KLBN11.SA_Sinal_Venda", "KLBN11.SA_Media_OHLC_Futura", "KLBN11.SA_Volume_Lag_1", "KLBN11.SA_Volume_Lag_2", "KLBN11.SA_Volume_Lag_3", "KLBN11.SA_Volume_Lag_4", "KLBN11.SA_Volume_Lag_5", "KLBN11.SA_Spread_Lag_1", "KLBN11.SA_Spread_Lag_2", "KLBN11.SA_Spread_Lag_3", "KLBN11.SA_Spread_Lag_4", "KLBN11.SA_Spread_Lag_5", "KLBN11.SA_Volatilidade_Lag_1", "KLBN11.SA_Volatilidade_Lag_2", "KLBN11.SA_Volatilidade_Lag_3", "KLBN11.SA_Volatilidade_Lag_4", "KLBN11.SA_Volatilidade_Lag_5", "KLBN11.<PERSON>_Parkinson_Volatility_Lag_1", "KLBN11.<PERSON>_Parkinson_Volatility_Lag_2", "KLBN11.<PERSON>_Parkinson_Volatility_Lag_3", "KLBN11.<PERSON>_Parkinson_Volatility_Lag_4", "KLBN11.<PERSON>_Parkinson_Volatility_Lag_5", "KLBN11.SA_EMV_Lag_1", "KLBN11.SA_EMV_Lag_2", "KLBN11.SA_EMV_Lag_3", "KLBN11.SA_EMV_Lag_4", "KLBN11.SA_EMV_Lag_5", "KLBN11.SA_EMV_MA_Lag_1", "KLBN11.SA_EMV_MA_Lag_2", "KLBN11.SA_EMV_MA_Lag_3", "KLBN11.SA_EMV_MA_Lag_4", "KLBN11.SA_EMV_MA_Lag_5", "KLBN11.SA_VO_Lag_1", "KLBN11.SA_VO_Lag_2", "KLBN11.SA_VO_Lag_3", "KLBN11.SA_VO_Lag_4", "KLBN11.SA_VO_Lag_5", "KLBN11.SA_MFI_Lag_1", "KLBN11.SA_MFI_Lag_2", "KLBN11.SA_MFI_Lag_3", "KLBN11.SA_MFI_Lag_4", "KLBN11.SA_MFI_Lag_5", "KLBN11.SA_Amihud_Lag_1", "KLBN11.SA_Amihud_Lag_2", "KLBN11.SA_Amihud_Lag_3", "KLBN11.SA_Amihud_Lag_4", "KLBN11.SA_Amihud_Lag_5", "KLBN11.SA_Roll_Spread_Lag_1", "KLBN11.SA_Roll_Spread_Lag_2", "KLBN11.SA_Roll_Spread_Lag_3", "KLBN11.SA_Roll_Spread_Lag_4", "KLBN11.SA_Roll_Spread_Lag_5", "KLBN11.SA_Hurst_Lag_1", "KLBN11.SA_Hurst_Lag_2", "KLBN11.SA_Hurst_Lag_3", "KLBN11.SA_Hurst_Lag_4", "KLBN11.<PERSON>_Hurst_Lag_5", "KLBN11.SA_Vol_per_Volume_Lag_1", "KLBN11.SA_Vol_per_Volume_Lag_2", "KLBN11.SA_Vol_per_Volume_Lag_3", "KLBN11.SA_Vol_per_Volume_Lag_4", "KLBN11.SA_Vol_per_Volume_Lag_5", "KLBN11.SA_CMF_Lag_1", "KLBN11.SA_CMF_Lag_2", "KLBN11.SA_CMF_Lag_3", "KLBN11.SA_CMF_Lag_4", "KLBN11.SA_CMF_Lag_5", "KLBN11.SA_AD_Line_Lag_1", "KLBN11.SA_AD_Line_Lag_2", "KLBN11.SA_AD_Line_Lag_3", "KLBN11.SA_AD_Line_Lag_4", "KLBN11.SA_AD_Line_Lag_5", "HYPE3.SA_Media_OHLC", "HYPE3.SA_MFI_Historical", "HYPE3.SA_Amihud_Historical", "HYPE3.SA_Roll_Spread_Historical", "HYPE3.<PERSON>_<PERSON><PERSON>_Historical", "HYPE3.SA_Vol_per_Volume_Historical", "HYPE3.SA_CMF_Historical", "HYPE3.SA_AD_Line_Historical", "HYPE3.SA_Segunda", "HYPE3.SA_Terca", "HYPE3.SA_Quarta", "HYPE3.SA_Quinta", "HYPE3.SA_Sexta", "HYPE3.SA_Mes_1", "HYPE3.SA_Mes_2", "HYPE3.SA_Mes_3", "HYPE3.SA_Mes_4", "HYPE3.SA_Mes_5", "HYPE3.SA_Mes_6", "HYPE3.SA_Mes_7", "HYPE3.SA_Mes_8", "HYPE3.SA_Mes_9", "HYPE3.SA_Mes_10", "HYPE3.SA_Mes_11", "HYPE3.SA_Mes_12", "HYPE3.SA_Quarter_1", "HYPE3.SA_Quarter_2", "HYPE3.SA_Quarter_3", "HYPE3.SA_Quarter_4", "HYPE3.SA_Last_Day_Quarter", "HYPE3.SA_Pre_Feriado_Brasil", "HYPE3.SA_Sinal_Compra", "HYPE3.SA_Sinal_Venda", "HYPE3.SA_Media_OHLC_Futura", "HYPE3.SA_Volume_Lag_1", "HYPE3.SA_Volume_Lag_2", "HYPE3.SA_Volume_Lag_3", "HYPE3.SA_Volume_Lag_4", "HYPE3.SA_Volume_Lag_5", "HYPE3.SA_Spread_Lag_1", "HYPE3.SA_Spread_Lag_2", "HYPE3.SA_Spread_Lag_3", "HYPE3.SA_Spread_Lag_4", "HYPE3.SA_Spread_Lag_5", "HYPE3.SA_Volatilidade_Lag_1", "HYPE3.SA_Volatilidade_Lag_2", "HYPE3.SA_Volatilidade_Lag_3", "HYPE3.SA_Volatilidade_Lag_4", "HYPE3.SA_Volatilidade_Lag_5", "HYPE3.SA_Parkinson_Volatility_Lag_1", "HYPE3.<PERSON>_Parkinson_Volatility_Lag_2", "HYPE3.SA_Parkinson_Volatility_Lag_3", "HYPE3.SA_Parkinson_Volatility_Lag_4", "HYPE3.<PERSON>_Parkinson_Volatility_Lag_5", "HYPE3.SA_EMV_Lag_1", "HYPE3.SA_EMV_Lag_2", "HYPE3.SA_EMV_Lag_3", "HYPE3.SA_EMV_Lag_4", "HYPE3.SA_EMV_Lag_5", "HYPE3.SA_EMV_MA_Lag_1", "HYPE3.SA_EMV_MA_Lag_2", "HYPE3.SA_EMV_MA_Lag_3", "HYPE3.SA_EMV_MA_Lag_4", "HYPE3.SA_EMV_MA_Lag_5", "HYPE3.SA_VO_Lag_1", "HYPE3.SA_VO_Lag_2", "HYPE3.SA_VO_Lag_3", "HYPE3.SA_VO_Lag_4", "HYPE3.SA_VO_Lag_5", "HYPE3.SA_MFI_Lag_1", "HYPE3.SA_MFI_Lag_2", "HYPE3.SA_MFI_Lag_3", "HYPE3.SA_MFI_Lag_4", "HYPE3.SA_MFI_Lag_5", "HYPE3.SA_Amihud_Lag_1", "HYPE3.SA_Amihud_Lag_2", "HYPE3.SA_Amihud_Lag_3", "HYPE3.SA_Amihud_Lag_4", "HYPE3.SA_Amihud_Lag_5", "HYPE3.SA_Roll_Spread_Lag_1", "HYPE3.SA_Roll_Spread_Lag_2", "HYPE3.SA_Roll_Spread_Lag_3", "HYPE3.SA_Roll_Spread_Lag_4", "HYPE3.SA_Roll_Spread_Lag_5", "HYPE3.<PERSON>_Hurst_Lag_1", "HYPE3.<PERSON>_Hu<PERSON>_Lag_2", "HYPE3.<PERSON>_Hu<PERSON>_Lag_3", "HYPE3.<PERSON>_<PERSON><PERSON>_Lag_4", "HYPE3.<PERSON>_<PERSON><PERSON>_Lag_5", "HYPE3.SA_Vol_per_Volume_Lag_1", "HYPE3.SA_Vol_per_Volume_Lag_2", "HYPE3.SA_Vol_per_Volume_Lag_3", "HYPE3.SA_Vol_per_Volume_Lag_4", "HYPE3.SA_Vol_per_Volume_Lag_5", "HYPE3.SA_CMF_Lag_1", "HYPE3.SA_CMF_Lag_2", "HYPE3.SA_CMF_Lag_3", "HYPE3.SA_CMF_Lag_4", "HYPE3.SA_CMF_Lag_5", "HYPE3.SA_AD_Line_Lag_1", "HYPE3.SA_AD_Line_Lag_2", "HYPE3.SA_AD_Line_Lag_3", "HYPE3.SA_AD_Line_Lag_4", "HYPE3.SA_AD_Line_Lag_5", "CXSE3.SA_Media_OHLC", "CXSE3.SA_MFI_Historical", "CXSE3.SA_Amihud_Historical", "CXSE3.SA_Roll_Spread_Historical", "CXSE3.<PERSON>_<PERSON><PERSON>_Historical", "CXSE3.SA_Vol_per_Volume_Historical", "CXSE3.SA_CMF_Historical", "CXSE3.SA_AD_Line_Historical", "CXSE3.SA_Segunda", "CXSE3.SA_Terca", "CXSE3.SA_Quarta", "CXSE3.SA_Quinta", "CXSE3.SA_Sexta", "CXSE3.SA_Mes_1", "CXSE3.SA_Mes_2", "CXSE3.SA_Mes_3", "CXSE3.SA_Mes_4", "CXSE3.SA_Mes_5", "CXSE3.SA_Mes_6", "CXSE3.SA_Mes_7", "CXSE3.SA_Mes_8", "CXSE3.SA_Mes_9", "CXSE3.SA_Mes_10", "CXSE3.SA_Mes_11", "CXSE3.SA_Mes_12", "CXSE3.SA_Quarter_1", "CXSE3.SA_Quarter_2", "CXSE3.SA_Quarter_3", "CXSE3.SA_Quarter_4", "CXSE3.SA_Last_Day_Quarter", "CXSE3.SA_Pre_Feriado_Brasil", "CXSE3.SA_Sinal_Compra", "CXSE3.SA_Sinal_Venda", "CXSE3.SA_Media_OHLC_Futura", "CXSE3.SA_Volume_Lag_1", "CXSE3.SA_Volume_Lag_2", "CXSE3.SA_Volume_Lag_3", "CXSE3.SA_Volume_Lag_4", "CXSE3.SA_Volume_Lag_5", "CXSE3.SA_Spread_Lag_1", "CXSE3.SA_Spread_Lag_2", "CXSE3.SA_Spread_Lag_3", "CXSE3.SA_Spread_Lag_4", "CXSE3.SA_Spread_Lag_5", "CXSE3.SA_Volatilidade_Lag_1", "CXSE3.SA_Volatilidade_Lag_2", "CXSE3.SA_Volatilidade_Lag_3", "CXSE3.SA_Volatilidade_Lag_4", "CXSE3.SA_Volatilidade_Lag_5", "CXSE3.SA_Parkinson_Volatility_Lag_1", "CXSE3.SA_Parkinson_Volatility_Lag_2", "CXSE3.SA_Parkinson_Volatility_Lag_3", "CXSE3.SA_Parkinson_Volatility_Lag_4", "CXSE3.<PERSON>_Parkinson_Volatility_Lag_5", "CXSE3.SA_EMV_Lag_1", "CXSE3.SA_EMV_Lag_2", "CXSE3.SA_EMV_Lag_3", "CXSE3.SA_EMV_Lag_4", "CXSE3.SA_EMV_Lag_5", "CXSE3.SA_EMV_MA_Lag_1", "CXSE3.SA_EMV_MA_Lag_2", "CXSE3.SA_EMV_MA_Lag_3", "CXSE3.SA_EMV_MA_Lag_4", "CXSE3.SA_EMV_MA_Lag_5", "CXSE3.SA_VO_Lag_1", "CXSE3.SA_VO_Lag_2", "CXSE3.SA_VO_Lag_3", "CXSE3.SA_VO_Lag_4", "CXSE3.SA_VO_Lag_5", "CXSE3.SA_MFI_Lag_1", "CXSE3.SA_MFI_Lag_2", "CXSE3.SA_MFI_Lag_3", "CXSE3.SA_MFI_Lag_4", "CXSE3.SA_MFI_Lag_5", "CXSE3.SA_Amihud_Lag_1", "CXSE3.SA_Amihud_Lag_2", "CXSE3.SA_Amihud_Lag_3", "CXSE3.SA_Amihud_Lag_4", "CXSE3.SA_Amihud_Lag_5", "CXSE3.SA_Roll_Spread_Lag_1", "CXSE3.SA_Roll_Spread_Lag_2", "CXSE3.SA_Roll_Spread_Lag_3", "CXSE3.SA_Roll_Spread_Lag_4", "CXSE3.SA_Roll_Spread_Lag_5", "CXSE3.SA_Hurst_Lag_1", "CXSE3.<PERSON>_Hurst_Lag_2", "CXSE3.<PERSON>_Hurst_Lag_3", "CXSE3.<PERSON>_Hu<PERSON>_Lag_4", "CXSE3.<PERSON>_<PERSON><PERSON>_Lag_5", "CXSE3.SA_Vol_per_Volume_Lag_1", "CXSE3.SA_Vol_per_Volume_Lag_2", "CXSE3.SA_Vol_per_Volume_Lag_3", "CXSE3.SA_Vol_per_Volume_Lag_4", "CXSE3.SA_Vol_per_Volume_Lag_5", "CXSE3.SA_CMF_Lag_1", "CXSE3.SA_CMF_Lag_2", "CXSE3.SA_CMF_Lag_3", "CXSE3.SA_CMF_Lag_4", "CXSE3.SA_CMF_Lag_5", "CXSE3.SA_AD_Line_Lag_1", "CXSE3.SA_AD_Line_Lag_2", "CXSE3.SA_AD_Line_Lag_3", "CXSE3.SA_AD_Line_Lag_4", "CXSE3.SA_AD_Line_Lag_5", "MELK3.SA_Media_OHLC", "MELK3.SA_MFI_Historical", "MELK3.SA_Amihud_Historical", "MELK3.SA_Roll_Spread_Historical", "MELK3.SA_<PERSON><PERSON>_Historical", "MELK3.SA_Vol_per_Volume_Historical", "MELK3.SA_CMF_Historical", "MELK3.SA_AD_Line_Historical", "MELK3.SA_Segunda", "MELK3.SA_Terca", "MELK3.SA_Quarta", "MELK3.SA_Quinta", "MELK3.SA_Sexta", "MELK3.SA_Mes_1", "MELK3.SA_Mes_2", "MELK3.SA_Mes_3", "MELK3.SA_Mes_4", "MELK3.SA_Mes_5", "MELK3.SA_Mes_6", "MELK3.SA_Mes_7", "MELK3.SA_Mes_8", "MELK3.SA_Mes_9", "MELK3.SA_Mes_10", "MELK3.SA_Mes_11", "MELK3.SA_Mes_12", "MELK3.SA_Quarter_1", "MELK3.SA_Quarter_2", "MELK3.SA_Quarter_3", "MELK3.SA_Quarter_4", "MELK3.SA_Last_Day_Quarter", "MELK3.SA_Pre_Feriado_Brasil", "MELK3.SA_Sinal_Compra", "MELK3.SA_Sinal_Venda", "MELK3.SA_Media_OHLC_Futura", "MELK3.SA_Volume_Lag_1", "MELK3.SA_Volume_Lag_2", "MELK3.SA_Volume_Lag_3", "MELK3.SA_Volume_Lag_4", "MELK3.SA_Volume_Lag_5", "MELK3.SA_Spread_Lag_1", "MELK3.SA_Spread_Lag_2", "MELK3.SA_Spread_Lag_3", "MELK3.SA_Spread_Lag_4", "MELK3.SA_Spread_Lag_5", "MELK3.SA_Volatilidade_Lag_1", "MELK3.SA_Volatilidade_Lag_2", "MELK3.SA_Volatilidade_Lag_3", "MELK3.SA_Volatilidade_Lag_4", "MELK3.SA_Volatilidade_Lag_5", "MELK3.SA_Parkinson_Volatility_Lag_1", "MELK3.SA_Parkinson_Volatility_Lag_2", "MELK3.SA_Parkinson_Volatility_Lag_3", "MELK3.SA_Parkinson_Volatility_Lag_4", "MELK3.<PERSON>_Parkinson_Volatility_Lag_5", "MELK3.SA_EMV_Lag_1", "MELK3.SA_EMV_Lag_2", "MELK3.SA_EMV_Lag_3", "MELK3.SA_EMV_Lag_4", "MELK3.SA_EMV_Lag_5", "MELK3.SA_EMV_MA_Lag_1", "MELK3.SA_EMV_MA_Lag_2", "MELK3.SA_EMV_MA_Lag_3", "MELK3.SA_EMV_MA_Lag_4", "MELK3.SA_EMV_MA_Lag_5", "MELK3.SA_VO_Lag_1", "MELK3.SA_VO_Lag_2", "MELK3.SA_VO_Lag_3", "MELK3.SA_VO_Lag_4", "MELK3.SA_VO_Lag_5", "MELK3.SA_MFI_Lag_1", "MELK3.SA_MFI_Lag_2", "MELK3.SA_MFI_Lag_3", "MELK3.SA_MFI_Lag_4", "MELK3.SA_MFI_Lag_5", "MELK3.SA_Amihud_Lag_1", "MELK3.SA_Amihud_Lag_2", "MELK3.SA_Amihud_Lag_3", "MELK3.SA_Amihud_Lag_4", "MELK3.SA_Amihud_Lag_5", "MELK3.SA_Roll_Spread_Lag_1", "MELK3.SA_Roll_Spread_Lag_2", "MELK3.SA_Roll_Spread_Lag_3", "MELK3.SA_Roll_Spread_Lag_4", "MELK3.SA_Roll_Spread_Lag_5", "MELK3.SA_Hurst_Lag_1", "MELK3.<PERSON>_Hu<PERSON>_Lag_2", "MELK3.SA_Hurst_Lag_3", "MELK3.<PERSON>_<PERSON><PERSON>_Lag_4", "MELK3.<PERSON>_<PERSON><PERSON>_Lag_5", "MELK3.SA_Vol_per_Volume_Lag_1", "MELK3.SA_Vol_per_Volume_Lag_2", "MELK3.SA_Vol_per_Volume_Lag_3", "MELK3.SA_Vol_per_Volume_Lag_4", "MELK3.SA_Vol_per_Volume_Lag_5", "MELK3.SA_CMF_Lag_1", "MELK3.SA_CMF_Lag_2", "MELK3.SA_CMF_Lag_3", "MELK3.SA_CMF_Lag_4", "MELK3.SA_CMF_Lag_5", "MELK3.SA_AD_Line_Lag_1", "MELK3.SA_AD_Line_Lag_2", "MELK3.SA_AD_Line_Lag_3", "MELK3.SA_AD_Line_Lag_4", "MELK3.SA_AD_Line_Lag_5", "PSSA3.SA_Media_OHLC", "PSSA3.SA_MFI_Historical", "PSSA3.SA_Amihud_Historical", "PSSA3.SA_Roll_Spread_Historical", "PSSA3.SA_Hurst_Historical", "PSSA3.SA_Vol_per_Volume_Historical", "PSSA3.SA_CMF_Historical", "PSSA3.SA_AD_Line_Historical", "PSSA3.SA_Segunda", "PSSA3.SA_Terca", "PSSA3.SA_Quarta", "PSSA3.SA_Quinta", "PSSA3.SA_Sexta", "PSSA3.SA_Mes_1", "PSSA3.SA_Mes_2", "PSSA3.SA_Mes_3", "PSSA3.SA_Mes_4", "PSSA3.SA_Mes_5", "PSSA3.SA_Mes_6", "PSSA3.SA_Mes_7", "PSSA3.SA_Mes_8", "PSSA3.SA_Mes_9", "PSSA3.SA_Mes_10", "PSSA3.SA_Mes_11", "PSSA3.SA_Mes_12", "PSSA3.SA_Quarter_1", "PSSA3.SA_Quarter_2", "PSSA3.SA_Quarter_3", "PSSA3.SA_Quarter_4", "PSSA3.SA_Last_Day_Quarter", "PSSA3.SA_Pre_Feriado_Brasil", "PSSA3.SA_Sinal_Compra", "PSSA3.SA_Sinal_Venda", "PSSA3.SA_Media_OHLC_Futura", "PSSA3.SA_Volume_Lag_1", "PSSA3.SA_Volume_Lag_2", "PSSA3.SA_Volume_Lag_3", "PSSA3.SA_Volume_Lag_4", "PSSA3.SA_Volume_Lag_5", "PSSA3.SA_Spread_Lag_1", "PSSA3.SA_Spread_Lag_2", "PSSA3.SA_Spread_Lag_3", "PSSA3.SA_Spread_Lag_4", "PSSA3.SA_Spread_Lag_5", "PSSA3.SA_Volatilidade_Lag_1", "PSSA3.SA_Volatilidade_Lag_2", "PSSA3.SA_Volatilidade_Lag_3", "PSSA3.SA_Volatilidade_Lag_4", "PSSA3.SA_Volatilidade_Lag_5", "PSSA3.SA_Parkinson_Volatility_Lag_1", "PSSA3.SA_Parkinson_Volatility_Lag_2", "PSSA3.SA_Parkinson_Volatility_Lag_3", "PSSA3.SA_Parkinson_Volatility_Lag_4", "PSSA3.SA_Parkinson_Volatility_Lag_5", "PSSA3.SA_EMV_Lag_1", "PSSA3.SA_EMV_Lag_2", "PSSA3.SA_EMV_Lag_3", "PSSA3.SA_EMV_Lag_4", "PSSA3.SA_EMV_Lag_5", "PSSA3.SA_EMV_MA_Lag_1", "PSSA3.SA_EMV_MA_Lag_2", "PSSA3.SA_EMV_MA_Lag_3", "PSSA3.SA_EMV_MA_Lag_4", "PSSA3.SA_EMV_MA_Lag_5", "PSSA3.SA_VO_Lag_1", "PSSA3.SA_VO_Lag_2", "PSSA3.SA_VO_Lag_3", "PSSA3.SA_VO_Lag_4", "PSSA3.SA_VO_Lag_5", "PSSA3.SA_MFI_Lag_1", "PSSA3.SA_MFI_Lag_2", "PSSA3.SA_MFI_Lag_3", "PSSA3.SA_MFI_Lag_4", "PSSA3.SA_MFI_Lag_5", "PSSA3.SA_Amihud_Lag_1", "PSSA3.SA_Amihud_Lag_2", "PSSA3.SA_Amihud_Lag_3", "PSSA3.SA_Amihud_Lag_4", "PSSA3.SA_Amihud_Lag_5", "PSSA3.SA_Roll_Spread_Lag_1", "PSSA3.SA_Roll_Spread_Lag_2", "PSSA3.SA_Roll_Spread_Lag_3", "PSSA3.SA_Roll_Spread_Lag_4", "PSSA3.SA_Roll_Spread_Lag_5", "PSSA3.SA_Hurst_Lag_1", "PSSA3.SA_Hurst_Lag_2", "PSSA3.SA_Hurst_Lag_3", "PSSA3.SA_Hurst_Lag_4", "PSSA3.SA_Hurst_Lag_5", "PSSA3.SA_Vol_per_Volume_Lag_1", "PSSA3.SA_Vol_per_Volume_Lag_2", "PSSA3.SA_Vol_per_Volume_Lag_3", "PSSA3.SA_Vol_per_Volume_Lag_4", "PSSA3.SA_Vol_per_Volume_Lag_5", "PSSA3.SA_CMF_Lag_1", "PSSA3.SA_CMF_Lag_2", "PSSA3.SA_CMF_Lag_3", "PSSA3.SA_CMF_Lag_4", "PSSA3.SA_CMF_Lag_5", "PSSA3.SA_AD_Line_Lag_1", "PSSA3.SA_AD_Line_Lag_2", "PSSA3.SA_AD_Line_Lag_3", "PSSA3.SA_AD_Line_Lag_4", "PSSA3.SA_AD_Line_Lag_5", "GRND3.SA_Media_OHLC", "GRND3.SA_MFI_Historical", "GRND3.SA_Amihud_Historical", "GRND3.SA_Roll_Spread_Historical", "GRND3.<PERSON>_<PERSON><PERSON>_Historical", "GRND3.SA_Vol_per_Volume_Historical", "GRND3.SA_CMF_Historical", "GRND3.SA_AD_Line_Historical", "GRND3.SA_Segunda", "GRND3.SA_Terca", "GRND3.SA_Quarta", "GRND3.SA_Quinta", "GRND3.SA_Sexta", "GRND3.SA_Mes_1", "GRND3.SA_Mes_2", "GRND3.SA_Mes_3", "GRND3.SA_Mes_4", "GRND3.SA_Mes_5", "GRND3.SA_Mes_6", "GRND3.SA_Mes_7", "GRND3.SA_Mes_8", "GRND3.SA_Mes_9", "GRND3.SA_Mes_10", "GRND3.SA_Mes_11", "GRND3.SA_Mes_12", "GRND3.SA_Quarter_1", "GRND3.SA_Quarter_2", "GRND3.SA_Quarter_3", "GRND3.SA_Quarter_4", "GRND3.SA_Last_Day_Quarter", "GRND3.SA_Pre_Feriado_Brasil", "GRND3.SA_Sinal_Compra", "GRND3.SA_Sinal_Venda", "GRND3.SA_Media_OHLC_Futura", "GRND3.SA_Volume_Lag_1", "GRND3.SA_Volume_Lag_2", "GRND3.SA_Volume_Lag_3", "GRND3.SA_Volume_Lag_4", "GRND3.SA_Volume_Lag_5", "GRND3.SA_Spread_Lag_1", "GRND3.SA_Spread_Lag_2", "GRND3.SA_Spread_Lag_3", "GRND3.SA_Spread_Lag_4", "GRND3.SA_Spread_Lag_5", "GRND3.SA_Volatilidade_Lag_1", "GRND3.SA_Volatilidade_Lag_2", "GRND3.SA_Volatilidade_Lag_3", "GRND3.SA_Volatilidade_Lag_4", "GRND3.SA_Volatilidade_Lag_5", "GRND3.SA_Parkinson_Volatility_Lag_1", "GRND3.<PERSON>_Parkinson_Volatility_Lag_2", "GRND3.SA_Parkinson_Volatility_Lag_3", "GRND3.SA_Parkinson_Volatility_Lag_4", "GRND3.<PERSON>_Parkinson_Volatility_Lag_5", "GRND3.SA_EMV_Lag_1", "GRND3.SA_EMV_Lag_2", "GRND3.SA_EMV_Lag_3", "GRND3.SA_EMV_Lag_4", "GRND3.SA_EMV_Lag_5", "GRND3.SA_EMV_MA_Lag_1", "GRND3.SA_EMV_MA_Lag_2", "GRND3.SA_EMV_MA_Lag_3", "GRND3.SA_EMV_MA_Lag_4", "GRND3.SA_EMV_MA_Lag_5", "GRND3.SA_VO_Lag_1", "GRND3.SA_VO_Lag_2", "GRND3.SA_VO_Lag_3", "GRND3.SA_VO_Lag_4", "GRND3.SA_VO_Lag_5", "GRND3.SA_MFI_Lag_1", "GRND3.SA_MFI_Lag_2", "GRND3.SA_MFI_Lag_3", "GRND3.SA_MFI_Lag_4", "GRND3.SA_MFI_Lag_5", "GRND3.SA_Amihud_Lag_1", "GRND3.SA_Amihud_Lag_2", "GRND3.SA_Amihud_Lag_3", "GRND3.SA_Amihud_Lag_4", "GRND3.SA_Amihud_Lag_5", "GRND3.SA_Roll_Spread_Lag_1", "GRND3.SA_Roll_Spread_Lag_2", "GRND3.SA_Roll_Spread_Lag_3", "GRND3.SA_Roll_Spread_Lag_4", "GRND3.SA_Roll_Spread_Lag_5", "GRND3.SA_Hurst_Lag_1", "GRND3.<PERSON>_<PERSON><PERSON>_Lag_2", "GRND3.<PERSON>_<PERSON><PERSON>_Lag_3", "GRND3.<PERSON>_<PERSON><PERSON>_Lag_4", "GRND3.<PERSON>_<PERSON><PERSON>_Lag_5", "GRND3.SA_Vol_per_Volume_Lag_1", "GRND3.SA_Vol_per_Volume_Lag_2", "GRND3.SA_Vol_per_Volume_Lag_3", "GRND3.SA_Vol_per_Volume_Lag_4", "GRND3.SA_Vol_per_Volume_Lag_5", "GRND3.SA_CMF_Lag_1", "GRND3.SA_CMF_Lag_2", "GRND3.SA_CMF_Lag_3", "GRND3.SA_CMF_Lag_4", "GRND3.SA_CMF_Lag_5", "GRND3.SA_AD_Line_Lag_1", "GRND3.SA_AD_Line_Lag_2", "GRND3.SA_AD_Line_Lag_3", "GRND3.SA_AD_Line_Lag_4", "GRND3.SA_AD_Line_Lag_5", "POMO4.SA_Media_OHLC", "POMO4.SA_MFI_Historical", "POMO4.SA_Amihud_Historical", "POMO4.SA_Roll_Spread_Historical", "POMO4.<PERSON>_<PERSON><PERSON>_Historical", "POMO4.SA_Vol_per_Volume_Historical", "POMO4.SA_CMF_Historical", "POMO4.SA_AD_Line_Historical", "POMO4.SA_Segunda", "POMO4.SA_Terca", "POMO4.SA_Quarta", "POMO4.SA_Quinta", "POMO4.SA_Sexta", "POMO4.SA_Mes_1", "POMO4.SA_Mes_2", "POMO4.SA_Mes_3", "POMO4.SA_Mes_4", "POMO4.SA_Mes_5", "POMO4.SA_Mes_6", "POMO4.SA_Mes_7", "POMO4.SA_Mes_8", "POMO4.SA_Mes_9", "POMO4.SA_Mes_10", "POMO4.SA_Mes_11", "POMO4.SA_Mes_12", "POMO4.SA_Quarter_1", "POMO4.SA_Quarter_2", "POMO4.SA_Quarter_3", "POMO4.SA_Quarter_4", "POMO4.SA_Last_Day_Quarter", "POMO4.SA_Pre_Feriado_Brasil", "POMO4.SA_Sinal_Compra", "POMO4.SA_Sinal_Venda", "POMO4.SA_Media_OHLC_Futura", "POMO4.SA_Volume_Lag_1", "POMO4.SA_Volume_Lag_2", "POMO4.SA_Volume_Lag_3", "POMO4.SA_Volume_Lag_4", "POMO4.SA_Volume_Lag_5", "POMO4.SA_Spread_Lag_1", "POMO4.SA_Spread_Lag_2", "POMO4.SA_Spread_Lag_3", "POMO4.SA_Spread_Lag_4", "POMO4.SA_Spread_Lag_5", "POMO4.SA_Volatilidade_Lag_1", "POMO4.SA_Volatilidade_Lag_2", "POMO4.SA_Volatilidade_Lag_3", "POMO4.SA_Volatilidade_Lag_4", "POMO4.SA_Volatilidade_Lag_5", "POMO4.SA_Parkinson_Volatility_Lag_1", "POMO4.<PERSON>_Parkinson_Volatility_Lag_2", "POMO4.SA_Parkinson_Volatility_Lag_3", "POMO4.SA_Parkinson_Volatility_Lag_4", "POMO4.<PERSON>_Parkinson_Volatility_Lag_5", "POMO4.SA_EMV_Lag_1", "POMO4.SA_EMV_Lag_2", "POMO4.SA_EMV_Lag_3", "POMO4.SA_EMV_Lag_4", "POMO4.SA_EMV_Lag_5", "POMO4.SA_EMV_MA_Lag_1", "POMO4.SA_EMV_MA_Lag_2", "POMO4.SA_EMV_MA_Lag_3", "POMO4.SA_EMV_MA_Lag_4", "POMO4.SA_EMV_MA_Lag_5", "POMO4.SA_VO_Lag_1", "POMO4.SA_VO_Lag_2", "POMO4.SA_VO_Lag_3", "POMO4.SA_VO_Lag_4", "POMO4.SA_VO_Lag_5", "POMO4.SA_MFI_Lag_1", "POMO4.SA_MFI_Lag_2", "POMO4.SA_MFI_Lag_3", "POMO4.SA_MFI_Lag_4", "POMO4.SA_MFI_Lag_5", "POMO4.SA_Amihud_Lag_1", "POMO4.SA_Amihud_Lag_2", "POMO4.SA_Amihud_Lag_3", "POMO4.SA_Amihud_Lag_4", "POMO4.SA_Amihud_Lag_5", "POMO4.SA_Roll_Spread_Lag_1", "POMO4.SA_Roll_Spread_Lag_2", "POMO4.SA_Roll_Spread_Lag_3", "POMO4.SA_Roll_Spread_Lag_4", "POMO4.SA_Roll_Spread_Lag_5", "POMO4.<PERSON>_Hu<PERSON>_Lag_1", "POMO4.<PERSON>_<PERSON><PERSON>_Lag_2", "POMO4.<PERSON>_<PERSON><PERSON>_Lag_3", "POMO4.<PERSON>_<PERSON><PERSON>_Lag_4", "POMO4.<PERSON>_<PERSON><PERSON>_Lag_5", "POMO4.SA_Vol_per_Volume_Lag_1", "POMO4.SA_Vol_per_Volume_Lag_2", "POMO4.SA_Vol_per_Volume_Lag_3", "POMO4.SA_Vol_per_Volume_Lag_4", "POMO4.SA_Vol_per_Volume_Lag_5", "POMO4.SA_CMF_Lag_1", "POMO4.SA_CMF_Lag_2", "POMO4.SA_CMF_Lag_3", "POMO4.SA_CMF_Lag_4", "POMO4.SA_CMF_Lag_5", "POMO4.SA_AD_Line_Lag_1", "POMO4.SA_AD_Line_Lag_2", "POMO4.SA_AD_Line_Lag_3", "POMO4.SA_AD_Line_Lag_4", "POMO4.SA_AD_Line_Lag_5", "BRSR6.SA_Media_OHLC", "BRSR6.SA_MFI_Historical", "BRSR6.SA_Amihud_Historical", "BRSR6.SA_Roll_Spread_Historical", "BRSR6.SA_Hurst_Historical", "BRSR6.SA_Vol_per_Volume_Historical", "BRSR6.SA_CMF_Historical", "BRSR6.SA_AD_Line_Historical", "BRSR6.SA_Segunda", "BRSR6.SA_Terca", "BRSR6.SA_Quarta", "BRSR6.SA_Quinta", "BRSR6.SA_Sexta", "BRSR6.SA_Mes_1", "BRSR6.SA_Mes_2", "BRSR6.SA_Mes_3", "BRSR6.SA_Mes_4", "BRSR6.SA_Mes_5", "BRSR6.SA_Mes_6", "BRSR6.SA_Mes_7", "BRSR6.SA_Mes_8", "BRSR6.SA_Mes_9", "BRSR6.SA_Mes_10", "BRSR6.SA_Mes_11", "BRSR6.SA_Mes_12", "BRSR6.SA_Quarter_1", "BRSR6.SA_Quarter_2", "BRSR6.SA_Quarter_3", "BRSR6.SA_Quarter_4", "BRSR6.SA_Last_Day_Quarter", "BRSR6.SA_Pre_Feriado_Brasil", "BRSR6.SA_Sinal_Compra", "BRSR6.SA_Sinal_Venda", "BRSR6.SA_Media_OHLC_Futura", "BRSR6.SA_Volume_Lag_1", "BRSR6.SA_Volume_Lag_2", "BRSR6.SA_Volume_Lag_3", "BRSR6.SA_Volume_Lag_4", "BRSR6.SA_Volume_Lag_5", "BRSR6.SA_Spread_Lag_1", "BRSR6.SA_Spread_Lag_2", "BRSR6.SA_Spread_Lag_3", "BRSR6.SA_Spread_Lag_4", "BRSR6.SA_Spread_Lag_5", "BRSR6.SA_Volatilidade_Lag_1", "BRSR6.SA_Volatilidade_Lag_2", "BRSR6.SA_Volatilidade_Lag_3", "BRSR6.SA_Volatilidade_Lag_4", "BRSR6.SA_Volatilidade_Lag_5", "BRSR6.SA_Parkinson_Volatility_Lag_1", "BRSR6.SA_Parkinson_Volatility_Lag_2", "BRSR6.SA_Parkinson_Volatility_Lag_3", "BRSR6.SA_Parkinson_Volatility_Lag_4", "BRSR6.SA_Parkinson_Volatility_Lag_5", "BRSR6.SA_EMV_Lag_1", "BRSR6.SA_EMV_Lag_2", "BRSR6.SA_EMV_Lag_3", "BRSR6.SA_EMV_Lag_4", "BRSR6.SA_EMV_Lag_5", "BRSR6.SA_EMV_MA_Lag_1", "BRSR6.SA_EMV_MA_Lag_2", "BRSR6.SA_EMV_MA_Lag_3", "BRSR6.SA_EMV_MA_Lag_4", "BRSR6.SA_EMV_MA_Lag_5", "BRSR6.SA_VO_Lag_1", "BRSR6.SA_VO_Lag_2", "BRSR6.SA_VO_Lag_3", "BRSR6.SA_VO_Lag_4", "BRSR6.SA_VO_Lag_5", "BRSR6.SA_MFI_Lag_1", "BRSR6.SA_MFI_Lag_2", "BRSR6.SA_MFI_Lag_3", "BRSR6.SA_MFI_Lag_4", "BRSR6.SA_MFI_Lag_5", "BRSR6.SA_Amihud_Lag_1", "BRSR6.SA_Amihud_Lag_2", "BRSR6.SA_Amihud_Lag_3", "BRSR6.SA_Amihud_Lag_4", "BRSR6.SA_Amihud_Lag_5", "BRSR6.SA_Roll_Spread_Lag_1", "BRSR6.SA_Roll_Spread_Lag_2", "BRSR6.SA_Roll_Spread_Lag_3", "BRSR6.SA_Roll_Spread_Lag_4", "BRSR6.SA_Roll_Spread_Lag_5", "BRSR6.SA_Hurst_Lag_1", "BRSR6.SA_Hurst_Lag_2", "BRSR6.SA_Hurst_Lag_3", "BRSR6.SA_Hurst_Lag_4", "BRSR6.SA_Hurst_Lag_5", "BRSR6.SA_Vol_per_Volume_Lag_1", "BRSR6.SA_Vol_per_Volume_Lag_2", "BRSR6.SA_Vol_per_Volume_Lag_3", "BRSR6.SA_Vol_per_Volume_Lag_4", "BRSR6.SA_Vol_per_Volume_Lag_5", "BRSR6.SA_CMF_Lag_1", "BRSR6.SA_CMF_Lag_2", "BRSR6.SA_CMF_Lag_3", "BRSR6.SA_CMF_Lag_4", "BRSR6.SA_CMF_Lag_5", "BRSR6.SA_AD_Line_Lag_1", "BRSR6.SA_AD_Line_Lag_2", "BRSR6.SA_AD_Line_Lag_3", "BRSR6.SA_AD_Line_Lag_4", "BRSR6.SA_AD_Line_Lag_5", "BBAS3.SA_Media_OHLC", "BBAS3.SA_MFI_Historical", "BBAS3.SA_Amihud_Historical", "BBAS3.SA_Roll_Spread_Historical", "BBAS3.SA_<PERSON><PERSON>_Historical", "BBAS3.SA_Vol_per_Volume_Historical", "BBAS3.SA_CMF_Historical", "BBAS3.SA_AD_Line_Historical", "BBAS3.SA_Segunda", "BBAS3.SA_Terca", "BBAS3.SA_Quarta", "BBAS3.SA_Quinta", "BBAS3.SA_Sexta", "BBAS3.SA_Mes_1", "BBAS3.SA_Mes_2", "BBAS3.SA_Mes_3", "BBAS3.SA_Mes_4", "BBAS3.SA_Mes_5", "BBAS3.SA_Mes_6", "BBAS3.SA_Mes_7", "BBAS3.SA_Mes_8", "BBAS3.SA_Mes_9", "BBAS3.SA_Mes_10", "BBAS3.SA_Mes_11", "BBAS3.SA_Mes_12", "BBAS3.SA_Quarter_1", "BBAS3.SA_Quarter_2", "BBAS3.SA_Quarter_3", "BBAS3.SA_Quarter_4", "BBAS3.SA_Last_Day_Quarter", "BBAS3.SA_Pre_Feriado_Brasil", "BBAS3.SA_Sinal_Compra", "BBAS3.SA_Sinal_Venda", "BBAS3.SA_Media_OHLC_Futura", "BBAS3.SA_Volume_Lag_1", "BBAS3.SA_Volume_Lag_2", "BBAS3.SA_Volume_Lag_3", "BBAS3.SA_Volume_Lag_4", "BBAS3.SA_Volume_Lag_5", "BBAS3.SA_Spread_Lag_1", "BBAS3.SA_Spread_Lag_2", "BBAS3.SA_Spread_Lag_3", "BBAS3.SA_Spread_Lag_4", "BBAS3.SA_Spread_Lag_5", "BBAS3.SA_Volatilidade_Lag_1", "BBAS3.SA_Volatilidade_Lag_2", "BBAS3.SA_Volatilidade_Lag_3", "BBAS3.SA_Volatilidade_Lag_4", "BBAS3.SA_Volatilidade_Lag_5", "BBAS3.SA_Parkinson_Volatility_Lag_1", "BBAS3.SA_Parkinson_Volatility_Lag_2", "BBAS3.SA_Parkinson_Volatility_Lag_3", "BBAS3.SA_Parkinson_Volatility_Lag_4", "BBAS3.SA_Parkinson_Volatility_Lag_5", "BBAS3.SA_EMV_Lag_1", "BBAS3.SA_EMV_Lag_2", "BBAS3.SA_EMV_Lag_3", "BBAS3.SA_EMV_Lag_4", "BBAS3.SA_EMV_Lag_5", "BBAS3.SA_EMV_MA_Lag_1", "BBAS3.SA_EMV_MA_Lag_2", "BBAS3.SA_EMV_MA_Lag_3", "BBAS3.SA_EMV_MA_Lag_4", "BBAS3.SA_EMV_MA_Lag_5", "BBAS3.SA_VO_Lag_1", "BBAS3.SA_VO_Lag_2", "BBAS3.SA_VO_Lag_3", "BBAS3.SA_VO_Lag_4", "BBAS3.SA_VO_Lag_5", "BBAS3.SA_MFI_Lag_1", "BBAS3.SA_MFI_Lag_2", "BBAS3.SA_MFI_Lag_3", "BBAS3.SA_MFI_Lag_4", "BBAS3.SA_MFI_Lag_5", "BBAS3.SA_Amihud_Lag_1", "BBAS3.SA_Amihud_Lag_2", "BBAS3.SA_Amihud_Lag_3", "BBAS3.SA_Amihud_Lag_4", "BBAS3.SA_Amihud_Lag_5", "BBAS3.SA_Roll_Spread_Lag_1", "BBAS3.SA_Roll_Spread_Lag_2", "BBAS3.SA_Roll_Spread_Lag_3", "BBAS3.SA_Roll_Spread_Lag_4", "BBAS3.SA_Roll_Spread_Lag_5", "BBAS3.SA_Hurst_Lag_1", "BBAS3.SA_<PERSON><PERSON>_Lag_2", "BBAS3.SA_<PERSON><PERSON>_Lag_3", "BBAS3.<PERSON>_<PERSON><PERSON>_Lag_4", "BBAS3.<PERSON>_<PERSON><PERSON>_Lag_5", "BBAS3.SA_Vol_per_Volume_Lag_1", "BBAS3.SA_Vol_per_Volume_Lag_2", "BBAS3.SA_Vol_per_Volume_Lag_3", "BBAS3.SA_Vol_per_Volume_Lag_4", "BBAS3.SA_Vol_per_Volume_Lag_5", "BBAS3.SA_CMF_Lag_1", "BBAS3.SA_CMF_Lag_2", "BBAS3.SA_CMF_Lag_3", "BBAS3.SA_CMF_Lag_4", "BBAS3.SA_CMF_Lag_5", "BBAS3.SA_AD_Line_Lag_1", "BBAS3.SA_AD_Line_Lag_2", "BBAS3.SA_AD_Line_Lag_3", "BBAS3.SA_AD_Line_Lag_4", "BBAS3.SA_AD_Line_Lag_5", "POMO3.SA_Media_OHLC", "POMO3.SA_MFI_Historical", "POMO3.SA_Amihud_Historical", "POMO3.SA_Roll_Spread_Historical", "POMO3.<PERSON>_<PERSON><PERSON>_Historical", "POMO3.SA_Vol_per_Volume_Historical", "POMO3.SA_CMF_Historical", "POMO3.SA_AD_Line_Historical", "POMO3.SA_Segunda", "POMO3.SA_Terca", "POMO3.SA_Quarta", "POMO3.SA_Quinta", "POMO3.SA_Sexta", "POMO3.SA_Mes_1", "POMO3.SA_Mes_2", "POMO3.SA_Mes_3", "POMO3.SA_Mes_4", "POMO3.SA_Mes_5", "POMO3.SA_Mes_6", "POMO3.SA_Mes_7", "POMO3.SA_Mes_8", "POMO3.SA_Mes_9", "POMO3.SA_Mes_10", "POMO3.SA_Mes_11", "POMO3.SA_Mes_12", "POMO3.SA_Quarter_1", "POMO3.SA_Quarter_2", "POMO3.SA_Quarter_3", "POMO3.SA_Quarter_4", "POMO3.SA_Last_Day_Quarter", "POMO3.SA_Pre_Feriado_Brasil", "POMO3.SA_Sinal_Compra", "POMO3.SA_Sinal_Venda", "POMO3.SA_Media_OHLC_Futura", "POMO3.SA_Volume_Lag_1", "POMO3.SA_Volume_Lag_2", "POMO3.SA_Volume_Lag_3", "POMO3.SA_Volume_Lag_4", "POMO3.SA_Volume_Lag_5", "POMO3.SA_Spread_Lag_1", "POMO3.SA_Spread_Lag_2", "POMO3.SA_Spread_Lag_3", "POMO3.SA_Spread_Lag_4", "POMO3.SA_Spread_Lag_5", "POMO3.SA_Volatilidade_Lag_1", "POMO3.SA_Volatilidade_Lag_2", "POMO3.SA_Volatilidade_Lag_3", "POMO3.SA_Volatilidade_Lag_4", "POMO3.SA_Volatilidade_Lag_5", "POMO3.SA_Parkinson_Volatility_Lag_1", "POMO3.<PERSON>_Parkinson_Volatility_Lag_2", "POMO3.SA_Parkinson_Volatility_Lag_3", "POMO3.SA_Parkinson_Volatility_Lag_4", "POMO3.<PERSON>_Parkinson_Volatility_Lag_5", "POMO3.SA_EMV_Lag_1", "POMO3.SA_EMV_Lag_2", "POMO3.SA_EMV_Lag_3", "POMO3.SA_EMV_Lag_4", "POMO3.SA_EMV_Lag_5", "POMO3.SA_EMV_MA_Lag_1", "POMO3.SA_EMV_MA_Lag_2", "POMO3.SA_EMV_MA_Lag_3", "POMO3.SA_EMV_MA_Lag_4", "POMO3.SA_EMV_MA_Lag_5", "POMO3.SA_VO_Lag_1", "POMO3.SA_VO_Lag_2", "POMO3.SA_VO_Lag_3", "POMO3.SA_VO_Lag_4", "POMO3.SA_VO_Lag_5", "POMO3.SA_MFI_Lag_1", "POMO3.SA_MFI_Lag_2", "POMO3.SA_MFI_Lag_3", "POMO3.SA_MFI_Lag_4", "POMO3.SA_MFI_Lag_5", "POMO3.SA_Amihud_Lag_1", "POMO3.SA_Amihud_Lag_2", "POMO3.SA_Amihud_Lag_3", "POMO3.SA_Amihud_Lag_4", "POMO3.SA_Amihud_Lag_5", "POMO3.SA_Roll_Spread_Lag_1", "POMO3.SA_Roll_Spread_Lag_2", "POMO3.SA_Roll_Spread_Lag_3", "POMO3.SA_Roll_Spread_Lag_4", "POMO3.SA_Roll_Spread_Lag_5", "POMO3.<PERSON>_Hurst_Lag_1", "POMO3.<PERSON>_<PERSON><PERSON>_Lag_2", "POMO3.<PERSON>_<PERSON><PERSON>_Lag_3", "POMO3.<PERSON>_<PERSON><PERSON>_Lag_4", "POMO3.<PERSON>_<PERSON><PERSON>_Lag_5", "POMO3.SA_Vol_per_Volume_Lag_1", "POMO3.SA_Vol_per_Volume_Lag_2", "POMO3.SA_Vol_per_Volume_Lag_3", "POMO3.SA_Vol_per_Volume_Lag_4", "POMO3.SA_Vol_per_Volume_Lag_5", "POMO3.SA_CMF_Lag_1", "POMO3.SA_CMF_Lag_2", "POMO3.SA_CMF_Lag_3", "POMO3.SA_CMF_Lag_4", "POMO3.SA_CMF_Lag_5", "POMO3.SA_AD_Line_Lag_1", "POMO3.SA_AD_Line_Lag_2", "POMO3.SA_AD_Line_Lag_3", "POMO3.SA_AD_Line_Lag_4", "POMO3.SA_AD_Line_Lag_5", "VIVT3.SA_Media_OHLC", "VIVT3.SA_MFI_Historical", "VIVT3.SA_Amihud_Historical", "VIVT3.SA_Roll_Spread_Historical", "VIVT3.<PERSON>_<PERSON><PERSON>_Historical", "VIVT3.SA_Vol_per_Volume_Historical", "VIVT3.SA_CMF_Historical", "VIVT3.SA_AD_Line_Historical", "VIVT3.SA_Segunda", "VIVT3.SA_Terca", "VIVT3.SA_Quarta", "VIVT3.SA_Quinta", "VIVT3.SA_Sexta", "VIVT3.SA_Mes_1", "VIVT3.SA_Mes_2", "VIVT3.SA_Mes_3", "VIVT3.SA_Mes_4", "VIVT3.SA_Mes_5", "VIVT3.SA_Mes_6", "VIVT3.SA_Mes_7", "VIVT3.SA_Mes_8", "VIVT3.SA_Mes_9", "VIVT3.SA_Mes_10", "VIVT3.SA_Mes_11", "VIVT3.SA_Mes_12", "VIVT3.SA_Quarter_1", "VIVT3.SA_Quarter_2", "VIVT3.SA_Quarter_3", "VIVT3.SA_Quarter_4", "VIVT3.SA_Last_Day_Quarter", "VIVT3.SA_Pre_Feriado_Brasil", "VIVT3.SA_Sinal_Compra", "VIVT3.SA_Sinal_Venda", "VIVT3.SA_Media_OHLC_Futura", "VIVT3.SA_Volume_Lag_1", "VIVT3.SA_Volume_Lag_2", "VIVT3.SA_Volume_Lag_3", "VIVT3.SA_Volume_Lag_4", "VIVT3.SA_Volume_Lag_5", "VIVT3.SA_Spread_Lag_1", "VIVT3.SA_Spread_Lag_2", "VIVT3.SA_Spread_Lag_3", "VIVT3.SA_Spread_Lag_4", "VIVT3.SA_Spread_Lag_5", "VIVT3.SA_Volatilidade_Lag_1", "VIVT3.SA_Volatilidade_Lag_2", "VIVT3.SA_Volatilidade_Lag_3", "VIVT3.SA_Volatilidade_Lag_4", "VIVT3.SA_Volatilidade_Lag_5", "VIVT3.SA_Parkinson_Volatility_Lag_1", "VIVT3.SA_Parkinson_Volatility_Lag_2", "VIVT3.SA_Parkinson_Volatility_Lag_3", "VIVT3.SA_Parkinson_Volatility_Lag_4", "VIVT3.<PERSON>_Parkinson_Volatility_Lag_5", "VIVT3.SA_EMV_Lag_1", "VIVT3.SA_EMV_Lag_2", "VIVT3.SA_EMV_Lag_3", "VIVT3.SA_EMV_Lag_4", "VIVT3.SA_EMV_Lag_5", "VIVT3.SA_EMV_MA_Lag_1", "VIVT3.SA_EMV_MA_Lag_2", "VIVT3.SA_EMV_MA_Lag_3", "VIVT3.SA_EMV_MA_Lag_4", "VIVT3.SA_EMV_MA_Lag_5", "VIVT3.SA_VO_Lag_1", "VIVT3.SA_VO_Lag_2", "VIVT3.SA_VO_Lag_3", "VIVT3.SA_VO_Lag_4", "VIVT3.SA_VO_Lag_5", "VIVT3.SA_MFI_Lag_1", "VIVT3.SA_MFI_Lag_2", "VIVT3.SA_MFI_Lag_3", "VIVT3.SA_MFI_Lag_4", "VIVT3.SA_MFI_Lag_5", "VIVT3.SA_Amihud_Lag_1", "VIVT3.SA_Amihud_Lag_2", "VIVT3.SA_Amihud_Lag_3", "VIVT3.SA_Amihud_Lag_4", "VIVT3.SA_Amihud_Lag_5", "VIVT3.SA_Roll_Spread_Lag_1", "VIVT3.SA_Roll_Spread_Lag_2", "VIVT3.SA_Roll_Spread_Lag_3", "VIVT3.SA_Roll_Spread_Lag_4", "VIVT3.SA_Roll_Spread_Lag_5", "VIVT3.<PERSON>_Hu<PERSON>_Lag_1", "VIVT3.<PERSON>_<PERSON><PERSON>_Lag_2", "VIVT3.<PERSON>_<PERSON><PERSON>_Lag_3", "VIVT3.<PERSON>_<PERSON><PERSON>_Lag_4", "VIVT3.<PERSON>_<PERSON><PERSON>_Lag_5", "VIVT3.SA_Vol_per_Volume_Lag_1", "VIVT3.SA_Vol_per_Volume_Lag_2", "VIVT3.SA_Vol_per_Volume_Lag_3", "VIVT3.SA_Vol_per_Volume_Lag_4", "VIVT3.SA_Vol_per_Volume_Lag_5", "VIVT3.SA_CMF_Lag_1", "VIVT3.SA_CMF_Lag_2", "VIVT3.SA_CMF_Lag_3", "VIVT3.SA_CMF_Lag_4", "VIVT3.SA_CMF_Lag_5", "VIVT3.SA_AD_Line_Lag_1", "VIVT3.SA_AD_Line_Lag_2", "VIVT3.SA_AD_Line_Lag_3", "VIVT3.SA_AD_Line_Lag_4", "VIVT3.SA_AD_Line_Lag_5", "RANI3.SA_Media_OHLC", "RANI3.SA_MFI_Historical", "RANI3.SA_Amihud_Historical", "RANI3.SA_Roll_Spread_Historical", "RANI3.SA_<PERSON><PERSON>_Historical", "RANI3.SA_Vol_per_Volume_Historical", "RANI3.SA_CMF_Historical", "RANI3.SA_AD_Line_Historical", "RANI3.SA_Segunda", "RANI3.SA_Terca", "RANI3.SA_Quarta", "RANI3.SA_Quinta", "RANI3.SA_Sexta", "RANI3.SA_Mes_1", "RANI3.SA_Mes_2", "RANI3.SA_Mes_3", "RANI3.SA_Mes_4", "RANI3.SA_Mes_5", "RANI3.SA_Mes_6", "RANI3.SA_Mes_7", "RANI3.SA_Mes_8", "RANI3.SA_Mes_9", "RANI3.SA_Mes_10", "RANI3.SA_Mes_11", "RANI3.SA_Mes_12", "RANI3.SA_Quarter_1", "RANI3.SA_Quarter_2", "RANI3.SA_Quarter_3", "RANI3.SA_Quarter_4", "RANI3.SA_Last_Day_Quarter", "RANI3.SA_Pre_Feriado_Brasil", "RANI3.SA_Sinal_Compra", "RANI3.SA_Sinal_Venda", "RANI3.SA_Media_OHLC_Futura", "RANI3.SA_Volume_Lag_1", "RANI3.SA_Volume_Lag_2", "RANI3.SA_Volume_Lag_3", "RANI3.SA_Volume_Lag_4", "RANI3.SA_Volume_Lag_5", "RANI3.SA_Spread_Lag_1", "RANI3.SA_Spread_Lag_2", "RANI3.SA_Spread_Lag_3", "RANI3.SA_Spread_Lag_4", "RANI3.SA_Spread_Lag_5", "RANI3.SA_Volatilidade_Lag_1", "RANI3.SA_Volatilidade_Lag_2", "RANI3.SA_Volatilidade_Lag_3", "RANI3.SA_Volatilidade_Lag_4", "RANI3.SA_Volatilidade_Lag_5", "RANI3.SA_Parkinson_Volatility_Lag_1", "RANI3.SA_Parkinson_Volatility_Lag_2", "RANI3.SA_Parkinson_Volatility_Lag_3", "RANI3.SA_Parkinson_Volatility_Lag_4", "RANI3.SA_Parkinson_Volatility_Lag_5", "RANI3.SA_EMV_Lag_1", "RANI3.SA_EMV_Lag_2", "RANI3.SA_EMV_Lag_3", "RANI3.SA_EMV_Lag_4", "RANI3.SA_EMV_Lag_5", "RANI3.SA_EMV_MA_Lag_1", "RANI3.SA_EMV_MA_Lag_2", "RANI3.SA_EMV_MA_Lag_3", "RANI3.SA_EMV_MA_Lag_4", "RANI3.SA_EMV_MA_Lag_5", "RANI3.SA_VO_Lag_1", "RANI3.SA_VO_Lag_2", "RANI3.SA_VO_Lag_3", "RANI3.SA_VO_Lag_4", "RANI3.SA_VO_Lag_5", "RANI3.SA_MFI_Lag_1", "RANI3.SA_MFI_Lag_2", "RANI3.SA_MFI_Lag_3", "RANI3.SA_MFI_Lag_4", "RANI3.SA_MFI_Lag_5", "RANI3.SA_Amihud_Lag_1", "RANI3.SA_Amihud_Lag_2", "RANI3.SA_Amihud_Lag_3", "RANI3.SA_Amihud_Lag_4", "RANI3.SA_Amihud_Lag_5", "RANI3.SA_Roll_Spread_Lag_1", "RANI3.SA_Roll_Spread_Lag_2", "RANI3.SA_Roll_Spread_Lag_3", "RANI3.SA_Roll_Spread_Lag_4", "RANI3.SA_Roll_Spread_Lag_5", "RANI3.SA_Hurst_Lag_1", "RANI3.SA_<PERSON><PERSON>_Lag_2", "RANI3.SA_<PERSON>rst_Lag_3", "RANI3.<PERSON>_<PERSON><PERSON>_Lag_4", "RANI3.<PERSON>_<PERSON><PERSON>_Lag_5", "RANI3.SA_Vol_per_Volume_Lag_1", "RANI3.SA_Vol_per_Volume_Lag_2", "RANI3.SA_Vol_per_Volume_Lag_3", "RANI3.SA_Vol_per_Volume_Lag_4", "RANI3.SA_Vol_per_Volume_Lag_5", "RANI3.SA_CMF_Lag_1", "RANI3.SA_CMF_Lag_2", "RANI3.SA_CMF_Lag_3", "RANI3.SA_CMF_Lag_4", "RANI3.SA_CMF_Lag_5", "RANI3.SA_AD_Line_Lag_1", "RANI3.SA_AD_Line_Lag_2", "RANI3.SA_AD_Line_Lag_3", "RANI3.SA_AD_Line_Lag_4", "RANI3.SA_AD_Line_Lag_5", "TAEE4.SA_Media_OHLC", "TAEE4.SA_MFI_Historical", "TAEE4.SA_Amihud_Historical", "TAEE4.SA_Roll_Spread_Historical", "TAEE4.<PERSON>_<PERSON><PERSON>_Historical", "TAEE4.SA_Vol_per_Volume_Historical", "TAEE4.SA_CMF_Historical", "TAEE4.SA_AD_Line_Historical", "TAEE4.SA_Segunda", "TAEE4.SA_Terca", "TAEE4.SA_Quarta", "TAEE4.SA_Quinta", "TAEE4.SA_Sexta", "TAEE4.SA_Mes_1", "TAEE4.SA_Mes_2", "TAEE4.SA_Mes_3", "TAEE4.SA_Mes_4", "TAEE4.SA_Mes_5", "TAEE4.SA_Mes_6", "TAEE4.SA_Mes_7", "TAEE4.SA_Mes_8", "TAEE4.SA_Mes_9", "TAEE4.SA_Mes_10", "TAEE4.SA_Mes_11", "TAEE4.SA_Mes_12", "TAEE4.SA_Quarter_1", "TAEE4.SA_Quarter_2", "TAEE4.SA_Quarter_3", "TAEE4.SA_Quarter_4", "TAEE4.SA_Last_Day_Quarter", "TAEE4.SA_Pre_Feriado_Brasil", "TAEE4.SA_Sinal_Compra", "TAEE4.SA_Sinal_Venda", "TAEE4.SA_Media_OHLC_Futura", "TAEE4.SA_Volume_Lag_1", "TAEE4.SA_Volume_Lag_2", "TAEE4.SA_Volume_Lag_3", "TAEE4.SA_Volume_Lag_4", "TAEE4.SA_Volume_Lag_5", "TAEE4.SA_Spread_Lag_1", "TAEE4.SA_Spread_Lag_2", "TAEE4.SA_Spread_Lag_3", "TAEE4.SA_Spread_Lag_4", "TAEE4.SA_Spread_Lag_5", "TAEE4.SA_Volatilidade_Lag_1", "TAEE4.SA_Volatilidade_Lag_2", "TAEE4.SA_Volatilidade_Lag_3", "TAEE4.SA_Volatilidade_Lag_4", "TAEE4.SA_Volatilidade_Lag_5", "TAEE4.SA_Parkinson_Volatility_Lag_1", "TAEE4.SA_Parkinson_Volatility_Lag_2", "TAEE4.SA_Parkinson_Volatility_Lag_3", "TAEE4.SA_Parkinson_Volatility_Lag_4", "TAEE4.SA_Parkinson_Volatility_Lag_5", "TAEE4.SA_EMV_Lag_1", "TAEE4.SA_EMV_Lag_2", "TAEE4.SA_EMV_Lag_3", "TAEE4.SA_EMV_Lag_4", "TAEE4.SA_EMV_Lag_5", "TAEE4.SA_EMV_MA_Lag_1", "TAEE4.SA_EMV_MA_Lag_2", "TAEE4.SA_EMV_MA_Lag_3", "TAEE4.SA_EMV_MA_Lag_4", "TAEE4.SA_EMV_MA_Lag_5", "TAEE4.SA_VO_Lag_1", "TAEE4.SA_VO_Lag_2", "TAEE4.SA_VO_Lag_3", "TAEE4.SA_VO_Lag_4", "TAEE4.SA_VO_Lag_5", "TAEE4.SA_MFI_Lag_1", "TAEE4.SA_MFI_Lag_2", "TAEE4.SA_MFI_Lag_3", "TAEE4.SA_MFI_Lag_4", "TAEE4.SA_MFI_Lag_5", "TAEE4.SA_Amihud_Lag_1", "TAEE4.SA_Amihud_Lag_2", "TAEE4.SA_Amihud_Lag_3", "TAEE4.SA_Amihud_Lag_4", "TAEE4.SA_Amihud_Lag_5", "TAEE4.SA_Roll_Spread_Lag_1", "TAEE4.SA_Roll_Spread_Lag_2", "TAEE4.SA_Roll_Spread_Lag_3", "TAEE4.SA_Roll_Spread_Lag_4", "TAEE4.SA_Roll_Spread_Lag_5", "TAEE4.<PERSON>_Hurst_Lag_1", "TAEE4.<PERSON>_<PERSON><PERSON>_Lag_2", "TAEE4.<PERSON>_<PERSON><PERSON>_Lag_3", "TAEE4.<PERSON>_<PERSON><PERSON>_Lag_4", "TAEE4.<PERSON>_<PERSON><PERSON>_Lag_5", "TAEE4.SA_Vol_per_Volume_Lag_1", "TAEE4.SA_Vol_per_Volume_Lag_2", "TAEE4.SA_Vol_per_Volume_Lag_3", "TAEE4.SA_Vol_per_Volume_Lag_4", "TAEE4.SA_Vol_per_Volume_Lag_5", "TAEE4.SA_CMF_Lag_1", "TAEE4.SA_CMF_Lag_2", "TAEE4.SA_CMF_Lag_3", "TAEE4.SA_CMF_Lag_4", "TAEE4.SA_CMF_Lag_5", "TAEE4.SA_AD_Line_Lag_1", "TAEE4.SA_AD_Line_Lag_2", "TAEE4.SA_AD_Line_Lag_3", "TAEE4.SA_AD_Line_Lag_4", "TAEE4.SA_AD_Line_Lag_5", "SHUL4.SA_Media_OHLC", "SHUL4.SA_MFI_Historical", "SHUL4.SA_Amihud_Historical", "SHUL4.SA_Roll_Spread_Historical", "SHUL4.SA_Hurst_Historical", "SHUL4.SA_Vol_per_Volume_Historical", "SHUL4.SA_CMF_Historical", "SHUL4.SA_AD_Line_Historical", "SHUL4.SA_Segunda", "SHUL4.SA_Terca", "SHUL4.SA_Quarta", "SHUL4.SA_Quinta", "SHUL4.SA_Sexta", "SHUL4.SA_Mes_1", "SHUL4.SA_Mes_2", "SHUL4.SA_Mes_3", "SHUL4.SA_Mes_4", "SHUL4.SA_Mes_5", "SHUL4.SA_Mes_6", "SHUL4.SA_Mes_7", "SHUL4.SA_Mes_8", "SHUL4.SA_Mes_9", "SHUL4.SA_Mes_10", "SHUL4.SA_Mes_11", "SHUL4.SA_Mes_12", "SHUL4.SA_Quarter_1", "SHUL4.SA_Quarter_2", "SHUL4.SA_Quarter_3", "SHUL4.SA_Quarter_4", "SHUL4.SA_Last_Day_Quarter", "SHUL4.SA_Pre_Feriado_Brasil", "SHUL4.SA_Sinal_Compra", "SHUL4.SA_Sinal_Venda", "SHUL4.SA_Media_OHLC_Futura", "SHUL4.SA_Volume_Lag_1", "SHUL4.SA_Volume_Lag_2", "SHUL4.SA_Volume_Lag_3", "SHUL4.SA_Volume_Lag_4", "SHUL4.SA_Volume_Lag_5", "SHUL4.SA_Spread_Lag_1", "SHUL4.SA_Spread_Lag_2", "SHUL4.SA_Spread_Lag_3", "SHUL4.SA_Spread_Lag_4", "SHUL4.SA_Spread_Lag_5", "SHUL4.SA_Volatilidade_Lag_1", "SHUL4.SA_Volatilidade_Lag_2", "SHUL4.SA_Volatilidade_Lag_3", "SHUL4.SA_Volatilidade_Lag_4", "SHUL4.SA_Volatilidade_Lag_5", "SHUL4.SA_Parkinson_Volatility_Lag_1", "SHUL4.<PERSON>_Parkinson_Volatility_Lag_2", "SHUL4.SA_Parkinson_Volatility_Lag_3", "SHUL4.SA_Parkinson_Volatility_Lag_4", "SHUL4.<PERSON>_Parkinson_Volatility_Lag_5", "SHUL4.SA_EMV_Lag_1", "SHUL4.SA_EMV_Lag_2", "SHUL4.SA_EMV_Lag_3", "SHUL4.SA_EMV_Lag_4", "SHUL4.SA_EMV_Lag_5", "SHUL4.SA_EMV_MA_Lag_1", "SHUL4.SA_EMV_MA_Lag_2", "SHUL4.SA_EMV_MA_Lag_3", "SHUL4.SA_EMV_MA_Lag_4", "SHUL4.SA_EMV_MA_Lag_5", "SHUL4.SA_VO_Lag_1", "SHUL4.SA_VO_Lag_2", "SHUL4.SA_VO_Lag_3", "SHUL4.SA_VO_Lag_4", "SHUL4.SA_VO_Lag_5", "SHUL4.SA_MFI_Lag_1", "SHUL4.SA_MFI_Lag_2", "SHUL4.SA_MFI_Lag_3", "SHUL4.SA_MFI_Lag_4", "SHUL4.SA_MFI_Lag_5", "SHUL4.SA_Amihud_Lag_1", "SHUL4.SA_Amihud_Lag_2", "SHUL4.SA_Amihud_Lag_3", "SHUL4.SA_Amihud_Lag_4", "SHUL4.SA_Amihud_Lag_5", "SHUL4.SA_Roll_Spread_Lag_1", "SHUL4.SA_Roll_Spread_Lag_2", "SHUL4.SA_Roll_Spread_Lag_3", "SHUL4.SA_Roll_Spread_Lag_4", "SHUL4.SA_Roll_Spread_Lag_5", "SHUL4.SA_Hurst_Lag_1", "SHUL4.<PERSON>_<PERSON><PERSON>_Lag_2", "SHUL4.<PERSON>_<PERSON>rst_Lag_3", "SHUL4.<PERSON>_<PERSON><PERSON>_Lag_4", "SHUL4.<PERSON>_<PERSON><PERSON>_Lag_5", "SHUL4.SA_Vol_per_Volume_Lag_1", "SHUL4.SA_Vol_per_Volume_Lag_2", "SHUL4.SA_Vol_per_Volume_Lag_3", "SHUL4.SA_Vol_per_Volume_Lag_4", "SHUL4.SA_Vol_per_Volume_Lag_5", "SHUL4.SA_CMF_Lag_1", "SHUL4.SA_CMF_Lag_2", "SHUL4.SA_CMF_Lag_3", "SHUL4.SA_CMF_Lag_4", "SHUL4.SA_CMF_Lag_5", "SHUL4.SA_AD_Line_Lag_1", "SHUL4.SA_AD_Line_Lag_2", "SHUL4.SA_AD_Line_Lag_3", "SHUL4.SA_AD_Line_Lag_4", "SHUL4.SA_AD_Line_Lag_5", "PNVL3.SA_Media_OHLC", "PNVL3.SA_MFI_Historical", "PNVL3.SA_Amihud_Historical", "PNVL3.SA_Roll_Spread_Historical", "PNVL3.SA_Hurst_Historical", "PNVL3.SA_Vol_per_Volume_Historical", "PNVL3.SA_CMF_Historical", "PNVL3.SA_AD_Line_Historical", "PNVL3.SA_Segunda", "PNVL3.SA_Terca", "PNVL3.SA_Quarta", "PNVL3.SA_Quinta", "PNVL3.SA_Sexta", "PNVL3.SA_Mes_1", "PNVL3.SA_Mes_2", "PNVL3.SA_Mes_3", "PNVL3.SA_Mes_4", "PNVL3.SA_Mes_5", "PNVL3.SA_Mes_6", "PNVL3.SA_Mes_7", "PNVL3.SA_Mes_8", "PNVL3.SA_Mes_9", "PNVL3.SA_Mes_10", "PNVL3.SA_Mes_11", "PNVL3.SA_Mes_12", "PNVL3.SA_Quarter_1", "PNVL3.SA_Quarter_2", "PNVL3.SA_Quarter_3", "PNVL3.SA_Quarter_4", "PNVL3.SA_Last_Day_Quarter", "PNVL3.SA_Pre_Feriado_Brasil", "PNVL3.SA_Sinal_Compra", "PNVL3.SA_Sinal_Venda", "PNVL3.SA_Media_OHLC_Futura", "PNVL3.SA_Volume_Lag_1", "PNVL3.SA_Volume_Lag_2", "PNVL3.SA_Volume_Lag_3", "PNVL3.SA_Volume_Lag_4", "PNVL3.SA_Volume_Lag_5", "PNVL3.SA_Spread_Lag_1", "PNVL3.SA_Spread_Lag_2", "PNVL3.SA_Spread_Lag_3", "PNVL3.SA_Spread_Lag_4", "PNVL3.SA_Spread_Lag_5", "PNVL3.SA_Volatilidade_Lag_1", "PNVL3.SA_Volatilidade_Lag_2", "PNVL3.SA_Volatilidade_Lag_3", "PNVL3.SA_Volatilidade_Lag_4", "PNVL3.SA_Volatilidade_Lag_5", "PNVL3.SA_Parkinson_Volatility_Lag_1", "PNVL3.SA_Parkinson_Volatility_Lag_2", "PNVL3.SA_Parkinson_Volatility_Lag_3", "PNVL3.SA_Parkinson_Volatility_Lag_4", "PNVL3.SA_Parkinson_Volatility_Lag_5", "PNVL3.SA_EMV_Lag_1", "PNVL3.SA_EMV_Lag_2", "PNVL3.SA_EMV_Lag_3", "PNVL3.SA_EMV_Lag_4", "PNVL3.SA_EMV_Lag_5", "PNVL3.SA_EMV_MA_Lag_1", "PNVL3.SA_EMV_MA_Lag_2", "PNVL3.SA_EMV_MA_Lag_3", "PNVL3.SA_EMV_MA_Lag_4", "PNVL3.SA_EMV_MA_Lag_5", "PNVL3.SA_VO_Lag_1", "PNVL3.SA_VO_Lag_2", "PNVL3.SA_VO_Lag_3", "PNVL3.SA_VO_Lag_4", "PNVL3.SA_VO_Lag_5", "PNVL3.SA_MFI_Lag_1", "PNVL3.SA_MFI_Lag_2", "PNVL3.SA_MFI_Lag_3", "PNVL3.SA_MFI_Lag_4", "PNVL3.SA_MFI_Lag_5", "PNVL3.SA_Amihud_Lag_1", "PNVL3.SA_Amihud_Lag_2", "PNVL3.SA_Amihud_Lag_3", "PNVL3.SA_Amihud_Lag_4", "PNVL3.SA_Amihud_Lag_5", "PNVL3.SA_Roll_Spread_Lag_1", "PNVL3.SA_Roll_Spread_Lag_2", "PNVL3.SA_Roll_Spread_Lag_3", "PNVL3.SA_Roll_Spread_Lag_4", "PNVL3.SA_Roll_Spread_Lag_5", "PNVL3.SA_Hurst_Lag_1", "PNVL3.SA_Hurst_Lag_2", "PNVL3.SA_Hurst_Lag_3", "PNVL3.SA_Hurst_Lag_4", "PNVL3.SA_<PERSON>rst_Lag_5", "PNVL3.SA_Vol_per_Volume_Lag_1", "PNVL3.SA_Vol_per_Volume_Lag_2", "PNVL3.SA_Vol_per_Volume_Lag_3", "PNVL3.SA_Vol_per_Volume_Lag_4", "PNVL3.SA_Vol_per_Volume_Lag_5", "PNVL3.SA_CMF_Lag_1", "PNVL3.SA_CMF_Lag_2", "PNVL3.SA_CMF_Lag_3", "PNVL3.SA_CMF_Lag_4", "PNVL3.SA_CMF_Lag_5", "PNVL3.SA_AD_Line_Lag_1", "PNVL3.SA_AD_Line_Lag_2", "PNVL3.SA_AD_Line_Lag_3", "PNVL3.SA_AD_Line_Lag_4", "PNVL3.SA_AD_Line_Lag_5", "CMIG4.SA_Media_OHLC", "CMIG4.SA_MFI_Historical", "CMIG4.SA_Amihud_Historical", "CMIG4.SA_Roll_Spread_Historical", "CMIG4.SA_<PERSON><PERSON>_Historical", "CMIG4.SA_Vol_per_Volume_Historical", "CMIG4.SA_CMF_Historical", "CMIG4.SA_AD_Line_Historical", "CMIG4.SA_Segunda", "CMIG4.SA_Terca", "CMIG4.SA_Quarta", "CMIG4.SA_Quinta", "CMIG4.SA_Sexta", "CMIG4.SA_Mes_1", "CMIG4.SA_Mes_2", "CMIG4.SA_Mes_3", "CMIG4.SA_Mes_4", "CMIG4.SA_Mes_5", "CMIG4.SA_Mes_6", "CMIG4.SA_Mes_7", "CMIG4.SA_Mes_8", "CMIG4.SA_Mes_9", "CMIG4.SA_Mes_10", "CMIG4.SA_Mes_11", "CMIG4.SA_Mes_12", "CMIG4.SA_Quarter_1", "CMIG4.SA_Quarter_2", "CMIG4.SA_Quarter_3", "CMIG4.SA_Quarter_4", "CMIG4.SA_Last_Day_Quarter", "CMIG4.SA_Pre_Feriado_Brasil", "CMIG4.SA_Sinal_Compra", "CMIG4.SA_Sinal_Venda", "CMIG4.SA_Media_OHLC_Futura", "CMIG4.SA_Volume_Lag_1", "CMIG4.SA_Volume_Lag_2", "CMIG4.SA_Volume_Lag_3", "CMIG4.SA_Volume_Lag_4", "CMIG4.SA_Volume_Lag_5", "CMIG4.SA_Spread_Lag_1", "CMIG4.SA_Spread_Lag_2", "CMIG4.SA_Spread_Lag_3", "CMIG4.SA_Spread_Lag_4", "CMIG4.SA_Spread_Lag_5", "CMIG4.SA_Volatilidade_Lag_1", "CMIG4.SA_Volatilidade_Lag_2", "CMIG4.SA_Volatilidade_Lag_3", "CMIG4.SA_Volatilidade_Lag_4", "CMIG4.SA_Volatilidade_Lag_5", "CMIG4.SA_Parkinson_Volatility_Lag_1", "CMIG4.SA_Parkinson_Volatility_Lag_2", "CMIG4.SA_Parkinson_Volatility_Lag_3", "CMIG4.SA_Parkinson_Volatility_Lag_4", "CMIG4.SA_Parkinson_Volatility_Lag_5", "CMIG4.SA_EMV_Lag_1", "CMIG4.SA_EMV_Lag_2", "CMIG4.SA_EMV_Lag_3", "CMIG4.SA_EMV_Lag_4", "CMIG4.SA_EMV_Lag_5", "CMIG4.SA_EMV_MA_Lag_1", "CMIG4.SA_EMV_MA_Lag_2", "CMIG4.SA_EMV_MA_Lag_3", "CMIG4.SA_EMV_MA_Lag_4", "CMIG4.SA_EMV_MA_Lag_5", "CMIG4.SA_VO_Lag_1", "CMIG4.SA_VO_Lag_2", "CMIG4.SA_VO_Lag_3", "CMIG4.SA_VO_Lag_4", "CMIG4.SA_VO_Lag_5", "CMIG4.SA_MFI_Lag_1", "CMIG4.SA_MFI_Lag_2", "CMIG4.SA_MFI_Lag_3", "CMIG4.SA_MFI_Lag_4", "CMIG4.SA_MFI_Lag_5", "CMIG4.SA_Amihud_Lag_1", "CMIG4.SA_Amihud_Lag_2", "CMIG4.SA_Amihud_Lag_3", "CMIG4.SA_Amihud_Lag_4", "CMIG4.SA_Amihud_Lag_5", "CMIG4.SA_Roll_Spread_Lag_1", "CMIG4.SA_Roll_Spread_Lag_2", "CMIG4.SA_Roll_Spread_Lag_3", "CMIG4.SA_Roll_Spread_Lag_4", "CMIG4.SA_Roll_Spread_Lag_5", "CMIG4.SA_Hurst_Lag_1", "CMIG4.<PERSON>_Hu<PERSON>_Lag_2", "CMIG4.<PERSON>_Hurst_Lag_3", "CMIG4.<PERSON>_Hu<PERSON>_Lag_4", "CMIG4.<PERSON>_<PERSON><PERSON>_Lag_5", "CMIG4.SA_Vol_per_Volume_Lag_1", "CMIG4.SA_Vol_per_Volume_Lag_2", "CMIG4.SA_Vol_per_Volume_Lag_3", "CMIG4.SA_Vol_per_Volume_Lag_4", "CMIG4.SA_Vol_per_Volume_Lag_5", "CMIG4.SA_CMF_Lag_1", "CMIG4.SA_CMF_Lag_2", "CMIG4.SA_CMF_Lag_3", "CMIG4.SA_CMF_Lag_4", "CMIG4.SA_CMF_Lag_5", "CMIG4.SA_AD_Line_Lag_1", "CMIG4.SA_AD_Line_Lag_2", "CMIG4.SA_AD_Line_Lag_3", "CMIG4.SA_AD_Line_Lag_4", "CMIG4.SA_AD_Line_Lag_5", "ALUP11.SA_Media_OHLC", "ALUP11.SA_MFI_Historical", "ALUP11.SA_Amihud_Historical", "ALUP11.SA_Roll_Spread_Historical", "ALUP11.<PERSON>_<PERSON><PERSON>_Historical", "ALUP11.SA_Vol_per_Volume_Historical", "ALUP11.SA_CMF_Historical", "ALUP11.SA_AD_Line_Historical", "ALUP11.SA_Segunda", "ALUP11.SA_Terca", "ALUP11.SA_Quarta", "ALUP11.SA_Quinta", "ALUP11.SA_Sexta", "ALUP11.SA_Mes_1", "ALUP11.SA_Mes_2", "ALUP11.SA_Mes_3", "ALUP11.SA_Mes_4", "ALUP11.SA_Mes_5", "ALUP11.SA_Mes_6", "ALUP11.SA_Mes_7", "ALUP11.SA_Mes_8", "ALUP11.SA_Mes_9", "ALUP11.SA_Mes_10", "ALUP11.SA_Mes_11", "ALUP11.SA_Mes_12", "ALUP11.SA_Quarter_1", "ALUP11.SA_Quarter_2", "ALUP11.SA_Quarter_3", "ALUP11.SA_Quarter_4", "ALUP11.SA_Last_Day_Quarter", "ALUP11.SA_Pre_Feriado_Brasil", "ALUP11.SA_Sinal_Compra", "ALUP11.SA_Sinal_Venda", "ALUP11.SA_Media_OHLC_Futura", "ALUP11.SA_Volume_Lag_1", "ALUP11.SA_Volume_Lag_2", "ALUP11.SA_Volume_Lag_3", "ALUP11.SA_Volume_Lag_4", "ALUP11.SA_Volume_Lag_5", "ALUP11.SA_Spread_Lag_1", "ALUP11.SA_Spread_Lag_2", "ALUP11.SA_Spread_Lag_3", "ALUP11.SA_Spread_Lag_4", "ALUP11.SA_Spread_Lag_5", "ALUP11.SA_Volatilidade_Lag_1", "ALUP11.SA_Volatilidade_Lag_2", "ALUP11.SA_Volatilidade_Lag_3", "ALUP11.SA_Volatilidade_Lag_4", "ALUP11.SA_Volatilidade_Lag_5", "ALUP11.<PERSON>_Parkinson_Volatility_Lag_1", "ALUP11.<PERSON>_Parkinson_Volatility_Lag_2", "ALUP11.<PERSON>_Parkinson_Volatility_Lag_3", "ALUP11.<PERSON>_Parkinson_Volatility_Lag_4", "ALUP11.<PERSON>_Parkinson_Volatility_Lag_5", "ALUP11.SA_EMV_Lag_1", "ALUP11.SA_EMV_Lag_2", "ALUP11.SA_EMV_Lag_3", "ALUP11.SA_EMV_Lag_4", "ALUP11.SA_EMV_Lag_5", "ALUP11.SA_EMV_MA_Lag_1", "ALUP11.SA_EMV_MA_Lag_2", "ALUP11.SA_EMV_MA_Lag_3", "ALUP11.SA_EMV_MA_Lag_4", "ALUP11.SA_EMV_MA_Lag_5", "ALUP11.SA_VO_Lag_1", "ALUP11.SA_VO_Lag_2", "ALUP11.SA_VO_Lag_3", "ALUP11.SA_VO_Lag_4", "ALUP11.SA_VO_Lag_5", "ALUP11.SA_MFI_Lag_1", "ALUP11.SA_MFI_Lag_2", "ALUP11.SA_MFI_Lag_3", "ALUP11.SA_MFI_Lag_4", "ALUP11.SA_MFI_Lag_5", "ALUP11.SA_Amihud_Lag_1", "ALUP11.SA_Amihud_Lag_2", "ALUP11.SA_Amihud_Lag_3", "ALUP11.SA_Amihud_Lag_4", "ALUP11.SA_Amihud_Lag_5", "ALUP11.SA_Roll_Spread_Lag_1", "ALUP11.SA_Roll_Spread_Lag_2", "ALUP11.SA_Roll_Spread_Lag_3", "ALUP11.SA_Roll_Spread_Lag_4", "ALUP11.SA_Roll_Spread_Lag_5", "ALUP11.<PERSON>_Hurst_Lag_1", "ALUP11.<PERSON>_Hu<PERSON>_Lag_2", "ALUP11.<PERSON>_Hurst_Lag_3", "ALUP11.<PERSON>_<PERSON><PERSON>_Lag_4", "ALUP11.<PERSON>_<PERSON><PERSON>_Lag_5", "ALUP11.SA_Vol_per_Volume_Lag_1", "ALUP11.SA_Vol_per_Volume_Lag_2", "ALUP11.SA_Vol_per_Volume_Lag_3", "ALUP11.SA_Vol_per_Volume_Lag_4", "ALUP11.SA_Vol_per_Volume_Lag_5", "ALUP11.SA_CMF_Lag_1", "ALUP11.SA_CMF_Lag_2", "ALUP11.SA_CMF_Lag_3", "ALUP11.SA_CMF_Lag_4", "ALUP11.SA_CMF_Lag_5", "ALUP11.SA_AD_Line_Lag_1", "ALUP11.SA_AD_Line_Lag_2", "ALUP11.SA_AD_Line_Lag_3", "ALUP11.SA_AD_Line_Lag_4", "ALUP11.SA_AD_Line_Lag_5", "ROMI3.SA_Media_OHLC", "ROMI3.SA_MFI_Historical", "ROMI3.SA_Amihud_Historical", "ROMI3.SA_Roll_Spread_Historical", "ROMI3.SA_<PERSON>rst_Historical", "ROMI3.SA_Vol_per_Volume_Historical", "ROMI3.SA_CMF_Historical", "ROMI3.SA_AD_Line_Historical", "ROMI3.SA_Segunda", "ROMI3.SA_Terca", "ROMI3.SA_Quarta", "ROMI3.SA_Quinta", "ROMI3.SA_Sexta", "ROMI3.SA_Mes_1", "ROMI3.SA_Mes_2", "ROMI3.SA_Mes_3", "ROMI3.SA_Mes_4", "ROMI3.SA_Mes_5", "ROMI3.SA_Mes_6", "ROMI3.SA_Mes_7", "ROMI3.SA_Mes_8", "ROMI3.SA_Mes_9", "ROMI3.SA_Mes_10", "ROMI3.SA_Mes_11", "ROMI3.SA_Mes_12", "ROMI3.SA_Quarter_1", "ROMI3.SA_Quarter_2", "ROMI3.SA_Quarter_3", "ROMI3.SA_Quarter_4", "ROMI3.SA_Last_Day_Quarter", "ROMI3.SA_Pre_Feriado_Brasil", "ROMI3.SA_Sinal_Compra", "ROMI3.SA_Sinal_Venda", "ROMI3.SA_Media_OHLC_Futura", "ROMI3.SA_Volume_Lag_1", "ROMI3.SA_Volume_Lag_2", "ROMI3.SA_Volume_Lag_3", "ROMI3.SA_Volume_Lag_4", "ROMI3.SA_Volume_Lag_5", "ROMI3.SA_Spread_Lag_1", "ROMI3.SA_Spread_Lag_2", "ROMI3.SA_Spread_Lag_3", "ROMI3.SA_Spread_Lag_4", "ROMI3.SA_Spread_Lag_5", "ROMI3.SA_Volatilidade_Lag_1", "ROMI3.SA_Volatilidade_Lag_2", "ROMI3.SA_Volatilidade_Lag_3", "ROMI3.SA_Volatilidade_Lag_4", "ROMI3.SA_Volatilidade_Lag_5", "ROMI3.SA_Parkinson_Volatility_Lag_1", "ROMI3.SA_Parkinson_Volatility_Lag_2", "ROMI3.SA_Parkinson_Volatility_Lag_3", "ROMI3.SA_Parkinson_Volatility_Lag_4", "ROMI3.SA_Parkinson_Volatility_Lag_5", "ROMI3.SA_EMV_Lag_1", "ROMI3.SA_EMV_Lag_2", "ROMI3.SA_EMV_Lag_3", "ROMI3.SA_EMV_Lag_4", "ROMI3.SA_EMV_Lag_5", "ROMI3.SA_EMV_MA_Lag_1", "ROMI3.SA_EMV_MA_Lag_2", "ROMI3.SA_EMV_MA_Lag_3", "ROMI3.SA_EMV_MA_Lag_4", "ROMI3.SA_EMV_MA_Lag_5", "ROMI3.SA_VO_Lag_1", "ROMI3.SA_VO_Lag_2", "ROMI3.SA_VO_Lag_3", "ROMI3.SA_VO_Lag_4", "ROMI3.SA_VO_Lag_5", "ROMI3.SA_MFI_Lag_1", "ROMI3.SA_MFI_Lag_2", "ROMI3.SA_MFI_Lag_3", "ROMI3.SA_MFI_Lag_4", "ROMI3.SA_MFI_Lag_5", "ROMI3.SA_Amihud_Lag_1", "ROMI3.SA_Amihud_Lag_2", "ROMI3.SA_Amihud_Lag_3", "ROMI3.SA_Amihud_Lag_4", "ROMI3.SA_Amihud_Lag_5", "ROMI3.SA_Roll_Spread_Lag_1", "ROMI3.SA_Roll_Spread_Lag_2", "ROMI3.SA_Roll_Spread_Lag_3", "ROMI3.SA_Roll_Spread_Lag_4", "ROMI3.SA_Roll_Spread_Lag_5", "ROMI3.SA_Hurst_Lag_1", "ROMI3.SA_Hurst_Lag_2", "ROMI3.SA_Hurst_Lag_3", "ROMI3.SA_Hurst_Lag_4", "ROMI3.<PERSON>_<PERSON><PERSON>_Lag_5", "ROMI3.SA_Vol_per_Volume_Lag_1", "ROMI3.SA_Vol_per_Volume_Lag_2", "ROMI3.SA_Vol_per_Volume_Lag_3", "ROMI3.SA_Vol_per_Volume_Lag_4", "ROMI3.SA_Vol_per_Volume_Lag_5", "ROMI3.SA_CMF_Lag_1", "ROMI3.SA_CMF_Lag_2", "ROMI3.SA_CMF_Lag_3", "ROMI3.SA_CMF_Lag_4", "ROMI3.SA_CMF_Lag_5", "ROMI3.SA_AD_Line_Lag_1", "ROMI3.SA_AD_Line_Lag_2", "ROMI3.SA_AD_Line_Lag_3", "ROMI3.SA_AD_Line_Lag_4", "ROMI3.SA_AD_Line_Lag_5", "VULC3.SA_Media_OHLC", "VULC3.SA_MFI_Historical", "VULC3.SA_Amihud_Historical", "VULC3.SA_Roll_Spread_Historical", "VULC3.SA_<PERSON><PERSON>_Historical", "VULC3.SA_Vol_per_Volume_Historical", "VULC3.SA_CMF_Historical", "VULC3.SA_AD_Line_Historical", "VULC3.SA_Segunda", "VULC3.SA_Terca", "VULC3.SA_Quarta", "VULC3.SA_Quinta", "VULC3.SA_Sexta", "VULC3.SA_Mes_1", "VULC3.SA_Mes_2", "VULC3.SA_Mes_3", "VULC3.SA_Mes_4", "VULC3.SA_Mes_5", "VULC3.SA_Mes_6", "VULC3.SA_Mes_7", "VULC3.SA_Mes_8", "VULC3.SA_Mes_9", "VULC3.SA_Mes_10", "VULC3.SA_Mes_11", "VULC3.SA_Mes_12", "VULC3.SA_Quarter_1", "VULC3.SA_Quarter_2", "VULC3.SA_Quarter_3", "VULC3.SA_Quarter_4", "VULC3.SA_Last_Day_Quarter", "VULC3.SA_Pre_Feriado_Brasil", "VULC3.SA_Sinal_Compra", "VULC3.SA_Sinal_Venda", "VULC3.SA_Media_OHLC_Futura", "VULC3.SA_Volume_Lag_1", "VULC3.SA_Volume_Lag_2", "VULC3.SA_Volume_Lag_3", "VULC3.SA_Volume_Lag_4", "VULC3.SA_Volume_Lag_5", "VULC3.SA_Spread_Lag_1", "VULC3.SA_Spread_Lag_2", "VULC3.SA_Spread_Lag_3", "VULC3.SA_Spread_Lag_4", "VULC3.SA_Spread_Lag_5", "VULC3.SA_Volatilidade_Lag_1", "VULC3.SA_Volatilidade_Lag_2", "VULC3.SA_Volatilidade_Lag_3", "VULC3.SA_Volatilidade_Lag_4", "VULC3.SA_Volatilidade_Lag_5", "VULC3.SA_Parkinson_Volatility_Lag_1", "VULC3.SA_Parkinson_Volatility_Lag_2", "VULC3.SA_Parkinson_Volatility_Lag_3", "VULC3.SA_Parkinson_Volatility_Lag_4", "VULC3.SA_Parkinson_Volatility_Lag_5", "VULC3.SA_EMV_Lag_1", "VULC3.SA_EMV_Lag_2", "VULC3.SA_EMV_Lag_3", "VULC3.SA_EMV_Lag_4", "VULC3.SA_EMV_Lag_5", "VULC3.SA_EMV_MA_Lag_1", "VULC3.SA_EMV_MA_Lag_2", "VULC3.SA_EMV_MA_Lag_3", "VULC3.SA_EMV_MA_Lag_4", "VULC3.SA_EMV_MA_Lag_5", "VULC3.SA_VO_Lag_1", "VULC3.SA_VO_Lag_2", "VULC3.SA_VO_Lag_3", "VULC3.SA_VO_Lag_4", "VULC3.SA_VO_Lag_5", "VULC3.SA_MFI_Lag_1", "VULC3.SA_MFI_Lag_2", "VULC3.SA_MFI_Lag_3", "VULC3.SA_MFI_Lag_4", "VULC3.SA_MFI_Lag_5", "VULC3.SA_Amihud_Lag_1", "VULC3.SA_Amihud_Lag_2", "VULC3.SA_Amihud_Lag_3", "VULC3.SA_Amihud_Lag_4", "VULC3.SA_Amihud_Lag_5", "VULC3.SA_Roll_Spread_Lag_1", "VULC3.SA_Roll_Spread_Lag_2", "VULC3.SA_Roll_Spread_Lag_3", "VULC3.SA_Roll_Spread_Lag_4", "VULC3.SA_Roll_Spread_Lag_5", "VULC3.SA_Hurst_Lag_1", "VULC3.<PERSON>_Hurst_Lag_2", "VULC3.SA_Hurst_Lag_3", "VULC3.<PERSON>_<PERSON>rst_Lag_4", "VULC3.<PERSON>_<PERSON><PERSON>_Lag_5", "VULC3.SA_Vol_per_Volume_Lag_1", "VULC3.SA_Vol_per_Volume_Lag_2", "VULC3.SA_Vol_per_Volume_Lag_3", "VULC3.SA_Vol_per_Volume_Lag_4", "VULC3.SA_Vol_per_Volume_Lag_5", "VULC3.SA_CMF_Lag_1", "VULC3.SA_CMF_Lag_2", "VULC3.SA_CMF_Lag_3", "VULC3.SA_CMF_Lag_4", "VULC3.SA_CMF_Lag_5", "VULC3.SA_AD_Line_Lag_1", "VULC3.SA_AD_Line_Lag_2", "VULC3.SA_AD_Line_Lag_3", "VULC3.SA_AD_Line_Lag_4", "VULC3.SA_AD_Line_Lag_5", "CAML3.SA_Media_OHLC", "CAML3.SA_MFI_Historical", "CAML3.SA_Amihud_Historical", "CAML3.SA_Roll_Spread_Historical", "CAML3.<PERSON>_<PERSON><PERSON>_Historical", "CAML3.SA_Vol_per_Volume_Historical", "CAML3.SA_CMF_Historical", "CAML3.SA_AD_Line_Historical", "CAML3.SA_Segunda", "CAML3.SA_Terca", "CAML3.SA_Quarta", "CAML3.SA_Quinta", "CAML3.SA_Sexta", "CAML3.SA_Mes_1", "CAML3.SA_Mes_2", "CAML3.SA_Mes_3", "CAML3.SA_Mes_4", "CAML3.SA_Mes_5", "CAML3.SA_Mes_6", "CAML3.SA_Mes_7", "CAML3.SA_Mes_8", "CAML3.SA_Mes_9", "CAML3.SA_Mes_10", "CAML3.SA_Mes_11", "CAML3.SA_Mes_12", "CAML3.SA_Quarter_1", "CAML3.SA_Quarter_2", "CAML3.SA_Quarter_3", "CAML3.SA_Quarter_4", "CAML3.SA_Last_Day_Quarter", "CAML3.SA_Pre_Feriado_Brasil", "CAML3.SA_Sinal_Compra", "CAML3.SA_Sinal_Venda", "CAML3.SA_Media_OHLC_Futura", "CAML3.SA_Volume_Lag_1", "CAML3.SA_Volume_Lag_2", "CAML3.SA_Volume_Lag_3", "CAML3.SA_Volume_Lag_4", "CAML3.SA_Volume_Lag_5", "CAML3.SA_Spread_Lag_1", "CAML3.SA_Spread_Lag_2", "CAML3.SA_Spread_Lag_3", "CAML3.SA_Spread_Lag_4", "CAML3.SA_Spread_Lag_5", "CAML3.SA_Volatilidade_Lag_1", "CAML3.SA_Volatilidade_Lag_2", "CAML3.SA_Volatilidade_Lag_3", "CAML3.SA_Volatilidade_Lag_4", "CAML3.SA_Volatilidade_Lag_5", "CAML3.SA_Parkinson_Volatility_Lag_1", "CAML3.SA_Parkinson_Volatility_Lag_2", "CAML3.SA_Parkinson_Volatility_Lag_3", "CAML3.SA_Parkinson_Volatility_Lag_4", "CAML3.SA_Parkinson_Volatility_Lag_5", "CAML3.SA_EMV_Lag_1", "CAML3.SA_EMV_Lag_2", "CAML3.SA_EMV_Lag_3", "CAML3.SA_EMV_Lag_4", "CAML3.SA_EMV_Lag_5", "CAML3.SA_EMV_MA_Lag_1", "CAML3.SA_EMV_MA_Lag_2", "CAML3.SA_EMV_MA_Lag_3", "CAML3.SA_EMV_MA_Lag_4", "CAML3.SA_EMV_MA_Lag_5", "CAML3.SA_VO_Lag_1", "CAML3.SA_VO_Lag_2", "CAML3.SA_VO_Lag_3", "CAML3.SA_VO_Lag_4", "CAML3.SA_VO_Lag_5", "CAML3.SA_MFI_Lag_1", "CAML3.SA_MFI_Lag_2", "CAML3.SA_MFI_Lag_3", "CAML3.SA_MFI_Lag_4", "CAML3.SA_MFI_Lag_5", "CAML3.SA_Amihud_Lag_1", "CAML3.SA_Amihud_Lag_2", "CAML3.SA_Amihud_Lag_3", "CAML3.SA_Amihud_Lag_4", "CAML3.SA_Amihud_Lag_5", "CAML3.SA_Roll_Spread_Lag_1", "CAML3.SA_Roll_Spread_Lag_2", "CAML3.SA_Roll_Spread_Lag_3", "CAML3.SA_Roll_Spread_Lag_4", "CAML3.SA_Roll_Spread_Lag_5", "CAML3.SA_Hurst_Lag_1", "CAML3.<PERSON>_<PERSON><PERSON>_Lag_2", "CAML3.SA_<PERSON>rst_Lag_3", "CAML3.<PERSON>_<PERSON><PERSON>_Lag_4", "CAML3.<PERSON>_<PERSON><PERSON>_Lag_5", "CAML3.SA_Vol_per_Volume_Lag_1", "CAML3.SA_Vol_per_Volume_Lag_2", "CAML3.SA_Vol_per_Volume_Lag_3", "CAML3.SA_Vol_per_Volume_Lag_4", "CAML3.SA_Vol_per_Volume_Lag_5", "CAML3.SA_CMF_Lag_1", "CAML3.SA_CMF_Lag_2", "CAML3.SA_CMF_Lag_3", "CAML3.SA_CMF_Lag_4", "CAML3.SA_CMF_Lag_5", "CAML3.SA_AD_Line_Lag_1", "CAML3.SA_AD_Line_Lag_2", "CAML3.SA_AD_Line_Lag_3", "CAML3.SA_AD_Line_Lag_4", "CAML3.SA_AD_Line_Lag_5", "RDOR3.SA_Media_OHLC", "RDOR3.SA_MFI_Historical", "RDOR3.SA_Amihud_Historical", "RDOR3.SA_Roll_Spread_Historical", "RDOR3.<PERSON>_<PERSON><PERSON>_Historical", "RDOR3.SA_Vol_per_Volume_Historical", "RDOR3.SA_CMF_Historical", "RDOR3.SA_AD_Line_Historical", "RDOR3.SA_Segunda", "RDOR3.SA_Terca", "RDOR3.SA_Quarta", "RDOR3.SA_Quinta", "RDOR3.SA_Sexta", "RDOR3.SA_Mes_1", "RDOR3.SA_Mes_2", "RDOR3.SA_Mes_3", "RDOR3.SA_Mes_4", "RDOR3.SA_Mes_5", "RDOR3.SA_Mes_6", "RDOR3.SA_Mes_7", "RDOR3.SA_Mes_8", "RDOR3.SA_Mes_9", "RDOR3.SA_Mes_10", "RDOR3.SA_Mes_11", "RDOR3.SA_Mes_12", "RDOR3.SA_Quarter_1", "RDOR3.SA_Quarter_2", "RDOR3.SA_Quarter_3", "RDOR3.SA_Quarter_4", "RDOR3.SA_Last_Day_Quarter", "RDOR3.SA_Pre_Feriado_Brasil", "RDOR3.SA_Sinal_Compra", "RDOR3.SA_Sinal_Venda", "RDOR3.SA_Media_OHLC_Futura", "RDOR3.SA_Volume_Lag_1", "RDOR3.SA_Volume_Lag_2", "RDOR3.SA_Volume_Lag_3", "RDOR3.SA_Volume_Lag_4", "RDOR3.SA_Volume_Lag_5", "RDOR3.SA_Spread_Lag_1", "RDOR3.SA_Spread_Lag_2", "RDOR3.SA_Spread_Lag_3", "RDOR3.SA_Spread_Lag_4", "RDOR3.SA_Spread_Lag_5", "RDOR3.SA_Volatilidade_Lag_1", "RDOR3.SA_Volatilidade_Lag_2", "RDOR3.SA_Volatilidade_Lag_3", "RDOR3.SA_Volatilidade_Lag_4", "RDOR3.SA_Volatilidade_Lag_5", "RDOR3.SA_Parkinson_Volatility_Lag_1", "RDOR3.<PERSON>_Parkinson_Volatility_Lag_2", "RDOR3.<PERSON>_Parkinson_Volatility_Lag_3", "RDOR3.<PERSON>_Parkinson_Volatility_Lag_4", "RDOR3.<PERSON>_Parkinson_Volatility_Lag_5", "RDOR3.SA_EMV_Lag_1", "RDOR3.SA_EMV_Lag_2", "RDOR3.SA_EMV_Lag_3", "RDOR3.SA_EMV_Lag_4", "RDOR3.SA_EMV_Lag_5", "RDOR3.SA_EMV_MA_Lag_1", "RDOR3.SA_EMV_MA_Lag_2", "RDOR3.SA_EMV_MA_Lag_3", "RDOR3.SA_EMV_MA_Lag_4", "RDOR3.SA_EMV_MA_Lag_5", "RDOR3.SA_VO_Lag_1", "RDOR3.SA_VO_Lag_2", "RDOR3.SA_VO_Lag_3", "RDOR3.SA_VO_Lag_4", "RDOR3.SA_VO_Lag_5", "RDOR3.SA_MFI_Lag_1", "RDOR3.SA_MFI_Lag_2", "RDOR3.SA_MFI_Lag_3", "RDOR3.SA_MFI_Lag_4", "RDOR3.SA_MFI_Lag_5", "RDOR3.SA_Amihud_Lag_1", "RDOR3.SA_Amihud_Lag_2", "RDOR3.SA_Amihud_Lag_3", "RDOR3.SA_Amihud_Lag_4", "RDOR3.SA_Amihud_Lag_5", "RDOR3.SA_Roll_Spread_Lag_1", "RDOR3.SA_Roll_Spread_Lag_2", "RDOR3.SA_Roll_Spread_Lag_3", "RDOR3.SA_Roll_Spread_Lag_4", "RDOR3.SA_Roll_Spread_Lag_5", "RDOR3.<PERSON>_Hurst_Lag_1", "RDOR3.<PERSON>_<PERSON><PERSON>_Lag_2", "RDOR3.<PERSON>_<PERSON><PERSON>_Lag_3", "RDOR3.<PERSON>_<PERSON><PERSON>_Lag_4", "RDOR3.<PERSON>_<PERSON><PERSON>_Lag_5", "RDOR3.SA_Vol_per_Volume_Lag_1", "RDOR3.SA_Vol_per_Volume_Lag_2", "RDOR3.SA_Vol_per_Volume_Lag_3", "RDOR3.SA_Vol_per_Volume_Lag_4", "RDOR3.SA_Vol_per_Volume_Lag_5", "RDOR3.SA_CMF_Lag_1", "RDOR3.SA_CMF_Lag_2", "RDOR3.SA_CMF_Lag_3", "RDOR3.SA_CMF_Lag_4", "RDOR3.SA_CMF_Lag_5", "RDOR3.SA_AD_Line_Lag_1", "RDOR3.SA_AD_Line_Lag_2", "RDOR3.SA_AD_Line_Lag_3", "RDOR3.SA_AD_Line_Lag_4", "RDOR3.SA_AD_Line_Lag_5", "EZTC3.SA_Media_OHLC", "EZTC3.SA_MFI_Historical", "EZTC3.SA_Amihud_Historical", "EZTC3.SA_Roll_Spread_Historical", "EZTC3.SA_<PERSON>rst_Historical", "EZTC3.SA_Vol_per_Volume_Historical", "EZTC3.SA_CMF_Historical", "EZTC3.SA_AD_Line_Historical", "EZTC3.SA_Segunda", "EZTC3.SA_Terca", "EZTC3.SA_Quarta", "EZTC3.SA_Quinta", "EZTC3.SA_Sexta", "EZTC3.SA_Mes_1", "EZTC3.SA_Mes_2", "EZTC3.SA_Mes_3", "EZTC3.SA_Mes_4", "EZTC3.SA_Mes_5", "EZTC3.SA_Mes_6", "EZTC3.SA_Mes_7", "EZTC3.SA_Mes_8", "EZTC3.SA_Mes_9", "EZTC3.SA_Mes_10", "EZTC3.SA_Mes_11", "EZTC3.SA_Mes_12", "EZTC3.SA_Quarter_1", "EZTC3.SA_Quarter_2", "EZTC3.SA_Quarter_3", "EZTC3.SA_Quarter_4", "EZTC3.SA_Last_Day_Quarter", "EZTC3.SA_Pre_Feriado_Brasil", "EZTC3.SA_Sinal_Compra", "EZTC3.SA_Sinal_Venda", "EZTC3.SA_Media_OHLC_Futura", "EZTC3.SA_Volume_Lag_1", "EZTC3.SA_Volume_Lag_2", "EZTC3.SA_Volume_Lag_3", "EZTC3.SA_Volume_Lag_4", "EZTC3.SA_Volume_Lag_5", "EZTC3.SA_Spread_Lag_1", "EZTC3.SA_Spread_Lag_2", "EZTC3.SA_Spread_Lag_3", "EZTC3.SA_Spread_Lag_4", "EZTC3.SA_Spread_Lag_5", "EZTC3.SA_Volatilidade_Lag_1", "EZTC3.SA_Volatilidade_Lag_2", "EZTC3.SA_Volatilidade_Lag_3", "EZTC3.SA_Volatilidade_Lag_4", "EZTC3.SA_Volatilidade_Lag_5", "EZTC3.SA_Parkinson_Volatility_Lag_1", "EZTC3.SA_Parkinson_Volatility_Lag_2", "EZTC3.SA_Parkinson_Volatility_Lag_3", "EZTC3.SA_Parkinson_Volatility_Lag_4", "EZTC3.SA_Parkinson_Volatility_Lag_5", "EZTC3.SA_EMV_Lag_1", "EZTC3.SA_EMV_Lag_2", "EZTC3.SA_EMV_Lag_3", "EZTC3.SA_EMV_Lag_4", "EZTC3.SA_EMV_Lag_5", "EZTC3.SA_EMV_MA_Lag_1", "EZTC3.SA_EMV_MA_Lag_2", "EZTC3.SA_EMV_MA_Lag_3", "EZTC3.SA_EMV_MA_Lag_4", "EZTC3.SA_EMV_MA_Lag_5", "EZTC3.SA_VO_Lag_1", "EZTC3.SA_VO_Lag_2", "EZTC3.SA_VO_Lag_3", "EZTC3.SA_VO_Lag_4", "EZTC3.SA_VO_Lag_5", "EZTC3.SA_MFI_Lag_1", "EZTC3.SA_MFI_Lag_2", "EZTC3.SA_MFI_Lag_3", "EZTC3.SA_MFI_Lag_4", "EZTC3.SA_MFI_Lag_5", "EZTC3.SA_Amihud_Lag_1", "EZTC3.SA_Amihud_Lag_2", "EZTC3.SA_Amihud_Lag_3", "EZTC3.SA_Amihud_Lag_4", "EZTC3.SA_Amihud_Lag_5", "EZTC3.SA_Roll_Spread_Lag_1", "EZTC3.SA_Roll_Spread_Lag_2", "EZTC3.SA_Roll_Spread_Lag_3", "EZTC3.SA_Roll_Spread_Lag_4", "EZTC3.SA_Roll_Spread_Lag_5", "EZTC3.SA_Hurst_Lag_1", "EZTC3.SA_Hurst_Lag_2", "EZTC3.SA_Hurst_Lag_3", "EZTC3.SA_<PERSON>rst_Lag_4", "EZTC3.<PERSON>_<PERSON><PERSON>_Lag_5", "EZTC3.SA_Vol_per_Volume_Lag_1", "EZTC3.SA_Vol_per_Volume_Lag_2", "EZTC3.SA_Vol_per_Volume_Lag_3", "EZTC3.SA_Vol_per_Volume_Lag_4", "EZTC3.SA_Vol_per_Volume_Lag_5", "EZTC3.SA_CMF_Lag_1", "EZTC3.SA_CMF_Lag_2", "EZTC3.SA_CMF_Lag_3", "EZTC3.SA_CMF_Lag_4", "EZTC3.SA_CMF_Lag_5", "EZTC3.SA_AD_Line_Lag_1", "EZTC3.SA_AD_Line_Lag_2", "EZTC3.SA_AD_Line_Lag_3", "EZTC3.SA_AD_Line_Lag_4", "EZTC3.SA_AD_Line_Lag_5"]}